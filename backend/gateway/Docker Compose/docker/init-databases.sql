-- Initialize auth_db database with all required tables
-- This replaces Flyway migrations with Docker-based initialization

-- Connect to auth_db database
\c auth_db;

-- Create Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);



-- Create Servers table
CREATE TABLE IF NOT EXISTS servers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    database_type VARCHAR(50) NOT NULL,
    username VARCHAR(100),
    password VARCHAR(255),
    description TEXT,
    replica_type VARCHAR(20), -- MSSQL Always On Availability Groups için replica tipi (PRIMARY, SECONDARY, READ_ONLY)
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create Metric Queries table (metrik tipi kaldırıldı, display fields'a taşındı)
CREATE TABLE IF NOT EXISTS metric_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_name VARCHAR(100) NOT NULL UNIQUE,
    query_description VARCHAR(500),
    database_type VARCHAR(50) NOT NULL,
    sql_query TEXT NOT NULL,
    metric_category VARCHAR(50), -- CPU, RAM, DISK, CONNECTION, PERFORMANCE, etc.
    is_active BOOLEAN NOT NULL DEFAULT true,
    execution_order INTEGER DEFAULT 0,
    display_mapping JSONB,
    data_type VARCHAR(50) DEFAULT 'snapshot',
    default_time_range VARCHAR(10) DEFAULT '1h',
    supports_time_range BOOLEAN NOT NULL DEFAULT false,
    numeric_field_name VARCHAR(100),
    time_aggregation_minutes INTEGER DEFAULT 60,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create Metric Display Fields table
CREATE TABLE IF NOT EXISTS metric_display_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_query_id UUID NOT NULL REFERENCES metric_queries(id) ON DELETE CASCADE,
    column_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    data_type VARCHAR(20) NOT NULL, -- string, int
    display_order INTEGER DEFAULT 0,

    -- Metrik türü (LATEST, AVERAGE, RANGE)
    metric_type VARCHAR(255) DEFAULT 'LATEST',

    -- Bileşen türü (NUMBER_CARD, TREND_CARD, LINE_CHART, BAR_CHART, TABLE, etc.)
    component_type VARCHAR(50) NOT NULL DEFAULT 'NUMBER_CARD',

    -- Bileşen konfigürasyonu (JSON format)
    component_config JSONB DEFAULT '{}',

    -- Açıklama
    description VARCHAR(500),

    -- Aktif durumu
    is_active BOOLEAN NOT NULL DEFAULT true,

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_display_field_data_type CHECK (data_type IN ('string', 'int', 'STRING', 'INT')),
    CONSTRAINT chk_metric_type CHECK (metric_type IN ('LATEST', 'AVERAGE', 'RANGE')),
    CONSTRAINT chk_component_type CHECK (component_type IN (
        'NUMBER_CARD', 'STATUS_CARD', 'PROGRESS_BAR', 'BADGE', 'ICON_NUMBER',
        'TREND_CARD', 'LINE_CHART', 'BAR_CHART', 'AREA_CHART', 'TABLE',
        'STATISTICS_CARD', 'COMPARISON_CARD'
    )),
    CONSTRAINT uk_metric_query_column UNIQUE (metric_query_id, column_name)
);

-- Create Metric Results table
CREATE TABLE IF NOT EXISTS metric_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_id UUID NOT NULL,
    metric_query_id UUID,
    metric_name VARCHAR(255) NOT NULL,
    sql_query TEXT NOT NULL,
    result_data JSONB,
    row_count BIGINT,
    execution_time_ms BIGINT NOT NULL,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    error_code VARCHAR(50),
    recorded_at BIGINT NOT NULL -- Unix timestamp in milliseconds
);

-- Create App Admins table (for JWT authentication)
-- Bu tablo uygulama yöneticileri için kullanılır
-- Veritabanı kullanıcıları (users tablosu) ile karışmayacak şekilde tasarlandı
CREATE TABLE IF NOT EXISTS app_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);

-- Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_metric_result_server_id ON metric_results(server_id);
CREATE INDEX IF NOT EXISTS idx_metric_result_metric_query_id ON metric_results(metric_query_id);
CREATE INDEX IF NOT EXISTS idx_metric_result_recorded_at ON metric_results(recorded_at);
CREATE INDEX IF NOT EXISTS idx_metric_result_server_recorded ON metric_results(server_id, recorded_at);
CREATE INDEX IF NOT EXISTS idx_metric_result_success ON metric_results(success);

-- Metric Queries indexes for new structure
CREATE INDEX IF NOT EXISTS idx_metric_queries_metric_category ON metric_queries(metric_category);
CREATE INDEX IF NOT EXISTS idx_metric_queries_database_type ON metric_queries(database_type);
CREATE INDEX IF NOT EXISTS idx_metric_queries_database_type_active ON metric_queries(database_type, is_active);

-- Metric Display Fields indexes
CREATE INDEX IF NOT EXISTS idx_metric_display_fields_query_id ON metric_display_fields(metric_query_id);
CREATE INDEX IF NOT EXISTS idx_metric_display_fields_data_type ON metric_display_fields(data_type);
CREATE INDEX IF NOT EXISTS idx_metric_display_fields_metric_type ON metric_display_fields(metric_type);
CREATE INDEX IF NOT EXISTS idx_metric_display_fields_component_type ON metric_display_fields(component_type);
CREATE INDEX IF NOT EXISTS idx_metric_display_fields_active ON metric_display_fields(is_active);

-- Create indexes for app_admins table
CREATE INDEX IF NOT EXISTS idx_app_admins_email ON app_admins(email);
CREATE INDEX IF NOT EXISTS idx_app_admins_active ON app_admins(is_active);
CREATE INDEX IF NOT EXISTS idx_app_admins_created_at ON app_admins(created_at);

-- Insert some initial data
INSERT INTO users (first_name, last_name, username, email) VALUES
('Admin', 'User', 'admin', '<EMAIL>')
ON CONFLICT (username) DO NOTHING;



-- Insert initial app admin (email: <EMAIL>, password: admin123)
-- BCrypt hash for "admin123": $2a$10$PKdDDhhL.TQNaEYZ/oBcSOjp0fTtIpgxiogEUasiGKZF86v66Wj5y
INSERT INTO app_admins (email, password, first_name, last_name, is_active)
VALUES ('<EMAIL>', '$2a$10$PKdDDhhL.TQNaEYZ/oBcSOjp0fTtIpgxiogEUasiGKZF86v66Wj5y', 'System', 'Admin', true)
ON CONFLICT (email) DO NOTHING;

-- Insert sample metric queries
INSERT INTO metric_queries (query_name, query_description, database_type, sql_query, metric_category, execution_order) VALUES

-- Sample query 1: Active connections
('Current Active Connections', 'Mevcut aktif bağlantı sayısı', 'POSTGRESQL',
'SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = ''active''',
'CONNECTION', 1),

-- Sample query 2: Connection states
('Connection States', 'Bağlantı durumları', 'POSTGRESQL',
'SELECT state, count(*) as connection_count FROM pg_stat_activity WHERE state IS NOT NULL GROUP BY state',
'CONNECTION', 2)

ON CONFLICT (query_name) DO NOTHING;

-- Insert sample display fields for the queries
INSERT INTO metric_display_fields (metric_query_id, column_name, display_name, data_type, display_order, metric_type, component_type, component_config, description) VALUES

-- Fields for Current Active Connections query
((SELECT id FROM metric_queries WHERE query_name = 'Current Active Connections'),
 'active_connections', 'Aktif Bağlantılar', 'int', 1, 'LATEST', 'NUMBER_CARD',
 '{"color": "blue", "unit": "adet", "showTrend": true}', 'Anlık aktif bağlantı sayısını gösterir'),

-- Fields for Connection States query
((SELECT id FROM metric_queries WHERE query_name = 'Connection States'),
 'state', 'Bağlantı Durumu', 'string', 1, 'LATEST', 'STATUS_CARD',
 '{"colorMapping": {"active": "green", "idle": "yellow", "waiting": "orange"}}', 'Bağlantı durumunu renkli olarak gösterir'),
((SELECT id FROM metric_queries WHERE query_name = 'Connection States'),
 'connection_count', 'Bağlantı Sayısı', 'int', 2, 'RANGE', 'BAR_CHART',
 '{"chartType": "vertical", "color": "purple", "timeRange": "1h"}', 'Bağlantı sayısının zaman içindeki değişimini gösterir')

ON CONFLICT (metric_query_id, column_name) DO NOTHING;

-- Add comments for columns
COMMENT ON COLUMN metric_queries.metric_category IS 'Category of metric: CPU, MEMORY, DISK, CONNECTION, PERFORMANCE, etc.';
COMMENT ON COLUMN metric_display_fields.data_type IS 'Data type: string, int';
COMMENT ON COLUMN metric_display_fields.column_name IS 'Column name from SQL query result';
COMMENT ON COLUMN metric_display_fields.display_name IS 'Human readable name for display';
COMMENT ON COLUMN metric_display_fields.metric_type IS 'Metric type: LATEST (current value), AVERAGE (time range average), RANGE (time range data)';
COMMENT ON COLUMN metric_display_fields.component_type IS 'UI component type: NUMBER_CARD, STATUS_CARD, PROGRESS_BAR, BADGE, ICON_NUMBER, TREND_CARD, LINE_CHART, BAR_CHART, AREA_CHART, TABLE, STATISTICS_CARD, COMPARISON_CARD';
COMMENT ON COLUMN metric_display_fields.component_config IS 'JSON configuration for the UI component (colors, units, chart settings, etc.)';

-- Create User Permissions table
CREATE TABLE IF NOT EXISTS user_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    server_id UUID NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    granted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    sql_query TEXT, -- SQL query executed to grant the permission
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP, -- Soft delete timestamp

    -- Foreign key constraints (optional, depends on your architecture)
    -- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    -- FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,

    -- Indexes for performance
    CONSTRAINT idx_user_permissions_user_id_index UNIQUE (user_id, server_id, role_name, is_active)
);

-- Create indexes for user_permissions table
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_server_id ON user_permissions(server_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_role_name ON user_permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_user_permissions_expires_at ON user_permissions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_permissions_is_active ON user_permissions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_server_role ON user_permissions(user_id, server_id, role_name);

-- Add comments for user_permissions table
COMMENT ON TABLE user_permissions IS 'User database permissions and roles';
COMMENT ON COLUMN user_permissions.user_id IS 'Reference to user who has the permission';
COMMENT ON COLUMN user_permissions.server_id IS 'Reference to database server';
COMMENT ON COLUMN user_permissions.role_name IS 'Database role name (from pg_roles or equivalent)';
COMMENT ON COLUMN user_permissions.granted_at IS 'When the permission was granted';
COMMENT ON COLUMN user_permissions.expires_at IS 'When the permission expires';
COMMENT ON COLUMN user_permissions.is_active IS 'Whether the permission is currently active';
COMMENT ON COLUMN user_permissions.sql_query IS 'SQL query executed to grant the permission';
COMMENT ON COLUMN user_permissions.deleted_at IS 'Soft delete timestamp - when the permission was deleted';

-- Create trigger function for updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for app_admins table
CREATE TRIGGER update_app_admins_updated_at
    BEFORE UPDATE ON app_admins
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for user_permissions table
CREATE TRIGGER update_user_permissions_updated_at
    BEFORE UPDATE ON user_permissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create test database by copying the main database structure
-- This creates auth_db_test with the same schema and data as auth_db
CREATE DATABASE auth_db_test WITH TEMPLATE auth_db;
