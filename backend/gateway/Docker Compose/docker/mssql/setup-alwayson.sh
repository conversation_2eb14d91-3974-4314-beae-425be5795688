#!/bin/bash

# MSSQL Always On Availability Groups Setup Script
# Bu script container'lar ba<PERSON><PERSON><PERSON>ktan sonra Always On yapılandırmasını tamamlar

echo "Setting up MSSQL Always On Availability Groups..."

# Primary replica'da endpoint ve AG oluştur
echo "Configuring primary replica..."
docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);
"

# Secondary replica'da endpoint oluştur
echo "Configuring secondary replica..."
docker exec mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);
"

# Primary'dan backup'ı secondary'ye kopyala
echo "Copying database backup to secondary..."
docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
BACKUP DATABASE TestDB TO DISK = '/var/opt/mssql/shared/TestDB.bak';
"

# Secondary'de backup'ı restore et
echo "Restoring database on secondary..."
docker exec mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
RESTORE DATABASE TestDB FROM DISK = '/var/opt/mssql/shared/TestDB.bak' WITH NORECOVERY;
"

# Primary'da Availability Group oluştur
echo "Creating Availability Group on primary..."
docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
CREATE AVAILABILITY GROUP TestAG
    WITH (AUTOMATED_BACKUP_PREFERENCE = PRIMARY)
    FOR DATABASE TestDB
    REPLICA ON
        'mssql-primary' WITH (
            ENDPOINT_URL = 'TCP://mssql-primary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        ),
        'mssql-secondary' WITH (
            ENDPOINT_URL = 'TCP://mssql-secondary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        );
"

# Secondary'yi AG'ye katıl
echo "Joining secondary to Availability Group..."
docker exec mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
ALTER AVAILABILITY GROUP TestAG JOIN;
ALTER DATABASE TestDB SET HADR AVAILABILITY GROUP = TestAG;
"

echo "MSSQL Always On Availability Groups setup completed!"
echo ""
echo "Connection Information:"
echo "Primary Replica: localhost:1433 (Read/Write)"
echo "Secondary Replica: localhost:1434 (Read-Only)"
echo "Username: sa"
echo "Password: YourStrong@Passw0rd"
echo ""
echo "Test the setup with:"
echo "docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q 'SELECT @@SERVERNAME, role_desc FROM sys.dm_hadr_availability_replica_states ars INNER JOIN sys.availability_replicas ar ON ars.replica_id = ar.replica_id WHERE is_local = 1'"
