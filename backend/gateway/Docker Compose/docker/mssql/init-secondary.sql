-- MSSQL Always On Secondary Replica Initialization Script
-- Bu script secondary replica'da çalışır

USE master;
GO

-- Always On Availability Groups'u etkinleştir
EXEC sp_configure 'show advanced options', 1;
RECONFIGURE;
GO

EXEC sp_configure 'Agent XPs', 1;
RECONFIGURE;
GO

-- Secondary replica için endpoint oluşturma script'i
-- Not: Bu script container başladıktan sonra manuel olarak çalıştırılmalı
/*
-- Endpoint oluştur
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);

-- Primary'dan gelen backup'ı restore et
RESTORE DATABASE TestDB FROM DISK = '/var/opt/mssql/shared/TestDB.bak' 
WITH NORECOVERY;

-- Availability Group'a katıl
ALTER AVAILABILITY GROUP TestAG JOIN;

-- Veritabanını Availability Group'a ekle
ALTER DATABASE TestDB SET HADR AVAILABILITY GROUP = TestAG;
*/

PRINT 'Secondary replica initialization completed';
GO
