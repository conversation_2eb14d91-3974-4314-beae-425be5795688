#!/bin/bash

# MSSQL Always On Availability Groups Test Script
# Bu script Always On yapılandırmasını test eder

echo "🧪 Testing MSSQL Always On Availability Groups..."
echo "================================================"

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test fonksiyonu
test_connection() {
    local server=$1
    local port=$2
    local description=$3
    
    echo -e "${BLUE}Testing $description ($server:$port)...${NC}"
    
    result=$(docker exec $server /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "SELECT @@SERVERNAME as ServerName" -h -1 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Connection successful: $result${NC}"
        return 0
    else
        echo -e "${RED}❌ Connection failed${NC}"
        return 1
    fi
}

# Replica durumunu test et
test_replica_status() {
    local server=$1
    local description=$2
    
    echo -e "${BLUE}Testing $description replica status...${NC}"
    
    result=$(docker exec $server /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
    SELECT
        @@SERVERNAME as ServerName,
        ISNULL(role_desc, 'STANDALONE') as Role,
        ISNULL(operational_state_desc, 'N/A') as State
    FROM sys.dm_hadr_availability_replica_states ars
    RIGHT JOIN sys.availability_replicas ar ON ars.replica_id = ar.replica_id
    WHERE ar.replica_server_name = @@SERVERNAME OR ar.replica_server_name IS NULL
    " -h -1 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Replica status: $result${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to get replica status${NC}"
        return 1
    fi
}

# Veritabanı durumunu test et
test_database() {
    local server=$1
    local description=$2
    
    echo -e "${BLUE}Testing $description database access...${NC}"
    
    result=$(docker exec $server /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -d TestDB -Q "
    SELECT
        DB_NAME() as DatabaseName,
        COUNT(*) as UserCount
    FROM Users
    " -h -1 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Database access: $result${NC}"
        return 0
    else
        echo -e "${RED}❌ Database access failed${NC}"
        return 1
    fi
}

# Availability Group durumunu test et
test_availability_group() {
    local server=$1
    local description=$2
    
    echo -e "${BLUE}Testing $description Availability Group...${NC}"
    
    result=$(docker exec $server /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "
    SELECT
        ISNULL(ag.name, 'NO_AG') as AG_Name,
        COUNT(ar.replica_server_name) as ReplicaCount
    FROM sys.availability_groups ag
    RIGHT JOIN sys.availability_replicas ar ON ag.group_id = ar.group_id
    GROUP BY ag.name
    " -h -1 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Availability Group: $result${NC}"
        return 0
    else
        echo -e "${RED}❌ Availability Group check failed${NC}"
        return 1
    fi
}

# Ana test süreci
echo -e "${YELLOW}1. Testing container connectivity...${NC}"
test_connection "mssql-primary" "1433" "Primary Replica"
primary_conn=$?

test_connection "mssql-secondary" "1434" "Secondary Replica"
secondary_conn=$?

echo ""
echo -e "${YELLOW}2. Testing replica status...${NC}"
test_replica_status "mssql-primary" "Primary"
test_replica_status "mssql-secondary" "Secondary"

echo ""
echo -e "${YELLOW}3. Testing database access...${NC}"
test_database "mssql-primary" "Primary"
test_database "mssql-secondary" "Secondary"

echo ""
echo -e "${YELLOW}4. Testing Availability Groups...${NC}"
test_availability_group "mssql-primary" "Primary"
test_availability_group "mssql-secondary" "Secondary"

echo ""
echo -e "${YELLOW}5. Testing write operations...${NC}"
echo -e "${BLUE}Testing write on primary...${NC}"
write_result=$(docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -d TestDB -Q "
INSERT INTO Users (Username, Email) VALUES ('testuser_$(date +%s)', '<EMAIL>');
SELECT @@ROWCOUNT as RowsAffected;
" -h -1 2>/dev/null)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Write operation successful: $write_result${NC}"
else
    echo -e "${RED}❌ Write operation failed${NC}"
fi

echo -e "${BLUE}Testing write on secondary (should fail)...${NC}"
write_secondary_result=$(docker exec mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -d TestDB -Q "
INSERT INTO Users (Username, Email) VALUES ('should_fail', '<EMAIL>');
" 2>&1)

if [[ $write_secondary_result == *"read-only"* ]] || [[ $write_secondary_result == *"READONLY"* ]]; then
    echo -e "${GREEN}✅ Secondary correctly rejected write operation${NC}"
else
    echo -e "${YELLOW}⚠️  Secondary write result: $write_secondary_result${NC}"
fi

echo ""
echo "================================================"
echo -e "${BLUE}📊 Test Summary${NC}"
echo "================================================"

if [ $primary_conn -eq 0 ] && [ $secondary_conn -eq 0 ]; then
    echo -e "${GREEN}✅ All connections working${NC}"
    echo ""
    echo -e "${GREEN}🎉 MSSQL Always On Availability Groups is ready!${NC}"
    echo ""
    echo "Connection Information:"
    echo "Primary (Read/Write): localhost:1433"
    echo "Secondary (Read-Only): localhost:1434"
    echo "Username: sa"
    echo "Password: YourStrong@Passw0rd"
    echo "Database: TestDB"
else
    echo -e "${RED}❌ Some connections failed. Check the setup.${NC}"
    exit 1
fi
