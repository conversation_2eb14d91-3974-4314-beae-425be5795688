-- MSSQL Always On Primary Replica Initialization Script
-- <PERSON>u script primary replica'da çalışır

USE master;
GO

-- Always On Availability Groups'u etkinleştir
EXEC sp_configure 'show advanced options', 1;
RECONFIGURE;
GO

EXEC sp_configure 'Agent XPs', 1;
RECONFIGURE;
GO

-- Test veritabanı oluştur
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'TestDB')
BEGIN
    CREATE DATABASE TestDB;
END
GO

-- Test veritabanını Full Recovery Model'e geç
ALTER DATABASE TestDB SET RECOVERY FULL;
GO

-- Test veritabanının backup'ını al (Always On için gere<PERSON>)
BACKUP DATABASE TestDB TO DISK = '/var/opt/mssql/data/TestDB.bak';
GO

-- Test kullanıcıları oluştur
USE TestDB;
GO

-- Test tabloları oluştur
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
BEGIN
    CREATE TABLE Users (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL,
        Email NVARCHAR(100) NOT NULL,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Test verileri ekle
IF NOT EXISTS (SELECT * FROM Users)
BEGIN
    INSERT INTO Users (Username, Email) VALUES 
    ('admin', '<EMAIL>'),
    ('testuser', '<EMAIL>');
END
GO

-- Test rolleri oluştur
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'db_reader' AND type = 'R')
BEGIN
    CREATE ROLE db_reader;
    GRANT SELECT ON SCHEMA::dbo TO db_reader;
END
GO

IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'db_writer' AND type = 'R')
BEGIN
    CREATE ROLE db_writer;
    GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::dbo TO db_writer;
END
GO

-- Always On Availability Group oluşturma script'i
-- Not: Bu script container başladıktan sonra manuel olarak çalıştırılmalı
/*
-- Endpoint oluştur
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);

-- Availability Group oluştur
CREATE AVAILABILITY GROUP TestAG
    WITH (AUTOMATED_BACKUP_PREFERENCE = PRIMARY)
    FOR DATABASE TestDB
    REPLICA ON 
        'mssql-primary' WITH (
            ENDPOINT_URL = 'TCP://mssql-primary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        ),
        'mssql-secondary' WITH (
            ENDPOINT_URL = 'TCP://mssql-secondary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        );
*/

PRINT 'Primary replica initialization completed';
GO
