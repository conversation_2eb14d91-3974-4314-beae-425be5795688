# Docker Compose Environment Variables Example
# Bu dosyayı .env olarak kopyalayın ve değerleri düzenleyin

# PostgreSQL Configuration
POSTGRES_DB=auth_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_PORT=5432
POSTGRES_HEALTH_CHECK_INTERVAL=30s
POSTGRES_HEALTH_CHECK_TIMEOUT=10s
POSTGRES_HEALTH_CHECK_RETRIES=3

# Redis Configuration
REDIS_PORT=6379
REDIS_HEALTH_CHECK_INTERVAL=30s
REDIS_HEALTH_CHECK_TIMEOUT=10s
REDIS_HEALTH_CHECK_RETRIES=3

# Network Configuration
NETWORK_NAME=monitoring-network

# Volume Configuration
POSTGRES_VOLUME_NAME=postgres_data
REDIS_VOLUME_NAME=redis_data

# Container Names
POSTGRES_CONTAINER_NAME=monitoring-postgres
REDIS_CONTAINER_NAME=monitoring-redis

# Production Example:
# POSTGRES_PORT=5433
# REDIS_PORT=6380
# POSTGRES_HEALTH_CHECK_INTERVAL=60s
# POSTGRES_HEALTH_CHECK_TIMEOUT=30s
# POSTGRES_HEALTH_CHECK_RETRIES=5
