# MSSQL Always On Availability Groups Docker Setup

Bu rehber, Docker Compose kullanarak MSSQL Always On Availability Groups ortamını nasıl kuracağınızı açıklar.

## 🚀 Hızlı Başlangıç

### 1. Environment Dosyasını Hazırlayın

```bash
# .env.mssql dosyasını .env olarak kopyalayın
cp .env.mssql .env

# Gerekirse şifreleri ve portları düzenleyin
nano .env
```

### 2. Container'lar<PERSON>

```bash
# Tüm servisleri başlat (Docker Compose V2)
docker compose up -d

# Veya eski versiyon için
docker-compose up -d

# Logları takip edin
docker compose logs -f mssql-primary mssql-secondary
```

### 3. Always On Yapılandırmasını Tamamlayın

```bash
# Setup script'ini çalıştırın
chmod +x docker/mssql/setup-alwayson.sh
./docker/mssql/setup-alwayson.sh
```

## 📋 Servis Bilgileri

### Primary Replica
- **Host**: localhost
- **Port**: 1433
- **Kullanıcı**: sa
- **Şifre**: YourStrong@Passw0rd
- **Rol**: Read/Write
- **HADR Port**: 5022

### Secondary Replica
- **Host**: localhost
- **Port**: 1434
- **Kullanıcı**: sa
- **Şifre**: YourStrong@Passw0rd
- **Rol**: Read-Only
- **HADR Port**: 5023

### Test Veritabanı
- **Database**: TestDB
- **Tables**: Users
- **Roles**: db_reader, db_writer

## 🔧 Manuel Yapılandırma

Eğer otomatik setup çalışmazsa, manuel olarak yapılandırabilirsiniz:

### 1. Primary Replica'da Endpoint Oluşturun

```sql
-- Primary container'a bağlanın
docker exec -it mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C

-- Endpoint oluşturun
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);
GO
```

### 2. Secondary Replica'da Endpoint Oluşturun

```sql
-- Secondary container'a bağlanın
docker exec -it mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C

-- Endpoint oluşturun
CREATE ENDPOINT AlwaysOn_Endpoint
    STATE = STARTED
    AS TCP (LISTENER_PORT = 5022)
    FOR DATABASE_MIRRORING (ROLE = ALL);
GO
```

### 3. Veritabanı Backup'ını Alın ve Restore Edin

```sql
-- Primary'da backup alın
BACKUP DATABASE TestDB TO DISK = '/var/opt/mssql/shared/TestDB.bak';
GO

-- Secondary'de restore edin
RESTORE DATABASE TestDB FROM DISK = '/var/opt/mssql/shared/TestDB.bak' WITH NORECOVERY;
GO
```

### 4. Availability Group Oluşturun

```sql
-- Primary'da AG oluşturun
CREATE AVAILABILITY GROUP TestAG
    WITH (AUTOMATED_BACKUP_PREFERENCE = PRIMARY)
    FOR DATABASE TestDB
    REPLICA ON 
        'mssql-primary' WITH (
            ENDPOINT_URL = 'TCP://mssql-primary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        ),
        'mssql-secondary' WITH (
            ENDPOINT_URL = 'TCP://mssql-secondary:5022',
            AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
            FAILOVER_MODE = AUTOMATIC,
            BACKUP_PRIORITY = 50,
            SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
        );
GO
```

### 5. Secondary'yi AG'ye Katın

```sql
-- Secondary'de AG'ye katılın
ALTER AVAILABILITY GROUP TestAG JOIN;
GO

-- Veritabanını AG'ye ekleyin
ALTER DATABASE TestDB SET HADR AVAILABILITY GROUP = TestAG;
GO
```

## 🧪 Test Komutları

### Replica Durumunu Kontrol Edin

```sql
-- Primary/Secondary durumunu kontrol edin
SELECT 
    @@SERVERNAME as ServerName,
    role_desc as Role,
    operational_state_desc as State
FROM sys.dm_hadr_availability_replica_states ars 
INNER JOIN sys.availability_replicas ar ON ars.replica_id = ar.replica_id 
WHERE is_local = 1;
```

### Failover Testi

```sql
-- Primary'da failover yapın
ALTER AVAILABILITY GROUP TestAG FAILOVER;
```

### Bağlantı Testi

```bash
# Primary'a bağlanın (Read/Write)
docker exec -it mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -d TestDB -Q "SELECT @@SERVERNAME, COUNT(*) FROM Users"

# Secondary'ye bağlanın (Read-Only)
docker exec -it mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -d TestDB -Q "SELECT @@SERVERNAME, COUNT(*) FROM Users"
```

## 🔍 Troubleshooting

### Container Loglarını Kontrol Edin

```bash
# Primary logs
docker logs mssql-primary

# Secondary logs
docker logs mssql-secondary
```

### AG Durumunu Kontrol Edin

```sql
-- AG durumunu kontrol edin
SELECT 
    ag.name as AG_Name,
    ar.replica_server_name,
    ars.role_desc,
    ars.operational_state_desc,
    ars.connected_state_desc,
    ars.synchronization_state_desc
FROM sys.availability_groups ag
INNER JOIN sys.availability_replicas ar ON ag.group_id = ar.group_id
INNER JOIN sys.dm_hadr_availability_replica_states ars ON ar.replica_id = ars.replica_id;
```

### Endpoint Durumunu Kontrol Edin

```sql
-- Endpoint durumunu kontrol edin
SELECT name, state_desc, type_desc FROM sys.endpoints WHERE type_desc = 'DATABASE_MIRRORING';
```

## 🛑 Temizlik

```bash
# Container'ları durdurun ve temizleyin
docker compose down
# veya
docker-compose down

# Volume'ları da silin (DİKKAT: Tüm veriler silinir!)
docker compose down -v
docker volume prune -f
```

## 📝 Notlar

- MSSQL Always On, Enterprise Edition gerektirir, ancak Developer Edition ile test edilebilir
- Container'lar arası iletişim için Docker network kullanılır
- Shared volume, backup dosyalarının paylaşımı için kullanılır
- Health check'ler container'ların hazır olduğunu garanti eder
- Setup script, container'lar hazır olduktan sonra çalıştırılmalıdır
