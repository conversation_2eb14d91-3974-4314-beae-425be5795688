#!/bin/bash

# MSSQL Always On Availability Groups Başlatma Script'i
# Bu script tüm süreci otomatikleştirir

echo "🚀 Starting MSSQL Always On Availability Groups..."
echo "=================================================="

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Environment dosyasını kontrol et
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Creating from template...${NC}"
    if [ -f ".env.mssql" ]; then
        cp .env.mssql .env
        echo -e "${GREEN}✅ .env file created from .env.mssql${NC}"
    else
        echo -e "${RED}❌ .env.mssql template not found!${NC}"
        exit 1
    fi
fi

# Docker Compose komutunu tespit et
DOCKER_COMPOSE_CMD="docker-compose"
if ! command -v docker-compose &> /dev/null; then
    if command -v docker &> /dev/null && docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
    else
        echo -e "${RED}❌ Neither 'docker-compose' nor 'docker compose' found!${NC}"
        echo "Please install Docker Compose or Docker Desktop"
        exit 1
    fi
fi

# Docker Compose ile servisleri başlat
echo -e "${BLUE}📦 Starting Docker containers with $DOCKER_COMPOSE_CMD...${NC}"
$DOCKER_COMPOSE_CMD up -d

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to start containers${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Containers started successfully${NC}"

# Container'ların hazır olmasını bekle
echo -e "${BLUE}⏳ Waiting for containers to be ready...${NC}"
echo "This may take up to 2 minutes..."

# Primary container'ın hazır olmasını bekle
echo "Waiting for primary replica..."
for i in {1..60}; do
    if docker exec mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "SELECT 1" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Primary replica is ready${NC}"
        break
    fi
    if [ $i -eq 60 ]; then
        echo -e "${RED}❌ Primary replica failed to start${NC}"
        exit 1
    fi
    sleep 2
done

# Secondary container'ın hazır olmasını bekle
echo "Waiting for secondary replica..."
for i in {1..60}; do
    if docker exec mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "SELECT 1" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Secondary replica is ready${NC}"
        break
    fi
    if [ $i -eq 60 ]; then
        echo -e "${RED}❌ Secondary replica failed to start${NC}"
        exit 1
    fi
    sleep 2
done

# Always On yapılandırmasını çalıştır
echo -e "${BLUE}🔧 Configuring Always On Availability Groups...${NC}"
./docker/mssql/setup-alwayson.sh

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Always On setup failed${NC}"
    exit 1
fi

# Kısa bir bekleme
sleep 5

# Test'leri çalıştır
echo -e "${BLUE}🧪 Running tests...${NC}"
./docker/mssql/test-alwayson.sh

if [ $? -eq 0 ]; then
    echo ""
    echo "=================================================="
    echo -e "${GREEN}🎉 MSSQL Always On Availability Groups is ready!${NC}"
    echo "=================================================="
    echo ""
    echo "📋 Connection Information:"
    echo "Primary Replica (Read/Write):"
    echo "  Host: localhost"
    echo "  Port: 1433"
    echo "  Username: sa"
    echo "  Password: YourStrong@Passw0rd"
    echo ""
    echo "Secondary Replica (Read-Only):"
    echo "  Host: localhost"
    echo "  Port: 1434"
    echo "  Username: sa"
    echo "  Password: YourStrong@Passw0rd"
    echo ""
    echo "📊 Test Database: TestDB"
    echo "🔧 Available Roles: db_reader, db_writer"
    echo ""
    echo "🛠️  Useful Commands:"
    echo "  View logs: $DOCKER_COMPOSE_CMD logs -f mssql-primary mssql-secondary"
    echo "  Connect to primary: docker exec -it mssql-primary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C"
    echo "  Connect to secondary: docker exec -it mssql-secondary /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C"
    echo "  Run tests: ./docker/mssql/test-alwayson.sh"
    echo "  Stop services: $DOCKER_COMPOSE_CMD down"
    echo ""
    echo "📚 For more information, see README-MSSQL-AlwaysOn.md"
else
    echo -e "${RED}❌ Some tests failed. Check the logs for details.${NC}"
    echo "You can still use the services, but some features might not work correctly."
    echo ""
    echo "Troubleshooting:"
    echo "  Check logs: $DOCKER_COMPOSE_CMD logs mssql-primary mssql-secondary"
    echo "  Retry setup: ./docker/mssql/setup-alwayson.sh"
    echo "  Run tests: ./docker/mssql/test-alwayson.sh"
fi
