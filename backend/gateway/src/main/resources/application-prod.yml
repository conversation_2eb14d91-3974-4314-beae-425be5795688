# Production Environment Configuration

server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: gateway-prod
    
  # Production Database (External PostgreSQL)
  datasource:
    url: ${DB_URL:**************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:dbauth_user}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  # JPA Configuration for Production
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate  # Never auto-create in production
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

# Gateway Configuration for Production
gateway:
  server-management-url: ${SERVER_MANAGEMENT_URL:http://server-management:8082}
  user-management-url: ${USER_MANAGEMENT_URL:http://user-management:8081}
  
  http-client:
    connection-timeout-ms: 10000
    read-timeout-ms: 30000
    write-timeout-ms: 30000
    max-retries: 3
    max-connections: 50
    
  rate-limit:
    enabled: true
    requests-per-minute: 1000
    burst-limit: 100
    
  security:
    basic-auth-enabled: false
    jwt-enabled: true
    jwt-secret: ${JWT_SECRET}  # Must be provided via environment
    jwt-expiry-seconds: 3600   # 1 hour in production

# Production Logging (Less verbose)
logging:
  level:
    com.dbauth: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.springframework.web.filter: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: /var/log/gateway/gateway.log
    max-size: 100MB
    max-history: 30

# Swagger Configuration for Production
springdoc:
  api-docs:
    enabled: false  # Disable in production for security
  swagger-ui:
    enabled: false  # Disable in production

# Actuator for Production (Limited exposure)
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    info:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Production specific properties
app:
  environment: production
  debug-mode: false
  mock-external-services: false

# Security headers
server:
  error:
    include-message: never
    include-binding-errors: never
    include-stacktrace: never
    include-exception: false
