# Development Environment Configuration

server:
  port: 8080

spring:
  application:
    name: gateway-dev
  
  # Development Database (Local PostgreSQL)
  datasource:
    url: ****************************************
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: password
    
  # JPA Configuration for Development
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update  # Auto-create/update tables in dev
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true

# Gateway Configuration for Development
gateway:
  server-management-url: http://localhost:8082
  user-management-url: http://localhost:8081
  
  http-client:
    connection-timeout-ms: 5000   # Shorter timeout for dev
    read-timeout-ms: 15000
    write-timeout-ms: 15000
    max-retries: 2
    max-connections: 10
    
  rate-limit:
    enabled: false  # Disable rate limiting in dev
    
  security:
    basic-auth-enabled: false  # Disable basic auth in dev
    jwt-enabled: true
    jwt-secret: ${JWT_SECRET:dev-jwt-secret-key-for-development-environment-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-this-is-exactly-64-characters-long}
    jwt-expiration: ${JWT_EXPIRATION:86400000}  # 1 day in milliseconds

# Development Logging
logging:
  level:
    com.dbauth: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web.filter: DEBUG  # Request logging
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"

# Swagger Configuration for Development
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none

# Actuator for Development
management:
  endpoints:
    web:
      exposure:
        include: "*"  # Expose all endpoints in dev
  endpoint:
    health:
      show-details: always
    env:
      show-values: always

# Development specific properties
app:
  environment: development
  debug-mode: true
  mock-external-services: true
