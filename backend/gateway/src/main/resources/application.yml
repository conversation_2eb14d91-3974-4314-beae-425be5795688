# Gateway Application Configuration
# Default configuration - can be overridden by profile-specific configs

server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: gateway

  # Active profile (can be overridden by environment variable)
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # Database Configuration (PostgreSQL)
  datasource:
    url: ****************************************
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: password

  # JWT aktif olduğunu belirt
  main:
    allow-bean-definition-overriding: true

  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Flyway Configuration - DISABLED
  flyway:
    enabled: false



# Gateway Configuration
gateway:
  # Server Management modülü URL'i - Varsayılan: "http://localhost:8082"
  server-management-url: http://localhost:8082
  # User Management modülü URL'i - Ayrı proje olarak çalışıyor
  user-management-url: http://localhost:8081

  # HTTP Client Configuration
  http-client:
    # Connection timeout (milisaniye) - Varsayılan: 10000 (10 saniye)
    connection-timeout-ms: 10000
    # Read timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
    read-timeout-ms: 30000
    # Write timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
    write-timeout-ms: 30000
    # Maksimum retry sayısı - Varsayılan: 3
    max-retries: 3
    # Connection pool boyutu - Varsayılan: 20
    max-connections: 20

  # Rate Limiting Configuration
  rate-limit:
    # Rate limiting aktif/pasif - Varsayılan: false
    enabled: false
    # Dakika başına maksimum istek sayısı - Varsayılan: 1000
    requests-per-minute: 1000
    # Burst limit - Varsayılan: 100
    burst-limit: 100

  # Security Configuration
  security:
    # Basic auth aktif/pasif - Varsayılan: true
    basic-auth-enabled: true
    # Basic auth username - Varsayılan: "user"
    basic-auth-username: user
    # Basic auth password - Varsayılan: "df1a873a-b5b4-4baa-8437-f85c840829a3"
    basic-auth-password: df1a873a-b5b4-4baa-8437-f85c840829a3
    # JWT aktif/pasif - Varsayılan: true
    jwt-enabled: true
    # JWT secret key - Güvenli bir key kullan (512 bit = 64 karakter)
    jwt-secret: default-jwt-secret-key-for-gateway-authentication-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-exactly-64-chars
    # JWT token expiry (saniye) - 1 gün = 86400 saniye
    jwt-expiry-seconds: 86400

# Application specific configuration
# CORS yapılandırması shared-resources'tan gelir

# Logging configuration
logging:
  config: classpath:logback-spring.xml
  level:
    com.dbauth: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Environment variable for log directory
LOG_DIR: ../log/gateway

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
  endpoint:
    health:
      show-details: always
