package com.dbauth.gateway.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * Request/Response Logging Filter
 * Tüm HTTP isteklerini ve yanıtlarını loglar
 */
@Component
@Order(1)
@Slf4j
public class RequestResponseLoggingFilter implements Filter {

    private static final List<String> EXCLUDED_PATHS = Arrays.asList(
            "/actuator",
            "/swagger",
            "/v3/api-docs",
            "/favicon.ico"
    );

    private static final List<String> SENSITIVE_HEADERS = Arrays.asList(
            "authorization",
            "cookie",
            "set-cookie"
    );

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // Excluded path'leri kontrol et
        if (isExcludedPath(httpRequest.getRequestURI())) {
            chain.doFilter(request, response);
            return;
        }

        // Request ve Response wrapper'ları oluştur
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(httpRequest);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(httpResponse);

        long startTime = System.currentTimeMillis();

        try {
            // Request'i logla
            logRequest(wrappedRequest);

            // Filter chain'i devam ettir
            chain.doFilter(wrappedRequest, wrappedResponse);

        } finally {
            long duration = System.currentTimeMillis() - startTime;

            // Response'u logla
            logResponse(wrappedRequest, wrappedResponse, duration);

            // Response content'ini client'a gönder
            wrappedResponse.copyBodyToResponse();
        }
    }

    private void logRequest(ContentCachingRequestWrapper request) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String method = request.getMethod();
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();
            String remoteAddr = getClientIpAddress(request);

            StringBuilder logMessage = new StringBuilder();
            logMessage.append("\n=== INCOMING REQUEST ===");
            logMessage.append("\nTimestamp: ").append(timestamp);
            logMessage.append("\nMethod: ").append(method);
            logMessage.append("\nURI: ").append(uri);
            if (queryString != null) {
                logMessage.append("\nQuery: ").append(queryString);
            }
            logMessage.append("\nClient IP: ").append(remoteAddr);
            logMessage.append("\nUser-Agent: ").append(request.getHeader("User-Agent"));

            // Headers (sensitive olanları gizle)
            logMessage.append("\nHeaders:");
            request.getHeaderNames().asIterator().forEachRemaining(headerName -> {
                String headerValue = request.getHeader(headerName);
                if (SENSITIVE_HEADERS.contains(headerName.toLowerCase())) {
                    headerValue = "***HIDDEN***";
                }
                logMessage.append("\n  ").append(headerName).append(": ").append(headerValue);
            });

            // Request body (POST/PUT için)
            if ("POST".equals(method) || "PUT".equals(method)) {
                String requestBody = getRequestBody(request);
                if (requestBody != null && !requestBody.isEmpty()) {
                    // Password gibi sensitive alanları gizle
                    requestBody = hideSensitiveData(requestBody);
                    logMessage.append("\nBody: ").append(requestBody);
                }
            }

            log.info(logMessage.toString());

        } catch (Exception e) {
            log.warn("Error logging request: {}", e.getMessage());
        }
    }

    private void logResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, long duration) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String method = request.getMethod();
            String uri = request.getRequestURI();
            int status = response.getStatus();

            StringBuilder logMessage = new StringBuilder();
            logMessage.append("\n=== OUTGOING RESPONSE ===");
            logMessage.append("\nTimestamp: ").append(timestamp);
            logMessage.append("\nMethod: ").append(method);
            logMessage.append("\nURI: ").append(uri);
            logMessage.append("\nStatus: ").append(status);
            logMessage.append("\nDuration: ").append(duration).append("ms");

            // Response headers
            logMessage.append("\nResponse Headers:");
            response.getHeaderNames().forEach(headerName -> {
                String headerValue = response.getHeader(headerName);
                logMessage.append("\n  ").append(headerName).append(": ").append(headerValue);
            });

            // Response body (sadece JSON response'lar için)
            String contentType = response.getContentType();
            if (contentType != null && contentType.contains("application/json")) {
                String responseBody = getResponseBody(response);
                if (responseBody != null && !responseBody.isEmpty()) {
                    // Sensitive data'yı gizle
                    responseBody = hideSensitiveData(responseBody);
                    logMessage.append("\nResponse Body: ").append(responseBody);
                }
            }

            // Performance warning
            if (duration > 1000) {
                logMessage.append("\n⚠️  SLOW REQUEST WARNING: ").append(duration).append("ms");
            }

            log.info(logMessage.toString());

        } catch (Exception e) {
            log.warn("Error logging response: {}", e.getMessage());
        }
    }

    private boolean isExcludedPath(String path) {
        return EXCLUDED_PATHS.stream().anyMatch(path::startsWith);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] content = request.getContentAsByteArray();
        if (content.length > 0) {
            return new String(content, StandardCharsets.UTF_8);
        }
        return null;
    }

    private String getResponseBody(ContentCachingResponseWrapper response) {
        byte[] content = response.getContentAsByteArray();
        if (content.length > 0) {
            return new String(content, StandardCharsets.UTF_8);
        }
        return null;
    }

    private String hideSensitiveData(String data) {
        if (data == null) return null;

        // Password, token gibi sensitive alanları gizle
        return data
                .replaceAll("(\"password\"\\s*:\\s*\")[^\"]*\"", "$1***HIDDEN***\"")
                .replaceAll("(\"token\"\\s*:\\s*\")[^\"]*\"", "$1***HIDDEN***\"")
                .replaceAll("(\"secret\"\\s*:\\s*\")[^\"]*\"", "$1***HIDDEN***\"")
                .replaceAll("(\"authorization\"\\s*:\\s*\")[^\"]*\"", "$1***HIDDEN***\"");
    }
}
