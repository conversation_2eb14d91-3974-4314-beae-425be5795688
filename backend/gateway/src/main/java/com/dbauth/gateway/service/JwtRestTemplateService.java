package com.dbauth.gateway.service;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;

/**
 * JWT Token Forward Service
 * Gateway'den diğer modüllere yapılan REST çağrılarında JWT token'ı otomatik olarak forward eder
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JwtRestTemplateService {

    @Qualifier("gatewayRestTemplate")
    private final RestTemplate restTemplate;

    /**
     * GET request with JWT token forwarding
     */
    public <T> ResponseEntity<T> getForEntity(String url, Class<T> responseType, Object... uriVariables) {
        HttpHeaders headers = createHeadersWithJwt();
        HttpEntity<?> entity = new HttpEntity<>(headers);
        
        log.debug("Making GET request to: {} with JWT token", url);
        return restTemplate.exchange(url, HttpMethod.GET, entity, responseType, uriVariables);
    }

    /**
     * POST request with JWT token forwarding
     */
    public <T> ResponseEntity<T> postForEntity(String url, Object request, Class<T> responseType, Object... uriVariables) {
        HttpHeaders headers = createHeadersWithJwt();
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        
        log.debug("Making POST request to: {} with JWT token", url);
        return restTemplate.exchange(url, HttpMethod.POST, entity, responseType, uriVariables);
    }

    /**
     * PUT request with JWT token forwarding
     */
    public <T> ResponseEntity<T> putForEntity(String url, Object request, Class<T> responseType, Object... uriVariables) {
        HttpHeaders headers = createHeadersWithJwt();
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        
        log.debug("Making PUT request to: {} with JWT token", url);
        return restTemplate.exchange(url, HttpMethod.PUT, entity, responseType, uriVariables);
    }

    /**
     * DELETE request with JWT token forwarding
     */
    public <T> ResponseEntity<T> deleteForEntity(String url, Class<T> responseType, Object... uriVariables) {
        HttpHeaders headers = createHeadersWithJwt();
        HttpEntity<?> entity = new HttpEntity<>(headers);
        
        log.debug("Making DELETE request to: {} with JWT token", url);
        return restTemplate.exchange(url, HttpMethod.DELETE, entity, responseType, uriVariables);
    }

    /**
     * Generic exchange method with JWT token forwarding
     */
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, Object requestBody, 
                                         Class<T> responseType, Object... uriVariables) {
        HttpHeaders headers = createHeadersWithJwt();
        HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
        
        log.debug("Making {} request to: {} with JWT token", method, url);
        return restTemplate.exchange(url, method, entity, responseType, uriVariables);
    }

    /**
     * JWT token'ı içeren HTTP headers oluştur
     */
    private HttpHeaders createHeadersWithJwt() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

        // SecurityContext'ten JWT token'ı al
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.isAuthenticated()) {
            // JWT token'ı Authorization header'ına ekle
            // Not: Gerçek JWT token'ı almak için request'ten extract etmemiz gerekiyor
            String jwtToken = extractJwtTokenFromRequest();
            
            if (jwtToken != null && !jwtToken.isEmpty()) {
                headers.setBearerAuth(jwtToken);
                log.debug("JWT token added to request headers for user: {}", authentication.getName());
            } else {
                log.warn("JWT token not found in request context for user: {}", authentication.getName());
            }
        } else {
            log.warn("No authentication found in SecurityContext");
        }

        return headers;
    }

    /**
     * Request'ten JWT token'ı extract et
     */
    private String extractJwtTokenFromRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String authorizationHeader = request.getHeader("Authorization");

                if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
                    String token = authorizationHeader.substring(7);
                    log.debug("JWT token extracted from request");
                    return token;
                }
            }
        } catch (Exception e) {
            log.warn("Failed to extract JWT token from request: {}", e.getMessage());
        }

        return null;
    }
}
