package com.dbauth.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Authentication Response DTO
 * Login başarılı olduğunda dönen response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {

    private String token;
    private String type = "Bearer";
    private String email;
    private String firstName;
    private String lastName;
    private String fullName;
    private LocalDateTime expiresAt;
    private LocalDateTime issuedAt;

    public AuthResponse(String token, String email, String firstName, String lastName, String fullName, LocalDateTime expiresAt) {
        this.token = token;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullName = fullName;
        this.expiresAt = expiresAt;
        this.issuedAt = LocalDateTime.now();
    }
}
