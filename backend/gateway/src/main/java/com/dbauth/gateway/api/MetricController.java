package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Metric Management Proxy Controller
 * Routes requests to server-management service
 *
 * Supports both /api/metrics and /api/metric-queries endpoints for compatibility
 */
@RestController
@Tag(name = "Metric Management", description = "Metric management operations")
public class MetricController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.server-management-url}")
    private String serverManagementUrl;

    // ===== /api/metrics endpoints (for frontend-2 compatibility) =====

    @GetMapping("/api/metrics")
    @Operation(summary = "Get all metrics", description = "Retrieve all metric definitions")
    public ResponseEntity<?> getAllMetrics() {
        String url = serverManagementUrl + "/api/metric-queries";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/api/metrics/{id}")
    @Operation(summary = "Get metric by ID", description = "Retrieve a specific metric by its ID")
    public ResponseEntity<?> getMetricById(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PostMapping("/api/metrics")
    @Operation(summary = "Create metric", description = "Create a new metric definition")
    public ResponseEntity<?> createMetric(@RequestBody Map<String, Object> metricData) {
        String url = serverManagementUrl + "/api/metric-queries";
        return restTemplateService.exchange(url, HttpMethod.POST, metricData, Object.class);
    }

    @PutMapping("/api/metrics/{id}")
    @Operation(summary = "Update metric", description = "Update an existing metric")
    public ResponseEntity<?> updateMetric(@PathVariable String id, @RequestBody Map<String, Object> metricData) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.PUT, metricData, Object.class);
    }

    @DeleteMapping("/api/metrics/{id}")
    @Operation(summary = "Delete metric", description = "Delete a metric definition")
    public ResponseEntity<?> deleteMetric(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.DELETE, null, Object.class);
    }

    @PatchMapping("/api/metrics/{id}/status")
    @Operation(summary = "Toggle metric status", description = "Toggle metric active/inactive status")
    public ResponseEntity<?> toggleMetricStatus(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id + "/toggle-status";
        return restTemplateService.exchange(url, HttpMethod.PATCH, null, Object.class);
    }

    // ===== /api/metric-queries endpoints (original) =====

    @GetMapping("/api/metric-queries")
    @Operation(summary = "Get all metrics (original)", description = "Retrieve all metric definitions")
    public ResponseEntity<?> getAllMetricsOriginal() {
        String url = serverManagementUrl + "/api/metric-queries";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/api/metric-queries/{id}")
    @Operation(summary = "Get metric by ID (original)", description = "Retrieve a specific metric by its ID")
    public ResponseEntity<?> getMetricByIdOriginal(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PostMapping("/api/metric-queries")
    @Operation(summary = "Create metric (original)", description = "Create a new metric definition")
    public ResponseEntity<?> createMetricOriginal(@RequestBody Map<String, Object> metricData) {
        String url = serverManagementUrl + "/api/metric-queries";
        return restTemplateService.exchange(url, HttpMethod.POST, metricData, Object.class);
    }

    @PutMapping("/api/metric-queries/{id}")
    @Operation(summary = "Update metric (original)", description = "Update an existing metric")
    public ResponseEntity<?> updateMetricOriginal(@PathVariable String id, @RequestBody Map<String, Object> metricData) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.PUT, metricData, Object.class);
    }

    @DeleteMapping("/api/metric-queries/{id}")
    @Operation(summary = "Delete metric (original)", description = "Delete a metric definition")
    public ResponseEntity<?> deleteMetricOriginal(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id;
        return restTemplateService.exchange(url, HttpMethod.DELETE, null, Object.class);
    }

    @PatchMapping("/api/metric-queries/{id}/status")
    @Operation(summary = "Toggle metric status (original)", description = "Toggle metric active/inactive status")
    public ResponseEntity<?> toggleMetricStatusOriginal(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id + "/toggle-status";
        return restTemplateService.exchange(url, HttpMethod.PATCH, null, Object.class);
    }

    // ===== COMPONENT MANAGEMENT ENDPOINTS =====

    @GetMapping("/api/metric-queries/component-types")
    @Operation(summary = "Get all component types", description = "Retrieve all available component types")
    public ResponseEntity<?> getAllComponentTypes() {
        String url = serverManagementUrl + "/api/metric-queries/component-types";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/api/metric-queries/component-types/compatible")
    @Operation(summary = "Get compatible component types", description = "Get component types compatible with specific data type")
    public ResponseEntity<?> getCompatibleComponentTypes(@RequestParam String dataType) {
        String url = serverManagementUrl + "/api/metric-queries/component-types/compatible?dataType=" + dataType;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/api/metric-queries/component-recommendations")
    @Operation(summary = "Get component recommendations", description = "Get recommended components for metric query")
    public ResponseEntity<?> getComponentRecommendations(@RequestParam String metricQueryId) {
        String url = serverManagementUrl + "/api/metric-queries/component-recommendations?metricQueryId=" + metricQueryId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/api/metric-queries/{id}/display-fields")
    @Operation(summary = "Get metric display fields", description = "Get display field configurations for a metric")
    public ResponseEntity<?> getMetricDisplayFields(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-queries/" + id + "/display-fields";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PutMapping("/api/metric-queries/{id}/display-fields/{column}/component")
    @Operation(summary = "Update display field component", description = "Update component type for a specific display field")
    public ResponseEntity<?> updateDisplayFieldComponent(
            @PathVariable String id,
            @PathVariable String column,
            @RequestBody Map<String, Object> componentData) {
        String url = serverManagementUrl + "/api/metric-queries/" + id + "/display-fields/" + column + "/component";
        return restTemplateService.exchange(url, HttpMethod.PUT, componentData, Object.class);
    }
}
