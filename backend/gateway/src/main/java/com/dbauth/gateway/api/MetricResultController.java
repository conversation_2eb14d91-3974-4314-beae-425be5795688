package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Metric Result Proxy Controller
 * Routes requests to server-management service
 */
@RestController
@RequestMapping("/api/metric-results")
@Tag(name = "Metric Results", description = "Metric result operations")
public class MetricResultController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.server-management-url}")
    private String serverManagementUrl;

    @GetMapping
    @Operation(summary = "Get metric results", description = "Get metric results with optional filtering")
    public ResponseEntity<?> getMetricResults(
            @RequestParam(required = false) String serverId,
            @RequestParam(required = false) String metricId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results?");
        url.append("page=").append(page).append("&size=").append(size);
        
        if (serverId != null) {
            url.append("&serverId=").append(serverId);
        }
        if (metricId != null) {
            url.append("&metricId=").append(metricId);
        }
        if (startDate != null) {
            url.append("&startDate=").append(startDate);
        }
        if (endDate != null) {
            url.append("&endDate=").append(endDate);
        }
        
        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get metric result by ID", description = "Retrieve a specific metric result by its ID")
    public ResponseEntity<?> getMetricResultById(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-results/" + id;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/stats")
    @Operation(summary = "Get metric statistics", description = "Get overall metric execution statistics")
    public ResponseEntity<?> getMetricStats() {
        String url = serverManagementUrl + "/api/metric-results/stats";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}")
    @Operation(summary = "Get metric results by server", description = "Get results for a specific server")
    public ResponseEntity<?> getMetricResultsByServer(
            @PathVariable String serverId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/server/" + serverId + "?");
        url.append("page=").append(page).append("&size=").append(size);

        if (startDate != null) {
            url.append("&startDate=").append(startDate);
        }
        if (endDate != null) {
            url.append("&endDate=").append(endDate);
        }

        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}/latest")
    @Operation(summary = "Get latest metric results by server", description = "Get latest results for a specific server")
    public ResponseEntity<?> getLatestMetricResultsByServer(@PathVariable String serverId) {
        String url = serverManagementUrl + "/api/metric-results/server/" + serverId + "/latest";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/metric/{metricId}")
    @Operation(summary = "Get metric results by metric", description = "Get results for a specific metric")
    public ResponseEntity<?> getMetricResultsByMetric(
            @PathVariable String metricId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/metric/" + metricId + "?");
        url.append("page=").append(page).append("&size=").append(size);
        
        if (startDate != null) {
            url.append("&startDate=").append(startDate);
        }
        if (endDate != null) {
            url.append("&endDate=").append(endDate);
        }
        
        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete metric result", description = "Delete a specific metric result")
    public ResponseEntity<?> deleteMetricResult(@PathVariable String id) {
        String url = serverManagementUrl + "/api/metric-results/" + id;
        return restTemplateService.exchange(url, HttpMethod.DELETE, null, Object.class);
    }

    @GetMapping("/server/{serverId}/metric/{metricName}")
    @Operation(summary = "Get metric results by server and metric name", description = "Get results for a specific server and metric")
    public ResponseEntity<?> getMetricsByServerIdAndMetricName(
            @PathVariable String serverId,
            @PathVariable String metricName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/server/" + serverId + "/metric/" + metricName + "?");
        url.append("page=").append(page).append("&size=").append(size);

        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}/time-range")
    @Operation(summary = "Get metric results by time range", description = "Get results for a specific server within time range")
    public ResponseEntity<?> getMetricsByTimeRange(
            @PathVariable String serverId,
            @RequestParam Long startTime,
            @RequestParam Long endTime) {

        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/server/" + serverId + "/time-range?");
        url.append("startTime=").append(startTime).append("&endTime=").append(endTime);

        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}/stats")
    @Operation(summary = "Get metric statistics by server", description = "Get metric statistics for a specific server")
    public ResponseEntity<?> getMetricStatsByServer(@PathVariable String serverId) {
        String url = serverManagementUrl + "/api/metric-results/server/" + serverId + "/stats";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/recent")
    @Operation(summary = "Get recent metric results", description = "Get recent metric results")
    public ResponseEntity<?> getRecentMetrics(
            @RequestParam(defaultValue = "3600000") Long sinceMs,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size) {

        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/recent?");
        url.append("sinceMs=").append(sinceMs).append("&page=").append(page).append("&size=").append(size);

        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}/by-type")
    @Operation(summary = "Get metric results by type", description = "Get metric results by type for a specific server")
    public ResponseEntity<?> getMetricsByType(
            @PathVariable String serverId,
            @RequestParam String metricType,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime) {

        StringBuilder url = new StringBuilder(serverManagementUrl + "/api/metric-results/server/" + serverId + "/by-type?");
        url.append("metricType=").append(metricType);

        if (startTime != null) {
            url.append("&startTime=").append(startTime);
        }
        if (endTime != null) {
            url.append("&endTime=").append(endTime);
        }

        return restTemplateService.exchange(url.toString(), HttpMethod.GET, null, Object.class);
    }
}
