package com.dbauth.gateway.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Gateway'de diğer modüllerin JWT filter'larını devre dışı bırakır
 */
@Configuration
public class ModuleSecurityDisabler {

    /**
     * Server Management modülünün JWT filter'ını devre dışı bırak
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "server-management.security.jwt-enabled", havingValue = "false", matchIfMissing = true)
    public Object disableServerManagementJwtFilter() {
        // Server management JWT filter'ını devre dışı bırak
        System.setProperty("server-management.security.jwt-enabled", "false");
        return new Object();
    }

    /**
     * User Management modülünün JWT filter'ını devre dışı bırak
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "user-management.security.jwt-enabled", havingValue = "false", matchIfMissing = true)
    public Object disableUserManagementJwtFilter() {
        // User management JWT filter'ını devre dışı bırak
        System.setProperty("user-management.security.jwt-enabled", "false");
        return new Object();
    }
}
