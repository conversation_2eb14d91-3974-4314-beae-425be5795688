package com.dbauth.gateway;

import com.dbauth.gateway.config.GatewayProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;


/**
 * Gateway Application - Dış dünyadan gelen tüm istekleri karşılar
 * Tek giriş noktası (Single Entry Point) olarak çalışır
 */
@SpringBootApplication
@EnableAsync
@EnableConfigurationProperties({
    GatewayProperties.class
})
@ComponentScan(basePackages = {
    "com.dbauth.gateway",
    "com.dbauth.shared"
})
@EntityScan(basePackages = {
    "com.dbauth.gateway.entity"
})
@EnableJpaRepositories(basePackages = {
    "com.dbauth.gateway.repository"
})
@Slf4j
public class GatewayApplication {



    public static void main(String[] args) {
        // Server adını sistem property olarak ayarla (loglama için)
        System.setProperty("server.name", "gateway");

        // JWT authentication'ı aktif et
        System.setProperty("gateway.security.jwt-enabled", "true");

        log.info("Starting Gateway Application...");
        SpringApplication.run(GatewayApplication.class, args);
        log.info("Gateway Application started successfully!");
    }
}
