package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Metric Test Proxy Controller
 * Routes requests to server-management service
 */
@RestController
@RequestMapping("/api/metric-test")
@Tag(name = "Metric Testing", description = "Metric testing operations")
public class MetricTestController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.server-management-url}")
    private String serverManagementUrl;

    @PostMapping("/query")
    @Operation(summary = "Test metric query", description = "Test a metric query with provided data")
    public ResponseEntity<?> testMetricQuery(@RequestBody Map<String, Object> testData) {
        String url = serverManagementUrl + "/api/metric-test/query";
        return restTemplateService.exchange(url, HttpMethod.POST, testData, Object.class);
    }

    @PostMapping("/query/{queryId}/validate")
    @Operation(summary = "Validate metric query", description = "Validate a metric query by ID")
    public ResponseEntity<?> validateMetricQuery(@PathVariable String queryId) {
        String url = serverManagementUrl + "/api/metric-test/query/" + queryId + "/validate";
        return restTemplateService.exchange(url, HttpMethod.POST, null, Object.class);
    }

    @PostMapping("/server/{serverId}/query")
    @Operation(summary = "Test query on server", description = "Test a query on a specific server")
    public ResponseEntity<?> testQueryOnServer(
            @PathVariable String serverId, 
            @RequestBody Map<String, Object> queryData) {
        String url = serverManagementUrl + "/api/metric-test/server/" + serverId + "/query";
        return restTemplateService.exchange(url, HttpMethod.POST, queryData, Object.class);
    }

    @PostMapping("/connection")
    @Operation(summary = "Test database connection", description = "Test database connection with provided credentials")
    public ResponseEntity<?> testDatabaseConnection(@RequestBody Map<String, Object> connectionData) {
        String url = serverManagementUrl + "/api/metric-test/connection";
        return restTemplateService.exchange(url, HttpMethod.POST, connectionData, Object.class);
    }
}
