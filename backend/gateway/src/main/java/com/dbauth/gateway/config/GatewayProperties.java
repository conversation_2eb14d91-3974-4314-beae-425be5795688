package com.dbauth.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Gateway Configuration Properties
 * Gateway modülü için dinamik konfigürasyon değerleri
 */
@Data
@ConfigurationProperties(prefix = "gateway")
public class GatewayProperties {

    /**
     * Server Management modülü URL'i
     * Varsayılan: "http://localhost:8082"
     */
    private String serverManagementUrl = "http://localhost:8082";

    /**
     * User Management modülü URL'i
     * Varsayılan: "http://localhost:8081"
     */
    private String userManagementUrl = "http://localhost:8081";

    /**
     * HTTP client timeout ayarları
     */
    private HttpClient httpClient = new HttpClient();

    /**
     * API rate limiting ayarları
     */
    private RateLimit rateLimit = new RateLimit();

    /**
     * Security ayarları
     */
    private Security security = new Security();

    @Data
    public static class HttpClient {
        /**
         * Connection timeout (milisaniye)
         * Varsayılan: 10000 (10 saniye)
         */
        private int connectionTimeoutMs = 10000;

        /**
         * Read timeout (milisaniye)
         * Varsayılan: 30000 (30 saniye)
         */
        private int readTimeoutMs = 30000;

        /**
         * Write timeout (milisaniye)
         * Varsayılan: 30000 (30 saniye)
         */
        private int writeTimeoutMs = 30000;

        /**
         * Maksimum retry sayısı
         * Varsayılan: 3
         */
        private int maxRetries = 3;

        /**
         * Connection pool boyutu
         * Varsayılan: 20
         */
        private int maxConnections = 20;
    }

    @Data
    public static class RateLimit {
        /**
         * Rate limiting aktif/pasif
         * Varsayılan: false
         */
        private boolean enabled = false;

        /**
         * Dakika başına maksimum istek sayısı
         * Varsayılan: 1000
         */
        private int requestsPerMinute = 1000;

        /**
         * Burst limit
         * Varsayılan: 100
         */
        private int burstLimit = 100;
    }

    @Data
    public static class Security {
        /**
         * Basic auth aktif/pasif
         * Varsayılan: true
         */
        private boolean basicAuthEnabled = true;

        /**
         * Basic auth username
         * Varsayılan: "user"
         */
        private String basicAuthUsername = "user";

        /**
         * Basic auth password
         * Varsayılan: "df1a873a-b5b4-4baa-8437-f85c840829a3"
         */
        private String basicAuthPassword = "df1a873a-b5b4-4baa-8437-f85c840829a3";

        /**
         * JWT aktif/pasif
         * Varsayılan: false
         */
        private boolean jwtEnabled = false;

        /**
         * JWT secret key
         */
        private String jwtSecret = "your-secret-key-here";

        /**
         * JWT token expiry (saniye)
         * Varsayılan: 3600 (1 saat)
         */
        private int jwtExpirySeconds = 3600;
    }
}
