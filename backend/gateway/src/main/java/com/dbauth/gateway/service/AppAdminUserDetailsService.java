package com.dbauth.gateway.service;

import com.dbauth.gateway.entity.AppAdmin;
import com.dbauth.gateway.repository.AppAdminRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * App Admin User Details Service
 * Spring Security için UserDetailsService implementasyonu
 */
@Service
public class AppAdminUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(AppAdminUserDetailsService.class);

    private final AppAdminRepository appAdminRepository;

    public AppAdminUserDetailsService(AppAdminRepository appAdminRepository) {
        this.appAdminRepository = appAdminRepository;
    }

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        logger.debug("Loading user by email: {}", email);

        AppAdmin appAdmin = appAdminRepository.findByEmail(email)
                .orElseThrow(() -> {
                    logger.warn("User not found with email: {}", email);
                    return new UsernameNotFoundException("User not found with email: " + email);
                });

        if (!appAdmin.getIsActive()) {
            logger.warn("User account is disabled: {}", email);
            throw new UsernameNotFoundException("User account is disabled: " + email);
        }

        logger.debug("User loaded successfully: {}", email);
        return appAdmin;
    }
}
