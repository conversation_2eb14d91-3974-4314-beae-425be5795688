package com.dbauth.gateway.service;

import com.dbauth.gateway.dto.AuthRequest;
import com.dbauth.gateway.dto.AuthResponse;
import com.dbauth.gateway.dto.UserInfoResponse;
import com.dbauth.gateway.entity.AppAdmin;
import com.dbauth.gateway.repository.AppAdminRepository;
import com.dbauth.shared.security.JwtUtil;
import com.dbauth.gateway.config.GatewayProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Authentication Service
 * Login, Register ve kullanıcı bilgileri işlemleri
 */
@Service
@Transactional
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);

    private final AppAdminRepository appAdminRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final AuthenticationManager authenticationManager;
    private final GatewayProperties gatewayProperties;

    public AuthService(AppAdminRepository appAdminRepository,
                      PasswordEncoder passwordEncoder,
                      JwtUtil jwtUtil,
                      AuthenticationManager authenticationManager,
                      GatewayProperties gatewayProperties) {
        this.appAdminRepository = appAdminRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtUtil = jwtUtil;
        this.authenticationManager = authenticationManager;
        this.gatewayProperties = gatewayProperties;
    }

    /**
     * Kullanıcı girişi
     */
    public AuthResponse login(AuthRequest authRequest) {
        logger.info("Login attempt for email: {}", authRequest.getEmail());

        try {
            // Spring Security ile authentication
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            authRequest.getEmail(),
                            authRequest.getPassword()
                    )
            );

            // Kullanıcıyı veritabanından getir
            AppAdmin appAdmin = appAdminRepository.findByEmail(authRequest.getEmail())
                    .orElseThrow(() -> new UsernameNotFoundException("User not found"));

            // Son giriş zamanını güncelle
            appAdmin.updateLastLogin();
            appAdminRepository.save(appAdmin);

            // JWT token oluştur
            String token = jwtUtil.generateToken(appAdmin.getEmail(), appAdmin.getEmail());

            // Token expiry zamanını hesapla (GatewayProperties'den al)
            LocalDateTime expiresAt = LocalDateTime.now().plusSeconds(
                    gatewayProperties.getSecurity().getJwtExpirySeconds()
            );

            logger.info("Login successful for email: {}", authRequest.getEmail());

            return new AuthResponse(
                    token,
                    appAdmin.getEmail(),
                    appAdmin.getFirstName(),
                    appAdmin.getLastName(),
                    appAdmin.getFullName(),
                    expiresAt
            );

        } catch (BadCredentialsException e) {
            logger.warn("Login failed for email: {} - Invalid credentials", authRequest.getEmail());
            throw new BadCredentialsException("Invalid email or password");
        } catch (Exception e) {
            logger.error("Login failed for email: {} - {}", authRequest.getEmail(), e.getMessage());
            throw new RuntimeException("Login failed: " + e.getMessage());
        }
    }

    /**
     * Yeni kullanıcı kaydı
     */
    public AuthResponse register(AuthRequest authRequest) {
        logger.info("Registration attempt for email: {}", authRequest.getEmail());

        // Email zaten var mı kontrol et
        if (appAdminRepository.existsByEmail(authRequest.getEmail())) {
            logger.warn("Registration failed - Email already exists: {}", authRequest.getEmail());
            throw new RuntimeException("Email already exists");
        }

        try {
            // Yeni admin oluştur
            AppAdmin appAdmin = new AppAdmin();
            appAdmin.setEmail(authRequest.getEmail());
            appAdmin.setPassword(passwordEncoder.encode(authRequest.getPassword()));
            appAdmin.setFirstName(authRequest.getFirstName());
            appAdmin.setLastName(authRequest.getLastName());
            appAdmin.setIsActive(true);

            // Kaydet
            appAdmin = appAdminRepository.save(appAdmin);

            // JWT token oluştur
            String token = jwtUtil.generateToken(appAdmin.getEmail(), appAdmin.getEmail());

            // Token expiry zamanını hesapla
            LocalDateTime expiresAt = LocalDateTime.now().plusSeconds(
                    gatewayProperties.getSecurity().getJwtExpirySeconds()
            );

            logger.info("Registration successful for email: {}", authRequest.getEmail());

            return new AuthResponse(
                    token,
                    appAdmin.getEmail(),
                    appAdmin.getFirstName(),
                    appAdmin.getLastName(),
                    appAdmin.getFullName(),
                    expiresAt
            );

        } catch (Exception e) {
            logger.error("Registration failed for email: {} - {}", authRequest.getEmail(), e.getMessage());
            throw new RuntimeException("Registration failed: " + e.getMessage());
        }
    }

    /**
     * Kullanıcı bilgilerini getir
     */
    public UserInfoResponse getUserInfo(String email) {
        logger.debug("Getting user info for email: {}", email);

        AppAdmin appAdmin = appAdminRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));

        return new UserInfoResponse(
                appAdmin.getId(),
                appAdmin.getEmail(),
                appAdmin.getFirstName(),
                appAdmin.getLastName(),
                appAdmin.getFullName(),
                appAdmin.getIsActive(),
                appAdmin.getCreatedAt(),
                appAdmin.getLastLoginAt()
        );
    }
}
