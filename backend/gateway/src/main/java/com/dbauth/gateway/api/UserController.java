package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * User Management Proxy Controller
 * Routes requests to user-management service
 */
@RestController
@RequestMapping("/api/users")
@Tag(name = "User Management", description = "User management operations")
public class UserController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.user-management-url}")
    private String userManagementUrl;

    @GetMapping
    @Operation(summary = "Get all users", description = "Retrieve all users")
    public ResponseEntity<?> getAllUsers() {
        String url = userManagementUrl + "/api/users";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PostMapping
    @Operation(summary = "Create user", description = "Create a new user")
    public ResponseEntity<?> createUser(@RequestBody Map<String, Object> userData) {
        String url = userManagementUrl + "/api/users";
        return restTemplateService.exchange(url, HttpMethod.POST, userData, Object.class);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get user by ID", description = "Retrieve a specific user by ID")
    public ResponseEntity<?> getUserById(@PathVariable String id) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user", description = "Update an existing user")
    public ResponseEntity<?> updateUser(@PathVariable String id, @RequestBody Map<String, Object> userData) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchange(url, HttpMethod.PUT, userData, Object.class);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete user", description = "Delete a user")
    public ResponseEntity<?> deleteUser(@PathVariable String id) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchange(url, HttpMethod.DELETE, null, Object.class);
    }
}
