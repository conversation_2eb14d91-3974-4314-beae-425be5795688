package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Server Management Proxy Controller
 * Routes requests to server-management service
 */
@RestController
@RequestMapping("/api/servers")
@Tag(name = "Server Management", description = "Server management operations")
public class ServerController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.server-management-url}")
    private String serverManagementUrl;

    @GetMapping
    @Operation(summary = "Get all servers", description = "Retrieve all database servers")
    public ResponseEntity<?> getAllServers() {
        String url = serverManagementUrl + "/api/servers";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get server by ID", description = "Retrieve a specific server by its ID")
    public ResponseEntity<?> getServerById(@PathVariable String id) {
        String url = serverManagementUrl + "/api/servers/" + id;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PostMapping
    @Operation(summary = "Create server", description = "Create a new database server")
    public ResponseEntity<?> createServer(@RequestBody Map<String, Object> serverData) {
        String url = serverManagementUrl + "/api/servers";
        return restTemplateService.exchange(url, HttpMethod.POST, serverData, Object.class);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update server", description = "Update an existing server")
    public ResponseEntity<?> updateServer(@PathVariable String id, @RequestBody Map<String, Object> serverData) {
        String url = serverManagementUrl + "/api/servers/" + id;
        return restTemplateService.exchange(url, HttpMethod.PUT, serverData, Object.class);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete server", description = "Delete a server")
    public ResponseEntity<?> deleteServer(@PathVariable String id) {
        String url = serverManagementUrl + "/api/servers/" + id;
        return restTemplateService.exchange(url, HttpMethod.DELETE, null, Object.class);
    }

    @PostMapping("/{id}/test-connection")
    @Operation(summary = "Test server connection", description = "Test connection to a server")
    public ResponseEntity<?> testConnection(@PathVariable String id) {
        String url = serverManagementUrl + "/api/servers/" + id + "/test-connection";
        return restTemplateService.exchange(url, HttpMethod.POST, null, Object.class);
    }

    @PostMapping("/test-connection")
    @Operation(summary = "Test connection with data", description = "Test connection with provided server data")
    public ResponseEntity<?> testConnectionWithData(@RequestBody Map<String, Object> serverData) {
        String url = serverManagementUrl + "/api/servers/test-connection";
        return restTemplateService.exchange(url, HttpMethod.POST, serverData, Object.class);
    }

    @PatchMapping("/{id}/status")
    @Operation(summary = "Toggle server status", description = "Toggle server active/inactive status")
    public ResponseEntity<?> toggleServerStatus(@PathVariable String id) {
        String url = serverManagementUrl + "/api/servers/" + id + "/status";
        return restTemplateService.exchange(url, HttpMethod.PATCH, null, Object.class);
    }

    @GetMapping("/{id}/details")
    @Operation(summary = "Get server details", description = "Get detailed server information including metrics")
    public ResponseEntity<?> getServerDetails(@PathVariable String id) {
        String url = serverManagementUrl + "/api/servers/" + id + "/details";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }
}
