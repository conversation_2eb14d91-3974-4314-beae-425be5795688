package com.dbauth.gateway.repository;

import com.dbauth.gateway.entity.AppAdmin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * App Admin Repository
 * Uygulama yöneticileri için repository
 */
@Repository
public interface AppAdminRepository extends JpaRepository<AppAdmin, UUID> {

    /**
     * Email'e göre admin bul
     */
    Optional<AppAdmin> findByEmail(String email);

    /**
     * Email'in var olup olmadığını kontrol et
     */
    boolean existsByEmail(String email);

    /**
     * Aktif admin'leri getir
     */
    @Query("SELECT a FROM AppAdmin a WHERE a.isActive = true")
    java.util.List<AppAdmin> findAllActive();

    /**
     * Admin'in son giri<PERSON> z<PERSON>ını güncelle
     */
    @Modifying
    @Query("UPDATE AppAdmin a SET a.lastLoginAt = :loginTime WHERE a.id = :adminId")
    void updateLastLoginTime(@Param("adminId") UUID adminId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * Admin'i aktif/pasif yap
     */
    @Modifying
    @Query("UPDATE AppAdmin a SET a.isActive = :isActive WHERE a.id = :adminId")
    void updateActiveStatus(@Param("adminId") UUID adminId, @Param("isActive") Boolean isActive);
}
