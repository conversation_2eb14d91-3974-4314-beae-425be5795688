# Gateway - API Gateway Service

## Proje Açıklaması

Gateway projesi, tüm dış isteklerin tek giriş noktası olan API Gateway servisidir.

## Özellikler

- **Port**: 8080
- **Rol**: API Proxy/Gateway
- **Database**: Shared database kullanır (kendi tablosu yok)
- **Flyway**: Kullanmaz (migration ihtiyacı yok)

## Konfigürasyon

```yaml
server:
  port: 8080

spring:
  application:
    name: gateway
  
  datasource:
    url: ****************************************
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: password
```

## Neden Flyway Yok?

Gateway projesi:
- <PERSON>di veritabanı tablosu oluşturmuyor
- Sadece mevcut tabloları okuyup API proxy görevi görüyor
- Migration dosyasına ihtiyacı yok
- User Management ve Server Management'ın tablolarını kullanır

## Bağımlılıklar

Gateway aşağıdaki servislere bağımlıdır:
- **User Management**: User işlemleri için
- **PostgreSQL**: Shared database

## Çalıştırma

```bash
cd backend/gateway
mvn spring-boot:run
```

Gateway http://localhost:8080 adresinde çalışır.
