# Test Environment Configuration

server:
  port: 8081

spring:
  application:
    name: user-management-test
  
  # Test Database (Local PostgreSQL)
  datasource:
    url: *********************************************
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: password
    
  # JPA Configuration for Test
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create-drop  # Recreate tables for each test
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true

# Gateway Configuration for Test
gateway:
  url: http://localhost:8080
  security:
    # JWT authentication devre dışı test için
    jwt-enabled: false
    # JWT secret key - Gateway ile aynı olmalı
    jwt-secret: test-jwt-secret-key-for-test-environment-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-exactly-64-chars
    # JWT token expiration time (milisaniye) - 1 gün
    jwt-expiration: 86400000

# User Management Configuration for Test
user:
  management:
    # Application info
    application-name: user-management-test
    application-version: 1.0.0-test
    
    # Permission cleanup configuration
    permission-cleanup:
      enabled: false  # Disable cleanup in tests
      cron: "0 0 2 * * ?"
    
    # Logging configuration
    logging:
      show-sql: true
      format-sql: true
      use-sql-comments: true
      debug-logging: true
      log-path: "logs/user-management-test"

# Test Logging
logging:
  level:
    com.dbauth.user: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web.filter: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"

# Swagger Configuration for Test
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# Actuator for Test
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: always

# Test specific properties
app:
  environment: test
  debug-mode: true
  mock-external-services: true

# Security Configuration for Test
security:
  # JWT authentication devre dışı
  jwt:
    enabled: false
  # Basic auth devre dışı
  basic-auth:
    enabled: false
