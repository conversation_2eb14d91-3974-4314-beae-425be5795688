package com.dbauth.user.integration;

import com.dbauth.user.UserManagementApplication;
import com.dbauth.user.application.dto.CreateUserRequest;
import com.dbauth.user.application.dto.UpdateUserRequest;
import com.dbauth.user.config.TestSecurityConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * User Management Integration Tests
 * Tests all API endpoints with real PostgreSQL database
 */
@SpringBootTest(classes = UserManagementApplication.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ContextConfiguration(classes = {UserManagementApplication.class, TestSecurityConfig.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
public class UserManagementIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static JdbcTemplate staticJdbcTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private CreateUserRequest createUserRequest;

    @BeforeEach
    void setUp() {
        // Test user data
        createUserRequest = new CreateUserRequest("Test", "User", "testuser", "<EMAIL>");
    }

    // ==================== USER CONTROLLER TESTS ====================

    @Test
    void shouldCreateUser() throws Exception {
        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("Test"))
                .andExpect(jsonPath("$.lastName").value("User"))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.createdAt").exists());
    }

    @Test
    void shouldGetAllUsers() throws Exception {
        // Create user first
        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated());

        // Get all users
        mockMvc.perform(get("/api/users"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].username").value("testuser"));
    }

    @Test
    void shouldGetUserById() throws Exception {
        // Create user first
        MvcResult createResult = mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        String userId = objectMapper.readTree(createResponseContent).get("id").asText();

        // Get user by ID
        mockMvc.perform(get("/api/users/{id}", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    @Test
    void shouldUpdateUser() throws Exception {
        // Create user first
        MvcResult createResult = mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        String userId = objectMapper.readTree(createResponseContent).get("id").asText();

        // Update user
        UpdateUserRequest updateRequest = new UpdateUserRequest("Updated", "User", "testuser", "<EMAIL>", true);

        mockMvc.perform(put("/api/users/{id}", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.firstName").value("Updated"))
                .andExpect(jsonPath("$.lastName").value("User"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    @Test
    void shouldDeleteUser() throws Exception {
        // Create user first
        MvcResult createResult = mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        String userId = objectMapper.readTree(createResponseContent).get("id").asText();

        // Delete user
        mockMvc.perform(delete("/api/users/{id}", userId))
                .andExpect(status().isNoContent());

        // Verify user is deleted
        mockMvc.perform(get("/api/users/{id}", userId))
                .andExpect(status().isNotFound());
    }

    // ==================== VALIDATION TESTS ====================

    @Test
    void shouldHandleUserValidationErrors() throws Exception {
        CreateUserRequest invalidRequest = new CreateUserRequest("", "", "", "invalid-email");

        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldHandleUserNotFound() throws Exception {
        UUID nonExistentId = UUID.randomUUID();

        mockMvc.perform(get("/api/users/{id}", nonExistentId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldHandleDuplicateUsername() throws Exception {
        // Create first user
        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated());

        // Try to create user with same username
        CreateUserRequest duplicateRequest = new CreateUserRequest("Another", "User", "testuser", "<EMAIL>");

        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(duplicateRequest)))
                .andExpect(status().isConflict());
    }

    @Test
    void shouldHandleDuplicateEmail() throws Exception {
        // Create first user
        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isCreated());

        // Try to create user with same email
        CreateUserRequest duplicateRequest = new CreateUserRequest("Another", "User", "anotheruser", "<EMAIL>");

        mockMvc.perform(post("/api/users")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(duplicateRequest)))
                .andExpect(status().isConflict());
    }

    @BeforeEach
    void cleanupDatabase() {
        try {
            // Clean up test data
            jdbcTemplate.execute("DELETE FROM users");
            jdbcTemplate.execute("DELETE FROM user_permissions"); // Eğer başka bir tablo varsa, onu da temizle

            // Reset sequences if needed
            jdbcTemplate.execute("ALTER SEQUENCE IF EXISTS users_id_seq RESTART WITH 1");
            jdbcTemplate.execute("ALTER SEQUENCE IF EXISTS user_permissions_id_seq RESTART WITH 1"); // Eğer başka bir tablo varsa, onun da sırasını sıfırla

            System.out.println("✅ Test database cleaned up successfully");
        } catch (Exception e) {
            System.err.println("❌ Error cleaning up test database: " + e.getMessage());
        }
    }
}
