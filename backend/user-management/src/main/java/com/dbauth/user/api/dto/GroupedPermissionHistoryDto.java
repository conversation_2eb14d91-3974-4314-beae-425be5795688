package com.dbauth.user.api.dto;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Grouped Permission History DTO
 * Kullanıcı ve sunucu bazında gruplu yetki geçmişi bilgilerini taşır
 */
public class GroupedPermissionHistoryDto {
    
    private UUID id;
    private UUID userId;
    private String firstName;
    private String lastName;
    private String email;
    private UUID serverId;
    private String serverName;
    private String databaseType;
    private String roleNames; // Virgülle ayrılmış rol isimleri
    private LocalDateTime firstGrantedAt;
    private LocalDateTime lastActionAt;
    private long totalPermissions;
    private long activePermissions;
    private long revokedPermissions;
    private long expiredPermissions;
    private String lastAction; // Son yapılan işlem
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Default constructor
    public GroupedPermissionHistoryDto() {}

    // Full constructor
    public GroupedPermissionHistoryDto(UUID id, UUID userId, String firstName, String lastName, String email,
                                      UUID serverId, String serverName, String databaseType, String roleNames,
                                      LocalDateTime firstGrantedAt, LocalDateTime lastActionAt, long totalPermissions,
                                      long activePermissions, long revokedPermissions, long expiredPermissions,
                                      String lastAction, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.serverId = serverId;
        this.serverName = serverName;
        this.databaseType = databaseType;
        this.roleNames = roleNames;
        this.firstGrantedAt = firstGrantedAt;
        this.lastActionAt = lastActionAt;
        this.totalPermissions = totalPermissions;
        this.activePermissions = activePermissions;
        this.revokedPermissions = revokedPermissions;
        this.expiredPermissions = expiredPermissions;
        this.lastAction = lastAction;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public UUID getServerId() {
        return serverId;
    }

    public void setServerId(UUID serverId) {
        this.serverId = serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public LocalDateTime getFirstGrantedAt() {
        return firstGrantedAt;
    }

    public void setFirstGrantedAt(LocalDateTime firstGrantedAt) {
        this.firstGrantedAt = firstGrantedAt;
    }

    public LocalDateTime getLastActionAt() {
        return lastActionAt;
    }

    public void setLastActionAt(LocalDateTime lastActionAt) {
        this.lastActionAt = lastActionAt;
    }

    public long getTotalPermissions() {
        return totalPermissions;
    }

    public void setTotalPermissions(long totalPermissions) {
        this.totalPermissions = totalPermissions;
    }

    public long getActivePermissions() {
        return activePermissions;
    }

    public void setActivePermissions(long activePermissions) {
        this.activePermissions = activePermissions;
    }

    public long getRevokedPermissions() {
        return revokedPermissions;
    }

    public void setRevokedPermissions(long revokedPermissions) {
        this.revokedPermissions = revokedPermissions;
    }

    public long getExpiredPermissions() {
        return expiredPermissions;
    }

    public void setExpiredPermissions(long expiredPermissions) {
        this.expiredPermissions = expiredPermissions;
    }

    public String getLastAction() {
        return lastAction;
    }

    public void setLastAction(String lastAction) {
        this.lastAction = lastAction;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
