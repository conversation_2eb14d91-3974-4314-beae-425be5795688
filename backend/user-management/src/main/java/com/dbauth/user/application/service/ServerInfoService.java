package com.dbauth.user.application.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.dbauth.shared.security.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Server Information Service
 * Server Management modülünden server bilgilerini alır
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ServerInfoService {

    private final RestTemplate restTemplate;
    private final JwtUtil jwtUtil;
    private final DatabaseConnectionService databaseConnectionService;

    @Value("${gateway.server-management-url:http://localhost:8082}")
    private String serverManagementUrl;

    /**
     * Server bilgilerini server-management modülünden alır
     */
    public ServerInfo getServerInfo(UUID serverId) {
        try {
            String url = serverManagementUrl + "/api/servers/" + serverId;

            // JWT token'ı header'a ekle
            HttpHeaders headers = new HttpHeaders();
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getName() != null) {
                String token = jwtUtil.generateToken(authentication.getName());
                headers.set("Authorization", "Bearer " + token);
            }

            HttpEntity<?> entity = new HttpEntity<>(headers);
            ResponseEntity<ServerInfo> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                ServerInfo.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new IllegalArgumentException("Server not found: " + serverId);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to fetch server info for ID: " + serverId, e);
        }
    }

    /**
     * Server'ın aktif olup olmadığını kontrol eder
     */
    public boolean isServerActive(UUID serverId) {
        try {
            ServerInfo serverInfo = getServerInfo(serverId);
            return serverInfo.getIsActive() != null && serverInfo.getIsActive();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Server'a database connection oluşturur
     */
    public Connection getConnection(UUID serverId) throws SQLException {
        try {
            ServerInfo serverInfo = getServerInfo(serverId);

            log.debug("Creating connection to server: {} ({}:{})",
                serverInfo.getName(), serverInfo.getHost(), serverInfo.getPort());

            // Default database name'i belirle
            String databaseName = getDatabaseName(serverInfo.getDatabaseType());

            return databaseConnectionService.createConnection(
                serverInfo.getHost(),
                serverInfo.getPort(),
                databaseName,
                serverInfo.getUsername(),
                serverInfo.getPassword(),
                serverInfo.getDatabaseType()
            );

        } catch (Exception e) {
            log.error("Failed to create connection for server: {}", serverId, e);
            throw new SQLException("Failed to create database connection", e);
        }
    }

    /**
     * Database type'a göre default database name'i döner
     */
    private String getDatabaseName(String databaseType) {
        if (databaseType == null) {
            return "master";
        }

        switch (databaseType.toUpperCase()) {
            case "SQL_SERVER":
                return "master";
            case "POSTGRESQL":
                return "postgres";
            case "MYSQL":
                return "mysql";
            default:
                return "master";
        }
    }
}
