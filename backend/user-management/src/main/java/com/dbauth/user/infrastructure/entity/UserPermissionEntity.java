package com.dbauth.user.infrastructure.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * User Permission JPA Entity - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında JPA entity
 * Clean Code: Database mapping for user permissions
 */
@Entity
@Table(name = "user_permissions", indexes = {
        @Index(name = "idx_user_permissions_user_id", columnList = "user_id"),
        @Index(name = "idx_user_permissions_server_id", columnList = "server_id"),
        @Index(name = "idx_user_permissions_role_name", columnList = "role_name"),
        @Index(name = "idx_user_permissions_expires_at", columnList = "expires_at"),
        @Index(name = "idx_user_permissions_is_active", columnList = "is_active"),
        @Index(name = "idx_user_permissions_user_server_role", columnList = "user_id, server_id, role_name")
})
public class UserPermissionEntity {

    @Id
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "server_id", nullable = false)
    private UUID serverId;

    @Column(name = "role_name", nullable = false, length = 100)
    private String roleName;

    @Column(name = "granted_at", nullable = false)
    private LocalDateTime grantedAt;

    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;

    @Column(name = "is_active", nullable = false)
    private boolean isActive;

    @Column(name = "sql_query", columnDefinition = "TEXT")
    private String sqlQuery;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // Default constructor for JPA
    public UserPermissionEntity() {
    }

    public UserPermissionEntity(UUID id, UUID userId, UUID serverId, String roleName,
                              LocalDateTime grantedAt, LocalDateTime expiresAt, boolean isActive) {
        this.id = id;
        this.userId = userId;
        this.serverId = serverId;
        this.roleName = roleName;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
        this.deletedAt = null; // Yeni oluşturulan yetkiler silinmemiş
    }

    public UserPermissionEntity(UUID id, UUID userId, UUID serverId, String roleName,
                              LocalDateTime grantedAt, LocalDateTime expiresAt, boolean isActive, String sqlQuery) {
        this.id = id;
        this.userId = userId;
        this.serverId = serverId;
        this.roleName = roleName;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
        this.sqlQuery = sqlQuery;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public UUID getServerId() {
        return serverId;
    }

    public void setServerId(UUID serverId) {
        this.serverId = serverId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public void setGrantedAt(LocalDateTime grantedAt) {
        this.grantedAt = grantedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getSqlQuery() {
        return sqlQuery;
    }

    public void setSqlQuery(String sqlQuery) {
        this.sqlQuery = sqlQuery;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserPermissionEntity that = (UserPermissionEntity) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        return "UserPermissionEntity{" +
                "id=" + id +
                ", userId=" + userId +
                ", serverId=" + serverId +
                ", roleName='" + roleName + '\'' +
                ", grantedAt=" + grantedAt +
                ", expiresAt=" + expiresAt +
                ", isActive=" + isActive +
                ", sqlQuery='" + sqlQuery + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", deletedAt=" + deletedAt +
                '}';
    }
}
