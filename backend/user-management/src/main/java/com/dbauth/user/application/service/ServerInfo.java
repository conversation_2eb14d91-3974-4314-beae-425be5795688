package com.dbauth.user.application.service;

import java.util.UUID;

/**
 * Server Information DTO
 * Hedef sunucu bilgilerini taşır
 */
public class ServerInfo {
    
    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private String databaseType;
    private String username;
    private String password;
    private String description;
    private Boolean isActive;
    
    // Constructors
    public ServerInfo() {}
    
    public ServerInfo(UUID id, String name, String host, Integer port, String databaseType,
                     String username, String password) {
        this.id = id;
        this.name = name;
        this.host = host;
        this.port = port;
        this.databaseType = databaseType;
        this.username = username;
        this.password = password;
        this.isActive = true;
    }
    
    // Getters and Setters
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public Integer getPort() {
        return port;
    }
    
    public void setPort(Integer port) {
        this.port = port;
    }
    
    public String getDatabaseType() {
        return databaseType;
    }
    
    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }
    

    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "ServerInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", databaseType='" + databaseType + '\'' +
                ", username='" + username + '\'' +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
