package com.dbauth.user.application.service;

import com.dbauth.user.application.dto.CreateUserRequest;
import com.dbauth.user.application.dto.UpdateUserRequest;
import com.dbauth.user.application.dto.UserDto;

import java.util.List;
import java.util.UUID;

/**
 * User Service Interface - Application katmanında tanımlanır
 * İş mantığı operasyonlarını tanımlar
 */
public interface UserService {
    
    /**
     * Yeni kullanıcı oluştur
     */
    UserDto createUser(CreateUserRequest request);
    
    /**
     * Kullanıcıyı ID ile getir
     */
    UserDto getUserById(UUID id);

    /**
     * Kullanıcıyı username ile getir
     */
    UserDto getUserByUsername(String username);

    /**
     * Tüm kullanıcıları listele
     */
    List<UserDto> getAllUsers();

    /**
     * Kullanıcıyı güncelle
     */
    UserDto updateUser(UUID id, UpdateUserRequest request);

    /**
     * Kullanıcıyı sil
     */
    void deleteUser(UUID id);

    /**
     * Kullanıcının var olup olmadığını kontrol et
     */
    boolean userExists(UUID id);
    
    /**
     * Username'in kullanılıp kullanılmadığını kontrol et
     */
    boolean usernameExists(String username);
    
    /**
     * Email'in kullanılıp kullanılmadığını kontrol et
     */
    boolean emailExists(String email);
}
