package com.dbauth.user.domain.repository;

import com.dbauth.user.application.dto.GroupedUserPermissionDto;
import com.dbauth.user.api.dto.GroupedPermissionHistoryDto;
import com.dbauth.user.api.dto.PermissionHistoryDto;
import com.dbauth.user.domain.entity.UserPermission;
import com.dbauth.user.domain.valueobject.DatabaseRole;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * User Permission Repository Interface
 * Onion Architecture: Domain katmanında repository interface
 * Clean Code: Repository pattern for data access abstraction
 */
public interface UserPermissionRepository {
    
    /**
     * Save user permission
     */
    UserPermission save(UserPermission userPermission);
    
    /**
     * Find user permission by ID
     */
    Optional<UserPermission> findById(UUID id);
    
    /**
     * Find all user permissions
     */
    List<UserPermission> findAll();
    
    /**
     * Find permissions by user ID
     */
    List<UserPermission> findByUserId(UUID userId);
    
    /**
     * Find permissions by server ID
     */
    List<UserPermission> findByServerId(UUID serverId);
    
    /**
     * Find active permissions by user ID
     */
    List<UserPermission> findActiveByUserId(UUID userId);
    
    /**
     * Find active permissions by server ID
     */
    List<UserPermission> findActiveByServerId(UUID serverId);
    
    /**
     * Find permissions by user and server
     */
    List<UserPermission> findByUserIdAndServerId(UUID userId, UUID serverId);
    
    /**
     * Find active permissions by user and server
     */
    List<UserPermission> findActiveByUserIdAndServerId(UUID userId, UUID serverId);
    
    /**
     * Find permissions by role name
     */
    List<UserPermission> findByRoleName(String roleName);
    
    /**
     * Find expired permissions
     */
    List<UserPermission> findExpiredPermissions();
    
    /**
     * Delete user permission by ID
     */
    void deleteById(UUID id);
    
    /**
     * Check if user has permission for server and role
     */
    boolean existsByUserIdAndServerIdAndRoleNameAndIsActiveTrue(UUID userId, UUID serverId, String roleName);
    
    /**
     * Get database roles from pg_roles table of the specified server
     * This method connects to the actual database server to fetch available roles
     */
    List<DatabaseRole> getDatabaseRolesFromServer(UUID serverId);

    /**
     * Get grouped permissions with user details
     */
    List<GroupedUserPermissionDto> findGroupedPermissions();

    /**
     * Get permission history with user and server details
     */
    List<PermissionHistoryDto> findPermissionHistory();

    /**
     * Get grouped permission history with user and server details
     */
    List<GroupedPermissionHistoryDto> findGroupedPermissionHistory();
}
