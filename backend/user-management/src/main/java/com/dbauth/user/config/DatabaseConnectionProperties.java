package com.dbauth.user.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Database Connection Configuration Properties
 * Onion Architecture: Configuration katmanında database connection ayarları
 * Single Responsibility: Sadece database connection ile ilgili ayarlar
 */
@ConfigurationProperties(prefix = "user.management.database")
public class DatabaseConnectionProperties {

    /**
     * Database connection timeout (milisaniye)
     * Varsayılan: 30000 (30 saniye)
     */
    private int connectionTimeoutMs = 30000;

    /**
     * Database query timeout (saniye)
     * Varsayılan: 30 saniye
     */
    private int queryTimeoutSeconds = 30;

    /**
     * Database connection pool ayarları
     */
    private Pool pool = new Pool();

    public static class Pool {
        /**
         * Maksimum pool boyutu
         * Varsayılan: 10
         */
        private int maximumPoolSize = 10;

        /**
         * Minimum idle connection sayısı
         * Varsayılan: 5
         */
        private int minimumIdle = 5;

        /**
         * Connection timeout (milisaniye)
         * Varsayılan: 20000 (20 saniye)
         */
        private long connectionTimeoutMs = 20000;

        /**
         * Idle timeout (milisaniye)
         * Varsayılan: 300000 (5 dakika)
         */
        private long idleTimeoutMs = 300000;

        /**
         * Max lifetime (milisaniye)
         * Varsayılan: 1200000 (20 dakika)
         */
        private long maxLifetimeMs = 1200000;

        // Getters and Setters
        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public void setMaximumPoolSize(int maximumPoolSize) {
            this.maximumPoolSize = maximumPoolSize;
        }

        public int getMinimumIdle() {
            return minimumIdle;
        }

        public void setMinimumIdle(int minimumIdle) {
            this.minimumIdle = minimumIdle;
        }

        public long getConnectionTimeoutMs() {
            return connectionTimeoutMs;
        }

        public void setConnectionTimeoutMs(long connectionTimeoutMs) {
            this.connectionTimeoutMs = connectionTimeoutMs;
        }

        public long getIdleTimeoutMs() {
            return idleTimeoutMs;
        }

        public void setIdleTimeoutMs(long idleTimeoutMs) {
            this.idleTimeoutMs = idleTimeoutMs;
        }

        public long getMaxLifetimeMs() {
            return maxLifetimeMs;
        }

        public void setMaxLifetimeMs(long maxLifetimeMs) {
            this.maxLifetimeMs = maxLifetimeMs;
        }
    }

    // Getters and Setters
    public int getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public void setConnectionTimeoutMs(int connectionTimeoutMs) {
        this.connectionTimeoutMs = connectionTimeoutMs;
    }

    public int getQueryTimeoutSeconds() {
        return queryTimeoutSeconds;
    }

    public void setQueryTimeoutSeconds(int queryTimeoutSeconds) {
        this.queryTimeoutSeconds = queryTimeoutSeconds;
    }

    public Pool getPool() {
        return pool;
    }

    public void setPool(Pool pool) {
        this.pool = pool;
    }
}
