package com.dbauth.user.api.dto;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Permission History DTO
 * Yetki geçmişi bilgilerini taşır
 */
public class PermissionHistoryDto {
    
    private UUID id;
    private UUID userId;
    private String firstName;
    private String lastName;
    private String email;
    private UUID serverId;
    private String serverName;
    private String databaseType;
    private String roleName;
    private String roleDescription;
    private String sqlQuery;
    private LocalDateTime grantedAt;
    private LocalDateTime expiresAt;
    private boolean isActive;
    private String action; // "GRANTED", "REVOKED", "EXPIRED"
    private LocalDateTime actionDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Default constructor
    public PermissionHistoryDto() {}

    // Full constructor
    public PermissionHistoryDto(UUID id, UUID userId, String firstName, String lastName, String email,
                               UUID serverId, String serverName, String databaseType, String roleName,
                               String roleDescription, String sqlQuery, LocalDateTime grantedAt, LocalDateTime expiresAt,
                               boolean isActive, String action, LocalDateTime actionDate,
                               LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.serverId = serverId;
        this.serverName = serverName;
        this.databaseType = databaseType;
        this.roleName = roleName;
        this.roleDescription = roleDescription;
        this.sqlQuery = sqlQuery;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
        this.action = action;
        this.actionDate = actionDate;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public UUID getServerId() {
        return serverId;
    }

    public void setServerId(UUID serverId) {
        this.serverId = serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleDescription() {
        return roleDescription;
    }

    public void setRoleDescription(String roleDescription) {
        this.roleDescription = roleDescription;
    }

    public String getSqlQuery() {
        return sqlQuery;
    }

    public void setSqlQuery(String sqlQuery) {
        this.sqlQuery = sqlQuery;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public void setGrantedAt(LocalDateTime grantedAt) {
        this.grantedAt = grantedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public LocalDateTime getActionDate() {
        return actionDate;
    }

    public void setActionDate(LocalDateTime actionDate) {
        this.actionDate = actionDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
