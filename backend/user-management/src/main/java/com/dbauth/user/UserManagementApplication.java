package com.dbauth.user;

import com.dbauth.user.config.UserManagementProperties;
import com.dbauth.user.config.DatabaseConnectionProperties;
import com.dbauth.user.config.LoggingProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * User Management Application - Kullanıcı ve yetki yönetimi
 * Onion Architecture ile tasarlanmış, temiz kod prensipleri uygulanmış
 * Clean Code: Configuration sınıfları parçalanmış, Single Responsibility uygulanmış
 */
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({
    UserManagementProperties.class,
    DatabaseConnectionProperties.class,
    LoggingProperties.class
})
@ComponentScan(basePackages = {
    "com.dbauth.user",
    "com.dbauth.shared"
})
@EntityScan(basePackages = {
    "com.dbauth.user.infrastructure.entity"
})
@EnableJpaRepositories(basePackages = {
    "com.dbauth.user.infrastructure.repository"
})
public class UserManagementApplication {

    public static void main(String[] args) {
        // Server adını sistem property olarak ayarla (loglama için)
        System.setProperty("server.name", "user-management");
        SpringApplication.run(UserManagementApplication.class, args);
    }
}
