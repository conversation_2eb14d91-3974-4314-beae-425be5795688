package com.dbauth.user.domain.entity;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * User Permission Domain Entity
 * Onion Architecture: Domain katmanında business logic
 * Clean Code: Immutable entity with business rules
 */
public class UserPermission {
    
    private final UUID id;
    private final UUID userId;
    private final UUID serverId;
    private final String roleName;
    private final LocalDateTime grantedAt;
    private final LocalDateTime expiresAt;
    private final boolean isActive;
    private final String sqlQuery;
    private final LocalDateTime createdAt;
    private final LocalDateTime updatedAt;
    private final LocalDateTime deletedAt;

    // Constructor for new permission
    public UserPermission(UUID userId, UUID serverId, String roleName, LocalDateTime expiresAt) {
        this.id = UUID.randomUUID();
        this.userId = userId;
        this.serverId = serverId;
        this.roleName = roleName;
        this.grantedAt = LocalDateTime.now();
        this.expiresAt = expiresAt;
        this.isActive = true;
        this.sqlQuery = null;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.deletedAt = null; // Yeni oluşturulan yetkiler silinmemiş
    }

    // Constructor for new permission with SQL query
    public UserPermission(UUID userId, UUID serverId, String roleName, LocalDateTime expiresAt, String sqlQuery) {
        this.id = UUID.randomUUID();
        this.userId = userId;
        this.serverId = serverId;
        this.roleName = roleName;
        this.grantedAt = LocalDateTime.now();
        this.expiresAt = expiresAt;
        this.isActive = true;
        this.sqlQuery = sqlQuery;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.deletedAt = null; // Yeni oluşturulan yetkiler silinmemiş
    }

    // Constructor for existing permission (from database)
    public UserPermission(UUID id, UUID userId, UUID serverId, String roleName,
                         LocalDateTime grantedAt, LocalDateTime expiresAt, boolean isActive,
                         String sqlQuery, LocalDateTime createdAt, LocalDateTime updatedAt, LocalDateTime deletedAt) {
        this.id = id;
        this.userId = userId;
        this.serverId = serverId;
        this.roleName = roleName;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
        this.sqlQuery = sqlQuery;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.deletedAt = deletedAt;
    }

    /**
     * Business rule: Check if permission is currently valid
     */
    public boolean isCurrentlyValid() {
        return isActive && !isExpired();
    }

    /**
     * Business rule: Check if permission is expired
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * Business rule: Revoke permission
     */
    public UserPermission revoke() {
        return new UserPermission(
            this.id,
            this.userId,
            this.serverId,
            this.roleName,
            this.grantedAt,
            this.expiresAt,
            false, // Set inactive
            this.sqlQuery,
            this.createdAt,
            LocalDateTime.now(), // Update timestamp
            this.deletedAt // Keep deletedAt as is
        );
    }

    /**
     * Business rule: Extend permission expiry
     */
    public UserPermission extendExpiry(LocalDateTime newExpiresAt) {
        if (newExpiresAt.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("New expiry date cannot be in the past");
        }

        return new UserPermission(
            this.id,
            this.userId,
            this.serverId,
            this.roleName,
            this.grantedAt,
            newExpiresAt,
            this.isActive,
            this.sqlQuery,
            this.createdAt,
            LocalDateTime.now(),
            this.deletedAt
        );
    }

    // Getters
    public UUID getId() {
        return id;
    }

    public UUID getUserId() {
        return userId;
    }

    public UUID getServerId() {
        return serverId;
    }

    public String getRoleName() {
        return roleName;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public boolean isActive() {
        return isActive;
    }

    public String getSqlQuery() {
        return sqlQuery;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    /**
     * Business rule: Activate permission
     */
    public UserPermission activate() {
        return new UserPermission(
                this.id,
                this.userId,
                this.serverId,
                this.roleName,
                this.grantedAt,
                this.expiresAt,
                true, // Aktif hale getir
                this.sqlQuery,
                this.createdAt,
                LocalDateTime.now(), // updatedAt güncelle
                this.deletedAt // deletedAt değişmez
        );
    }

    /**
     * Business rule: Mark permission as deleted (soft delete)
     */
    public UserPermission delete() {
        return new UserPermission(
                this.id,
                this.userId,
                this.serverId,
                this.roleName,
                this.grantedAt,
                this.expiresAt,
                false, // Silinen yetki aktif değil
                this.sqlQuery,
                this.createdAt,
                LocalDateTime.now(), // updatedAt güncelle
                LocalDateTime.now() // deletedAt set et
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserPermission that = (UserPermission) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        return "UserPermission{" +
                "id=" + id +
                ", userId=" + userId +
                ", serverId=" + serverId +
                ", roleName='" + roleName + '\'' +
                ", grantedAt=" + grantedAt +
                ", expiresAt=" + expiresAt +
                ", isActive=" + isActive +
                ", sqlQuery='" + sqlQuery + '\'' +
                ", deletedAt=" + deletedAt +
                '}';
    }
}
