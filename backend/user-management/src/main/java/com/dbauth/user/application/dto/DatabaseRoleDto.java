package com.dbauth.user.application.dto;

/**
 * Database Role DTO
 * Onion Architecture: Application katmanında DTO
 * Clean Code: Immutable data transfer object for database roles
 */
public class DatabaseRoleDto {

    private final String roleName;
    private final String description;
    private final boolean isSuper;
    private final boolean canCreateRole;
    private final boolean canCreateDb;
    private final boolean canLogin;
    private final boolean isReplication;
    private final boolean bypassRls;

    public DatabaseRoleDto(String roleName, String description, boolean isSuper,
                          boolean canCreateRole, boolean canCreateDb, boolean canLogin,
                          boolean isReplication, boolean bypassRls) {
        this.roleName = roleName;
        this.description = description;
        this.isSuper = isSuper;
        this.canCreateRole = canCreateRole;
        this.canCreateDb = canCreateDb;
        this.canLogin = canLogin;
        this.isReplication = isReplication;
        this.bypassRls = bypassRls;
    }

    // Getters
    public String getRoleName() {
        return roleName;
    }

    public String getDescription() {
        return description;
    }

    public boolean isSuper() {
        return isSuper;
    }

    public boolean canCreateRole() {
        return canCreateRole;
    }

    public boolean canCreateDb() {
        return canCreateDb;
    }

    public boolean canLogin() {
        return canLogin;
    }

    public boolean isReplication() {
        return isReplication;
    }

    public boolean bypassRls() {
        return bypassRls;
    }

    @Override
    public String toString() {
        return "DatabaseRoleDto{" +
                "roleName='" + roleName + '\'' +
                ", description='" + description + '\'' +
                ", isSuper=" + isSuper +
                ", canCreateRole=" + canCreateRole +
                ", canCreateDb=" + canCreateDb +
                ", canLogin=" + canLogin +
                ", isReplication=" + isReplication +
                ", bypassRls=" + bypassRls +
                '}';
    }
}
