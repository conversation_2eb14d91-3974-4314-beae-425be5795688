package com.dbauth.user.domain.valueobject;

/**
 * Database Role Value Object
 * Onion Architecture: Domain katmanında value object
 * Immutable: Database role bilgilerini temsil eder
 */
public class DatabaseRole {
    
    private final String roleName;
    private final String description;
    private final boolean isSuper;
    private final boolean canCreateRole;
    private final boolean canCreateDb;
    private final boolean canLogin;
    private final boolean isReplication;
    private final boolean bypassRls;

    public DatabaseRole(String roleName, String description, boolean isSuper, 
                       boolean canCreateRole, boolean canCreateDb, boolean canLogin,
                       boolean isReplication, boolean bypassRls) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        
        this.roleName = roleName.trim();
        this.description = description != null ? description.trim() : "";
        this.isSuper = isSuper;
        this.canCreateRole = canCreateRole;
        this.canCreateDb = canCreateDb;
        this.canLogin = canLogin;
        this.isReplication = isReplication;
        this.bypassRls = bypassRls;
    }

    /**
     * Business rule: Check if role has dangerous permissions
     */
    public boolean isDangerous() {
        return isSuper || canCreateRole || bypassRls;
    }

    /**
     * Business rule: Check if role can be used for application users
     */
    public boolean isApplicationRole() {
        return canLogin && !isReplication;
    }

    /**
     * Get role description with permissions summary
     */
    public String getDetailedDescription() {
        StringBuilder sb = new StringBuilder();
        if (description != null && !description.isEmpty()) {
            sb.append(description);
        } else {
            sb.append("Database role: ").append(roleName);
        }
        
        if (isSuper) {
            sb.append(" (SUPERUSER)");
        }
        
        return sb.toString();
    }

    // Getters
    public String getRoleName() {
        return roleName;
    }

    public String getDescription() {
        return description;
    }

    public boolean isSuper() {
        return isSuper;
    }

    public boolean canCreateRole() {
        return canCreateRole;
    }

    public boolean canCreateDb() {
        return canCreateDb;
    }

    public boolean canLogin() {
        return canLogin;
    }

    public boolean isReplication() {
        return isReplication;
    }

    public boolean bypassRls() {
        return bypassRls;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DatabaseRole that = (DatabaseRole) o;
        return roleName.equals(that.roleName);
    }

    @Override
    public int hashCode() {
        return roleName.hashCode();
    }

    @Override
    public String toString() {
        return "DatabaseRole{" +
                "roleName='" + roleName + '\'' +
                ", description='" + description + '\'' +
                ", isSuper=" + isSuper +
                ", canCreateRole=" + canCreateRole +
                ", canCreateDb=" + canCreateDb +
                ", canLogin=" + canLogin +
                ", isReplication=" + isReplication +
                ", bypassRls=" + bypassRls +
                '}';
    }
}
