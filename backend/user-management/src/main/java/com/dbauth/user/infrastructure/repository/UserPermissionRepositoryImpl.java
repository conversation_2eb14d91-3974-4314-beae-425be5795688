package com.dbauth.user.infrastructure.repository;

import com.dbauth.user.application.dto.GroupedUserPermissionDto;
import com.dbauth.user.api.dto.GroupedPermissionHistoryDto;
import com.dbauth.user.api.dto.PermissionHistoryDto;
import com.dbauth.user.application.service.DatabaseConnectionService;
import com.dbauth.user.application.service.ServerInfoService;
import com.dbauth.user.application.service.ServerInfo;
import com.dbauth.user.domain.entity.UserPermission;
import com.dbauth.user.domain.repository.UserPermissionRepository;
import com.dbauth.user.domain.valueobject.DatabaseRole;
import com.dbauth.user.infrastructure.entity.UserPermissionEntity;
import com.dbauth.shared.security.PasswordUtil;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * User Permission Repository Implementation
 * Onion Architecture: Infrastructure katmanında domain repository interface'ini implement eder
 * Clean Code: Domain entity ve JPA entity arasında mapping yapar
 */
@Slf4j
@Repository
public class UserPermissionRepositoryImpl implements UserPermissionRepository {

    private final JpaUserPermissionRepository jpaRepository;
    private final DatabaseConnectionService databaseConnectionService;
    private final ServerInfoService serverInfoService;
    private final PasswordUtil passwordUtil;

    public UserPermissionRepositoryImpl(JpaUserPermissionRepository jpaRepository,
                                      DatabaseConnectionService databaseConnectionService,
                                      ServerInfoService serverInfoService,
                                      PasswordUtil passwordUtil) {
        this.jpaRepository = jpaRepository;
        this.databaseConnectionService = databaseConnectionService;
        this.serverInfoService = serverInfoService;
        this.passwordUtil = passwordUtil;
    }

    @Override
    public UserPermission save(UserPermission userPermission) {
        UserPermissionEntity entity = toEntity(userPermission);
        UserPermissionEntity savedEntity = jpaRepository.save(entity);
        return toDomain(savedEntity);
    }

    @Override
    public Optional<UserPermission> findById(UUID id) {
        return jpaRepository.findById(id).map(this::toDomain);
    }

    @Override
    public List<UserPermission> findAll() {
        return jpaRepository.findAllByDeletedAtIsNullOrderByCreatedAtDesc().stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findByUserId(UUID userId) {
        return jpaRepository.findByUserIdAndDeletedAtIsNullOrderByCreatedAtDesc(userId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findByServerId(UUID serverId) {
        return jpaRepository.findByServerIdAndDeletedAtIsNullOrderByCreatedAtDesc(serverId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findActiveByUserId(UUID userId) {
        return jpaRepository.findByUserIdAndIsActiveTrue(userId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findActiveByServerId(UUID serverId) {
        return jpaRepository.findByServerIdAndIsActiveTrue(serverId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findByUserIdAndServerId(UUID userId, UUID serverId) {
        return jpaRepository.findByUserIdAndServerId(userId, serverId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findActiveByUserIdAndServerId(UUID userId, UUID serverId) {
        return jpaRepository.findByUserIdAndServerIdAndIsActiveTrue(userId, serverId).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findByRoleName(String roleName) {
        return jpaRepository.findByRoleName(roleName).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public List<UserPermission> findExpiredPermissions() {
        return jpaRepository.findExpiredPermissions(LocalDateTime.now()).stream()
                .map(this::toDomain)
                .toList();
    }

    @Override
    public void deleteById(UUID id) {
        jpaRepository.deleteById(id);
    }

    @Override
    public boolean existsByUserIdAndServerIdAndRoleNameAndIsActiveTrue(UUID userId, UUID serverId, String roleName) {
        return jpaRepository.existsByUserIdAndServerIdAndRoleNameAndIsActiveTrue(userId, serverId, roleName);
    }

    @Override
    public List<DatabaseRole> getDatabaseRolesFromServer(UUID serverId) {
        ServerInfo serverInfo = serverInfoService.getServerInfo(serverId);
        
        if (!"POSTGRESQL".equalsIgnoreCase(serverInfo.getDatabaseType()) &&
            !"SQL_SERVER".equalsIgnoreCase(serverInfo.getDatabaseType())) {
            throw new UnsupportedOperationException("Database roles are currently only supported for PostgreSQL and SQL Server");
        }

        List<DatabaseRole> roles = new ArrayList<>();
        Connection connection = null;

        try {
            // Şifreyi çöz
            log.debug("Encrypted password: {}", serverInfo.getPassword());
            String decryptedPassword = passwordUtil.decrypt(serverInfo.getPassword());
            log.debug("Decrypted password: {}", decryptedPassword);

            // Database type'a göre default database'i belirle
            String databaseName;
            String sql;

            if ("SQL_SERVER".equalsIgnoreCase(serverInfo.getDatabaseType())) {
                databaseName = "master";
                // MSSQL için database roles query'si
                sql = """
                    SELECT
                        r.name AS role_name,
                        COALESCE(r.name, '') AS description,
                        CASE WHEN r.name IN ('sysadmin', 'serveradmin') THEN 1 ELSE 0 END AS is_superuser,
                        CASE WHEN r.name IN ('sysadmin', 'securityadmin') THEN 1 ELSE 0 END AS can_create_role,
                        CASE WHEN r.name IN ('sysadmin', 'dbcreator') THEN 1 ELSE 0 END AS can_create_db,
                        0 AS can_login,
                        0 AS is_replication_role,
                        0 AS bypass_row_level_security
                    FROM sys.database_principals r
                    WHERE r.type = 'R'
                    AND r.name NOT IN ('public', 'guest', 'INFORMATION_SCHEMA', 'sys')
                    AND r.principal_id > 4
                    ORDER BY r.name
                    """;
            } else {
                // PostgreSQL için default database'i kullan
                databaseName = "postgres";
                // PostgreSQL pg_roles tablosundan sadece rolleri çek (kullanıcıları değil)
                sql = """
                    SELECT rolname AS role_name,
                           COALESCE(description.description, '') AS description,
                           rolsuper AS is_superuser,
                           rolcreaterole AS can_create_role,
                           rolcreatedb AS can_create_db,
                           rolcanlogin AS can_login,
                           rolreplication AS is_replication_role,
                           rolbypassrls AS bypass_row_level_security
                    FROM pg_roles
                    LEFT JOIN pg_description description ON description.objoid = pg_roles.oid
                    WHERE rolcanlogin = false
                    ORDER BY rolname
                    """;
            }

            // Server'a bağlan
            connection = databaseConnectionService.createConnection(
                serverInfo.getHost(),
                serverInfo.getPort(),
                databaseName,
                serverInfo.getUsername(),
                decryptedPassword,
                serverInfo.getDatabaseType()
            );

            try (PreparedStatement statement = connection.prepareStatement(sql);
                 ResultSet resultSet = statement.executeQuery()) {

                while (resultSet.next()) {
                    String roleName = resultSet.getString("role_name");
                    String description = resultSet.getString("description");
                    boolean isSuper = resultSet.getBoolean("is_superuser");
                    boolean canCreateRole = resultSet.getBoolean("can_create_role");
                    boolean canCreateDb = resultSet.getBoolean("can_create_db");
                    boolean canLogin = resultSet.getBoolean("can_login");
                    boolean isReplication = resultSet.getBoolean("is_replication_role");
                    boolean bypassRls = resultSet.getBoolean("bypass_row_level_security");

                    // Sadece gereksiz sistem rollerini filtrele (template ve public hariç, pg_ rolleri dahil et)
                    if (!isSystemRole(roleName)) {
                        DatabaseRole role = new DatabaseRole(
                            roleName,
                            description != null ? description : generateRoleDescription(roleName, isSuper, canCreateRole, canCreateDb),
                            isSuper,
                            canCreateRole,
                            canCreateDb,
                            canLogin,
                            isReplication,
                            bypassRls
                        );
                        roles.add(role);
                    }
                }
            }

        } catch (SQLException e) {
            throw new RuntimeException("Failed to fetch database roles from server: " + serverId, e);
        } finally {
            databaseConnectionService.closeConnection(connection);
        }

        return roles;
    }

    /**
     * Gereksiz sistem rollerini filtrele
     * PostgreSQL: pg_ rolleri dahil et, sadece template ve public'i filtrele
     * MSSQL: Temel sistem rollerini filtrele
     */
    private boolean isSystemRole(String roleName) {
        // PostgreSQL sistem rolleri
        if (roleName.startsWith("template") || roleName.equals("public")) {
            return true;
        }

        // MSSQL sistem rolleri (temel olanları filtrele)
        if (roleName.equals("guest") || roleName.equals("INFORMATION_SCHEMA") ||
            roleName.equals("sys") || roleName.equals("public")) {
            return true;
        }

        return false;
    }

    /**
     * Rol açıklaması oluştur
     */
    private String generateRoleDescription(String roleName, boolean isSuper, boolean canCreateRole, boolean canCreateDb) {
        StringBuilder desc = new StringBuilder();
        
        if (isSuper) {
            desc.append("Superuser role with full database access");
        } else {
            desc.append("Database role");
            if (canCreateRole || canCreateDb) {
                desc.append(" with");
                if (canCreateRole) desc.append(" role creation");
                if (canCreateRole && canCreateDb) desc.append(" and");
                if (canCreateDb) desc.append(" database creation");
                desc.append(" privileges");
            }
        }
        
        return desc.toString();
    }

    /**
     * Domain entity'yi JPA entity'ye çevir
     */
    private UserPermissionEntity toEntity(UserPermission domain) {
        UserPermissionEntity entity = new UserPermissionEntity();
        entity.setId(domain.getId());
        entity.setUserId(domain.getUserId());
        entity.setServerId(domain.getServerId());
        entity.setRoleName(domain.getRoleName());
        entity.setGrantedAt(domain.getGrantedAt());
        entity.setExpiresAt(domain.getExpiresAt());
        entity.setActive(domain.isActive());
        entity.setSqlQuery(domain.getSqlQuery());
        entity.setDeletedAt(domain.getDeletedAt());
        return entity;
    }

    /**
     * JPA entity'yi domain entity'ye çevir
     */
    private UserPermission toDomain(UserPermissionEntity entity) {
        return new UserPermission(
            entity.getId(),
            entity.getUserId(),
            entity.getServerId(),
            entity.getRoleName(),
            entity.getGrantedAt(),
            entity.getExpiresAt(),
            entity.isActive(),
            entity.getSqlQuery(),
            entity.getCreatedAt(),
            entity.getUpdatedAt(),
            entity.getDeletedAt()
        );
    }

    @Override
    public List<GroupedUserPermissionDto> findGroupedPermissions() {
        return jpaRepository.findGroupedPermissions();
    }

    @Override
    public List<PermissionHistoryDto> findPermissionHistory() {
        return jpaRepository.findPermissionHistory(LocalDateTime.now());
    }

    @Override
    public List<GroupedPermissionHistoryDto> findGroupedPermissionHistory() {
        LocalDateTime now = LocalDateTime.now();
        List<Object[]> results = jpaRepository.findGroupedPermissionHistoryNative(now);

        return results.stream().map(row -> {
            return new GroupedPermissionHistoryDto(
                (UUID) row[0],                     // id
                (UUID) row[1],                     // userId
                (String) row[2],                   // firstName
                (String) row[3],                   // lastName
                (String) row[4],                   // email
                (UUID) row[5],                     // serverId
                (String) row[6],                   // serverName
                (String) row[7],                   // databaseType
                (String) row[8],                   // roleNames
                ((Timestamp) row[9]).toLocalDateTime(),  // firstGrantedAt
                ((Timestamp) row[10]).toLocalDateTime(), // lastActionAt
                ((Number) row[11]).longValue(),    // totalPermissions
                ((Number) row[12]).longValue(),    // activePermissions
                ((Number) row[13]).longValue(),    // revokedPermissions
                ((Number) row[14]).longValue(),    // expiredPermissions
                (String) row[15],                  // lastAction
                ((Timestamp) row[16]).toLocalDateTime(), // createdAt
                ((Timestamp) row[17]).toLocalDateTime()  // updatedAt
            );
        }).collect(Collectors.toList());
    }
}
