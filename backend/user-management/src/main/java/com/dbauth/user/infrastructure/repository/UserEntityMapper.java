package com.dbauth.user.infrastructure.repository;

import com.dbauth.user.application.dto.UserDto;
import com.dbauth.user.domain.User;
import com.dbauth.user.infrastructure.entity.UserEntity;
import org.springframework.stereotype.Component;

/**
 * User mapping operations - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında tüm mapping işlemleri
 * Domain Entity, JPA Entity ve DTO arasında dönüşüm yapar
 */
@Component
public class UserEntityMapper {
    
    /**
     * Domain User'ı JPA UserEntity'ye dönüştürür
     */
    public UserEntity toEntity(User user) {
        if (user == null) {
            return null;
        }

        UserEntity entity = new UserEntity();
        entity.setId(user.getId());
        entity.setFirstName(user.getFirstName());
        entity.setLastName(user.getLastName());
        entity.setUsername(user.getUsername());
        entity.setEmail(user.getEmail());
        entity.setActive(user.isActive());
        entity.setCreatedAt(user.getCreatedAt());

        return entity;
    }
    
    /**
     * JPA UserEntity'yi Domain User'a dönüştürür
     */
    public User toDomain(UserEntity entity) {
        if (entity == null) {
            return null;
        }

        User user = new User();
        user.setId(entity.getId());
        user.setFirstName(entity.getFirstName());
        user.setLastName(entity.getLastName());
        user.setUsername(entity.getUsername());
        user.setEmail(entity.getEmail());
        user.setActive(entity.isActive());
        user.setCreatedAt(entity.getCreatedAt());

        return user;
    }

    /**
     * Domain User'ı UserDto'ya dönüştürür
     */
    public UserDto toDto(User user) {
        if (user == null) {
            return null;
        }

        return new UserDto(
                user.getId(),
                user.getFirstName(),
                user.getLastName(),
                user.getUsername(),
                user.getEmail(),
                user.isActive(),
                user.getCreatedAt()
        );
    }

    /**
     * UserDto'yu Domain User'a dönüştürür
     */
    public User fromDto(UserDto dto) {
        if (dto == null) {
            return null;
        }

        User user = new User();
        user.setId(dto.getId());
        user.setFirstName(dto.getFirstName());
        user.setLastName(dto.getLastName());
        user.setUsername(dto.getUsername());
        user.setEmail(dto.getEmail());
        user.setActive(dto.isActive());
        user.setCreatedAt(dto.getCreatedAt());

        return user;
    }
}
