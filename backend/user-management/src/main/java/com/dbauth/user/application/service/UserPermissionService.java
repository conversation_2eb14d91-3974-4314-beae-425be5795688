package com.dbauth.user.application.service;

import com.dbauth.user.api.exception.DuplicateUserException;
import com.dbauth.user.api.exception.InactiveUserException;
import com.dbauth.user.api.exception.PermissionExpiredException;
import com.dbauth.user.api.exception.PermissionNotFoundException;
import com.dbauth.user.api.exception.ServerConnectionException;
import com.dbauth.user.api.exception.UserNotFoundException;
import com.dbauth.user.application.dto.CreateUserPermissionRequest;
import com.dbauth.user.application.dto.GroupedUserPermissionDto;
import com.dbauth.user.application.dto.UserPermissionDto;
import com.dbauth.user.api.dto.GroupedPermissionHistoryDto;
import com.dbauth.user.api.dto.PermissionHistoryDto;
import com.dbauth.user.domain.entity.UserPermission;
import com.dbauth.user.domain.repository.UserPermissionRepository;
import com.dbauth.user.domain.UserRepository;
import com.dbauth.user.domain.valueobject.DatabaseRole;
import com.dbauth.shared.security.PasswordUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import java.sql.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * User Permission Application Service
 * Onion Architecture: Application katmanında business logic orchestration
 * Clean Code: Service layer for user permission operations
 */
@Service
@Transactional
@RequiredArgsConstructor
public class UserPermissionService {

    private final UserPermissionRepository userPermissionRepository;
    private final UserRepository userRepository;
    private final ServerInfoService serverInfoService;
    private final MssqlAlwaysOnService mssqlAlwaysOnService;
    private final PasswordUtil passwordUtil;

    /**
     * Grant permissions to user
     */
    public List<UserPermissionDto> grantPermissions(CreateUserPermissionRequest request) {
        // Validate user exists
        var user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new UserNotFoundException("User not found: " + request.getUserId()));

        if (!user.isActive()) {
            throw new InactiveUserException("Cannot grant permissions to inactive user");
        }

        // Validate server exists and is active
        if (!serverInfoService.isServerActive(request.getServerId())) {
            throw new ServerConnectionException("Server not found or inactive: " + request.getServerId());
        }

        // Calculate expiry date
        LocalDateTime expiresAt = LocalDateTime.now().plusDays(request.getDurationDays());

        // Create permissions for each role
        List<UserPermission> permissions = request.getRoleNames().stream()
                .map(roleName -> {
                    // Check if permission already exists and is active
                    boolean exists = userPermissionRepository.existsByUserIdAndServerIdAndRoleNameAndIsActiveTrue(
                            request.getUserId(), request.getServerId(), roleName);
                    
                    if (exists) {
                        throw new DuplicateUserException(
                                "User already has active permission for role: " + roleName);
                    }

                    return new UserPermission(
                            request.getUserId(),
                            request.getServerId(),
                            roleName,
                            expiresAt
                    );
                })
                .collect(Collectors.toList());

        // Save all permissions
        List<UserPermission> savedPermissions = permissions.stream()
                .map(userPermissionRepository::save)
                .collect(Collectors.toList());

        // Otomatik olarak yetkileri veritabanına uygula
        for (UserPermission permission : savedPermissions) {
            try {
                applyPermissions(permission.getId());
            } catch (Exception e) {
                // Yetki uygulama hatası durumunda log'la ama işlemi durdurma
                System.err.println("Yetki otomatik uygulanırken hata oluştu: " + e.getMessage());
            }
        }

        return savedPermissions.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all user permissions
     */
    @Transactional(readOnly = true)
    public List<UserPermissionDto> getAllUserPermissions() {
        return userPermissionRepository.findAll().stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get grouped user permissions with server details
     */
    @Transactional(readOnly = true)
    public List<GroupedUserPermissionDto> getGroupedPermissions() {
        List<GroupedUserPermissionDto> groupedPermissions = userPermissionRepository.findGroupedPermissions();

        // Server bilgilerini ekle
        return groupedPermissions.stream()
                .map(this::enrichWithServerInfo)
                .collect(Collectors.toList());
    }

    /**
     * Get permission history with server details
     */
    @Transactional(readOnly = true)
    public List<PermissionHistoryDto> getPermissionHistory() {
        List<PermissionHistoryDto> permissionHistory = userPermissionRepository.findPermissionHistory();

        // Server bilgilerini ekle
        return permissionHistory.stream()
                .map(this::enrichPermissionHistoryWithServerInfo)
                .collect(Collectors.toList());
    }

    /**
     * Get grouped permission history with server details
     */
    @Transactional(readOnly = true)
    public List<GroupedPermissionHistoryDto> getGroupedPermissionHistory() {
        List<GroupedPermissionHistoryDto> groupedHistory = userPermissionRepository.findGroupedPermissionHistory();

        // Server bilgilerini ekle
        return groupedHistory.stream()
                .map(this::enrichGroupedPermissionHistoryWithServerInfo)
                .collect(Collectors.toList());
    }

    /**
     * Get permission history for specific user and server
     */
    @Transactional(readOnly = true)
    public List<PermissionHistoryDto> getPermissionHistoryByUserAndServer(UUID userId, UUID serverId) {
        List<PermissionHistoryDto> permissionHistory = userPermissionRepository.findPermissionHistory();

        // Kullanıcı ve sunucu bazında filtrele
        List<PermissionHistoryDto> filteredHistory = permissionHistory.stream()
                .filter(history -> history.getUserId().equals(userId) && history.getServerId().equals(serverId))
                .collect(Collectors.toList());

        // Server bilgilerini ekle
        return filteredHistory.stream()
                .map(this::enrichPermissionHistoryWithServerInfo)
                .collect(Collectors.toList());
    }

    /**
     * Get detailed permission history by user and server (each action as separate record)
     */
    @Transactional(readOnly = true)
    public List<PermissionHistoryDto> getDetailedPermissionHistoryByUserAndServer(UUID userId, UUID serverId) {
        // Bu metod her yetki işlemini ayrı bir kayıt olarak döndürür
        // Şu anda mevcut findPermissionHistory kullanıyor ama gelecekte ayrı bir query yazılabilir
        List<PermissionHistoryDto> permissionHistory = userPermissionRepository.findPermissionHistory();

        List<PermissionHistoryDto> filteredHistory = permissionHistory.stream()
                .filter(history -> history.getUserId().equals(userId) && history.getServerId().equals(serverId))
                .sorted((h1, h2) -> h2.getUpdatedAt().compareTo(h1.getUpdatedAt())) // En yeni önce
                .collect(Collectors.toList());

        // Server bilgilerini ekle
        return filteredHistory.stream()
                .map(this::enrichPermissionHistoryWithServerInfo)
                .collect(Collectors.toList());
    }

    /**
     * Get user permission by ID
     */
    @Transactional(readOnly = true)
    public UserPermissionDto getUserPermissionById(UUID id) {
        UserPermission permission = userPermissionRepository.findById(id)
                .orElseThrow(() -> new PermissionNotFoundException("User permission not found: " + id));
        return toDto(permission);
    }

    /**
     * Get permissions by user ID
     */
    @Transactional(readOnly = true)
    public List<UserPermissionDto> getPermissionsByUserId(UUID userId) {
        return userPermissionRepository.findByUserId(userId).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get permissions by server ID
     */
    @Transactional(readOnly = true)
    public List<UserPermissionDto> getPermissionsByServerId(UUID serverId) {
        return userPermissionRepository.findByServerId(serverId).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Revoke user permission
     */
    public void revokePermission(UUID permissionId) {
        UserPermission permission = userPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new PermissionNotFoundException("User permission not found: " + permissionId));

        UserPermission revokedPermission = permission.revoke();
        userPermissionRepository.save(revokedPermission);
    }

    /**
     * Delete user permission (soft delete)
     */
    public void deletePermission(UUID permissionId) {
        UserPermission permission = userPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new PermissionNotFoundException("User permission not found: " + permissionId));

        // Soft delete: yetkiyi sil ama veritabanından kaldırma
        UserPermission deletedPermission = permission.delete();
        userPermissionRepository.save(deletedPermission);
    }

    /**
     * Get database roles from server
     */
    @Transactional(readOnly = true)
    public List<DatabaseRole> getDatabaseRoles(UUID serverId) {
        return userPermissionRepository.getDatabaseRolesFromServer(serverId);
    }

    /**
     * Clean up expired permissions
     */
    public void cleanupExpiredPermissions() {
        List<UserPermission> expiredPermissions = userPermissionRepository.findExpiredPermissions();
        
        for (UserPermission permission : expiredPermissions) {
            if (permission.isActive()) {
                UserPermission revokedPermission = permission.revoke();
                userPermissionRepository.save(revokedPermission);
            }
        }
    }

    /**
     * Convert domain entity to DTO
     */
    private UserPermissionDto toDto(UserPermission permission) {
        // Get user info
        var user = userRepository.findById(permission.getUserId())
                .orElseThrow(() -> new UserNotFoundException("User not found: " + permission.getUserId()));

        return new UserPermissionDto(
                permission.getId(),
                permission.getUserId(),
                user.getFirstName() + " " + user.getLastName(),
                user.getEmail(),
                permission.getServerId(),
                "", // Server name will be populated by controller
                "", // Database type will be populated by controller
                permission.getRoleName(),
                "", // Role description will be populated from database roles
                permission.getSqlQuery(),
                toTimestamp(permission.getGrantedAt()),
                toTimestamp(permission.getExpiresAt()),
                permission.isActive()
        );
    }

    /**
     * Enrich grouped permission with server info
     */
    private GroupedUserPermissionDto enrichWithServerInfo(GroupedUserPermissionDto groupedPermission) {
        try {
            var serverInfo = serverInfoService.getServerInfo(groupedPermission.getServerId());
            return new GroupedUserPermissionDto(
                    groupedPermission.getId(),
                    groupedPermission.getUserId(),
                    groupedPermission.getFirstName(),
                    groupedPermission.getLastName(),
                    groupedPermission.getEmail(),
                    groupedPermission.getServerId(),
                    groupedPermission.getRoleNames(),
                    groupedPermission.getGrantedAt(),
                    groupedPermission.getExpiresAt(),
                    groupedPermission.isHasActivePermissions(),
                    groupedPermission.getSqlQuery()
            );
        } catch (Exception e) {
            // If server info cannot be retrieved, return as is
            return groupedPermission;
        }
    }

    /**
     * Apply permissions to database
     */
    public void applyPermissions(UUID permissionId) {
        UserPermission permission = userPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new PermissionNotFoundException("Permission not found: " + permissionId));

        if (!permission.isActive()) {
            throw new InactiveUserException("Cannot apply inactive permission");
        }

        if (permission.getExpiresAt().isBefore(LocalDateTime.now())) {
            throw new PermissionExpiredException("Cannot apply expired permission");
        }

        // Get user info for SQL query
        var user = userRepository.findById(permission.getUserId())
                .orElseThrow(() -> new UserNotFoundException("User not found: " + permission.getUserId()));

        // Get server info to determine database type
        var serverInfo = serverInfoService.getServerInfo(permission.getServerId());

        // Generate SQL query based on database type
        String sqlQuery;
        if ("SQL_SERVER".equals(serverInfo.getDatabaseType())) {
            // MSSQL Always On için özel işlem
            sqlQuery = applyMssqlAlwaysOnPermissions(user, permission, serverInfo);
        } else {
            // PostgreSQL ve diğer veritabanları için mevcut işlem
            sqlQuery = generateGrantSqlQuery(user.getEmail(), permission.getRoleName());
        }

        // Update permission with SQL query
        UserPermission updatedPermission = new UserPermission(
            permission.getId(),
            permission.getUserId(),
            permission.getServerId(),
            permission.getRoleName(),
            permission.getGrantedAt(),
            permission.getExpiresAt(),
            permission.isActive(),
            sqlQuery,
            permission.getCreatedAt(),
            LocalDateTime.now(),
            permission.getDeletedAt()
        );

        userPermissionRepository.save(updatedPermission);

        // Gerçek veritabanı bağlantısı ve uygulama
        try {
            applyPermissionToDatabase(serverInfo, sqlQuery);
            System.out.println("Permissions successfully applied for user: " + permission.getUserId() +
                              " on server: " + permission.getServerId() +
                              " with roles: " + permission.getRoleName());
        } catch (SQLException e) {
            System.err.println("Failed to apply permissions: " + e.getMessage());
            throw new RuntimeException("Failed to apply permissions", e);
        }
    }

    /**
     * Revoke permissions from database
     */
    public void revokePermissions(UUID permissionId) {
        UserPermission permission = userPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new PermissionNotFoundException("Permission not found: " + permissionId));

        // Get user info for SQL query
        var user = userRepository.findById(permission.getUserId())
                .orElseThrow(() -> new UserNotFoundException("User not found: " + permission.getUserId()));

        // Get server info to determine database type
        var serverInfo = serverInfoService.getServerInfo(permission.getServerId());

        // Generate SQL query for revoking permission
        String sqlQuery = generateRevokeSqlQuery(user.getEmail(), permission.getRoleName());

        // Revoke the permission (set isActive = false) and update SQL query
        UserPermission revokedPermission = new UserPermission(
            permission.getId(),
            permission.getUserId(),
            permission.getServerId(),
            permission.getRoleName(),
            permission.getGrantedAt(),
            permission.getExpiresAt(),
            false, // Set inactive
            sqlQuery, // Update with revoke SQL query
            permission.getCreatedAt(),
            LocalDateTime.now(), // Update timestamp
            permission.getDeletedAt()
        );

        userPermissionRepository.save(revokedPermission);

        // Gerçek veritabanı bağlantısı ve iptal işlemi
        try {
            revokePermissionFromDatabase(serverInfo, sqlQuery);
            System.out.println("Permissions successfully revoked for user: " + permission.getUserId() +
                              " on server: " + permission.getServerId() +
                              " with roles: " + permission.getRoleName());
        } catch (SQLException e) {
            System.err.println("Failed to revoke permissions: " + e.getMessage());
            throw new RuntimeException("Failed to revoke permissions", e);
        }
    }

    /**
     * Generate GRANT SQL query for PostgreSQL
     */
    private String generateGrantSqlQuery(String userEmail, String roleName) {
        // PostgreSQL'de kullanıcı email'i ile role verme
        return String.format("GRANT \"%s\" TO \"%s\";", roleName, userEmail);
    }

    /**
     * Generate REVOKE SQL query for PostgreSQL
     */
    private String generateRevokeSqlQuery(String userEmail, String roleName) {
        // PostgreSQL'de kullanıcı email'i ile role iptal etme
        return String.format("REVOKE \"%s\" FROM \"%s\";", roleName, userEmail);
    }

    /**
     * Apply all permissions for a user on a specific server
     */
    public void applyUserServerPermissions(UUID userId, UUID serverId) {
        List<UserPermission> permissions = userPermissionRepository.findByUserIdAndServerId(userId, serverId);

        for (UserPermission permission : permissions) {
            // Süresi dolmamış tüm yetkiler için (aktif olup olmadığına bakmaksızın)
            if (permission.getExpiresAt().isAfter(LocalDateTime.now())) {
                // Önce yetkiyi aktif hale getir
                UserPermission activatedPermission = permission.activate();
                userPermissionRepository.save(activatedPermission);

                // Sonra veritabanında uygula
                applyPermissions(activatedPermission.getId());
            }
        }
    }

    /**
     * Revoke all permissions for a user on a specific server
     */
    public void revokeUserServerPermissions(UUID userId, UUID serverId) {
        List<UserPermission> permissions = userPermissionRepository.findByUserIdAndServerId(userId, serverId);

        for (UserPermission permission : permissions) {
            if (permission.isActive()) {
                revokePermissions(permission.getId());
            }
        }
    }

    /**
     * Enrich permission history with server info
     */
    private PermissionHistoryDto enrichPermissionHistoryWithServerInfo(PermissionHistoryDto permissionHistory) {
        try {
            var serverInfo = serverInfoService.getServerInfo(permissionHistory.getServerId());
            permissionHistory.setServerName(serverInfo.getName());
            permissionHistory.setDatabaseType(serverInfo.getDatabaseType());
            return permissionHistory;
        } catch (Exception e) {
            // If server info cannot be retrieved, return as is
            return permissionHistory;
        }
    }

    /**
     * Enrich grouped permission history with server info
     */
    private GroupedPermissionHistoryDto enrichGroupedPermissionHistoryWithServerInfo(GroupedPermissionHistoryDto groupedHistory) {
        try {
            var serverInfo = serverInfoService.getServerInfo(groupedHistory.getServerId());
            groupedHistory.setServerName(serverInfo.getName());
            groupedHistory.setDatabaseType(serverInfo.getDatabaseType());
            return groupedHistory;
        } catch (Exception e) {
            // If server info cannot be retrieved, return as is
            return groupedHistory;
        }
    }

    /**
     * Convert LocalDateTime to timestamp
     */
    private long toTimestamp(LocalDateTime dateTime) {
        return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * Apply permission to target database
     */
    private void applyPermissionToDatabase(ServerInfo serverInfo, String sqlQuery) throws SQLException {
        String decryptedPassword = passwordUtil.decrypt(serverInfo.getPassword());
        String jdbcUrl = getJdbcUrl(serverInfo);

        try (Connection connection = DriverManager.getConnection(jdbcUrl, serverInfo.getUsername(), decryptedPassword);
             PreparedStatement statement = connection.prepareStatement(sqlQuery)) {
            statement.execute();
        }
    }

    /**
     * Revoke permission from target database
     */
    private void revokePermissionFromDatabase(ServerInfo serverInfo, String sqlQuery) throws SQLException {
        String decryptedPassword = passwordUtil.decrypt(serverInfo.getPassword());
        String jdbcUrl = getJdbcUrl(serverInfo);

        try (Connection connection = DriverManager.getConnection(jdbcUrl, serverInfo.getUsername(), decryptedPassword);
             PreparedStatement statement = connection.prepareStatement(sqlQuery)) {
            statement.execute();
        }
    }

    /**
     * Get JDBC URL for server
     */
    private String getJdbcUrl(ServerInfo serverInfo) {
        String databaseName = getServerDatabaseName(serverInfo);
        switch (serverInfo.getDatabaseType()) {
            case "POSTGRESQL":
                return String.format("jdbc:postgresql://%s:%d/%s", serverInfo.getHost(), serverInfo.getPort(), databaseName);
            case "SQL_SERVER":
                return String.format("**************************************", serverInfo.getHost(), serverInfo.getPort(), databaseName);
            case "MYSQL":
                return String.format("jdbc:mysql://%s:%d/%s", serverInfo.getHost(), serverInfo.getPort(), databaseName);
            default:
                return String.format("jdbc:postgresql://%s:%d/%s", serverInfo.getHost(), serverInfo.getPort(), databaseName);
        }
    }

    /**
     * MSSQL Always On Availability Groups için özel yetki verme işlemi
     */
    private String applyMssqlAlwaysOnPermissions(Object user, UserPermission permission, Object serverInfo) {
        StringBuilder sqlLog = new StringBuilder();

        try {
            // Server connection bilgilerini al
            var connection = serverInfoService.getConnection(permission.getServerId());

            // 1. Primary replica kontrolü
            boolean isPrimary = mssqlAlwaysOnService.isPrimaryReplica(connection);
            sqlLog.append("-- Primary replica check: ").append(isPrimary).append("\n");

            if (!isPrimary) {
                sqlLog.append("-- WARNING: Bu sunucu primary replica değil, LOGIN işlemleri yapılamaz\n");
                return sqlLog.toString();
            }

            // 2. LOGIN oluştur (eğer yoksa)
            String loginName = getUserEmail(user); // user.getEmail() yerine helper metod
            String password = "TempPassword123!"; // Geçici şifre, gerçek uygulamada güvenli şifre üretilmeli

            boolean loginCreated = mssqlAlwaysOnService.createLogin(connection, loginName, password);
            if (loginCreated) {
                sqlLog.append("-- LOGIN created: ").append(loginName).append("\n");
            } else {
                sqlLog.append("-- LOGIN creation failed: ").append(loginName).append("\n");
            }

            // 3. LOGIN'in SID'ini al
            String loginSid = mssqlAlwaysOnService.getLoginSid(connection, loginName);
            if (loginSid != null) {
                sqlLog.append("-- LOGIN SID: ").append(loginSid).append("\n");
            }

            // 4. Orphaned user'ları kontrol et ve düzelt
            String databaseName = getServerDatabaseName(serverInfo); // serverInfo.getDefaultDatabase() yerine helper metod
            var orphanedUsers = mssqlAlwaysOnService.findOrphanedUsers(connection, databaseName);

            for (var orphanedUser : orphanedUsers) {
                if (orphanedUser.getUserName().equals(loginName)) {
                    boolean fixed = mssqlAlwaysOnService.fixOrphanedUser(connection, databaseName,
                        orphanedUser.getUserName(), loginName);
                    if (fixed) {
                        sqlLog.append("-- Orphaned user fixed: ").append(orphanedUser.getUserName()).append("\n");
                    }
                }
            }

            // 5. Veritabanı seviyesinde yetki ver
            String grantSql = generateMssqlGrantSqlQuery(loginName, permission.getRoleName(), databaseName);
            sqlLog.append("-- Grant SQL: ").append(grantSql).append("\n");

            // Gerçek yetki verme işlemi
            try (PreparedStatement stmt = connection.prepareStatement(grantSql)) {
                stmt.execute();
                sqlLog.append("-- Permissions successfully applied\n");
            } catch (SQLException e) {
                sqlLog.append("-- ERROR applying permissions: ").append(e.getMessage()).append("\n");
                throw e;
            }

            // 6. Replica'lar arasında senkronizasyon bilgisi
            var replicas = mssqlAlwaysOnService.getAvailabilityReplicas(connection);
            sqlLog.append("-- Available replicas: ").append(replicas.size()).append("\n");
            for (var replica : replicas) {
                sqlLog.append("--   ").append(replica.getServerName())
                      .append(" (").append(replica.getRole()).append(")\n");
            }

            connection.close();

        } catch (Exception e) {
            sqlLog.append("-- ERROR: ").append(e.getMessage()).append("\n");
        }

        return sqlLog.toString();
    }

    /**
     * MSSQL için GRANT SQL sorgusu oluşturur
     */
    private String generateMssqlGrantSqlQuery(String loginName, String roleName, String databaseName) {
        return String.format("""
            USE [%s];
            IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = '%s')
                CREATE USER [%s] FOR LOGIN [%s];
            ALTER ROLE [%s] ADD MEMBER [%s];
            """, databaseName, loginName, loginName, loginName, roleName, loginName);
    }

    /**
     * User email'ini güvenli şekilde alır
     */
    private String getUserEmail(Object user) {
        try {
            // User object'ten email'i al
            if (user instanceof com.dbauth.user.domain.User) {
                return ((com.dbauth.user.domain.User) user).getEmail();
            }
            // Reflection kullanarak email field'ını al
            var emailField = user.getClass().getDeclaredField("email");
            emailField.setAccessible(true);
            return (String) emailField.get(user);
        } catch (Exception e) {
            return "<EMAIL>";
        }
    }

    /**
     * Server'ın default database name'ini güvenli şekilde alır
     */
    private String getServerDatabaseName(Object serverInfo) {
        try {
            // ServerInfo object'ten database type'ı al
            if (serverInfo instanceof ServerInfo) {
                ServerInfo info = (ServerInfo) serverInfo;
                String dbType = info.getDatabaseType();
                if ("SQL_SERVER".equals(dbType)) {
                    return "master";
                } else if ("POSTGRESQL".equals(dbType)) {
                    return "postgres";
                } else if ("MYSQL".equals(dbType)) {
                    return "mysql";
                }
                return "master";
            }

            // Reflection kullanarak database type'ı al
            var dbTypeField = serverInfo.getClass().getDeclaredField("databaseType");
            dbTypeField.setAccessible(true);
            String dbType = (String) dbTypeField.get(serverInfo);

            if ("SQL_SERVER".equals(dbType)) {
                return "master";
            } else if ("POSTGRESQL".equals(dbType)) {
                return "postgres";
            } else if ("MYSQL".equals(dbType)) {
                return "mysql";
            }
            return "master";
        } catch (Exception e) {
            return "master"; // MSSQL default database
        }
    }
}
