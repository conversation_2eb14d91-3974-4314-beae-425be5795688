package com.dbauth.user.config;

import com.dbauth.shared.security.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import java.util.Arrays;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;

/**
 * User Management Security Configuration
 */
@Slf4j
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final SimpleJwtAuthenticationFilter jwtAuthenticationFilter;
    private final Environment environment;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("Configuring User Management Security...");
        
        // Test ortamında mıyız kontrol et
        boolean isTestProfile = Arrays.asList(environment.getActiveProfiles()).contains("test");
        log.info("Active profiles: {}, isTestProfile: {}", Arrays.toString(environment.getActiveProfiles()), isTestProfile);
        
        http
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> {
                // Test ortamında tüm isteklere izin ver
                if (isTestProfile) {
                    auth.anyRequest().permitAll();
                    log.info("Test profile active: permitting all requests");
                } else {
                    auth.requestMatchers("/health", "/actuator/**").permitAll()
                        .anyRequest().authenticated();
                    log.info("Production profile active: requiring authentication");
                }
            });
        
        // Test ortamında değilse JWT filtresini ekle
        if (!isTestProfile) {
            http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        }

        log.info("User Management Security configuration completed");
        return http.build();
    }

    /**
     * User Management için basit JWT Authentication Filter
     */
    @Slf4j
    @Component
    @RequiredArgsConstructor
    public static class SimpleJwtAuthenticationFilter extends OncePerRequestFilter {

        private final JwtUtil jwtUtil;

        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
                throws ServletException, IOException {

            final String authorizationHeader = request.getHeader("Authorization");

            String email = null;
            String jwt = null;

            // JWT token'ı Authorization header'ından çıkar
            if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
                jwt = authorizationHeader.substring(7);
                try {
                    email = jwtUtil.extractEmail(jwt);
                    log.debug("JWT token extracted for user: {}", email);
                } catch (Exception e) {
                    log.error("JWT token extraction failed: {}", e.getMessage());
                }
            }

            // Eğer email çıkarıldıysa ve henüz authentication yoksa
            if (email != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                
                // JWT token'ı doğrula
                if (jwtUtil.validateToken(jwt)) {
                    log.debug("JWT token is valid for user: {}", email);
                    
                    // Authentication token oluştur
                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                            email, 
                            null, 
                            List.of(new SimpleGrantedAuthority("ROLE_ADMIN"))
                    );
                    
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    
                    log.debug("JWT authentication successful for user: {}", email);
                } else {
                    log.warn("JWT token validation failed for user: {}", email);
                }
            }

            filterChain.doFilter(request, response);
        }
    }
}
