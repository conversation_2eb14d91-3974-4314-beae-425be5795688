package com.dbauth.user.application.dto;

import java.util.UUID;

/**
 * User Permission DTO
 * Onion Architecture: Application katmanında DTO
 * Clean Code: Immutable data transfer object
 */
public class UserPermissionDto {

    private final UUID id;
    private final UUID userId;
    private final String userName;
    private final String userEmail;
    private final UUID serverId;
    private final String serverName;
    private final String databaseType;
    private final String roleName;
    private final String roleDescription;
    private final String sqlQuery;
    private final long grantedAt;
    private final long expiresAt;
    private final boolean isActive;

    public UserPermissionDto(UUID id, UUID userId, String userName, String userEmail,
                           UUID serverId, String serverName, String databaseType,
                           String roleName, String roleDescription, String sqlQuery,
                           long grantedAt, long expiresAt, boolean isActive) {
        this.id = id;
        this.userId = userId;
        this.userName = userName;
        this.userEmail = userEmail;
        this.serverId = serverId;
        this.serverName = serverName;
        this.databaseType = databaseType;
        this.roleName = roleName;
        this.roleDescription = roleDescription;
        this.sqlQuery = sqlQuery;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
    }

    // Getters
    public UUID getId() {
        return id;
    }

    public UUID getUserId() {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public UUID getServerId() {
        return serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public String getRoleName() {
        return roleName;
    }

    public String getRoleDescription() {
        return roleDescription;
    }

    public String getSqlQuery() {
        return sqlQuery;
    }

    public long getGrantedAt() {
        return grantedAt;
    }

    public long getExpiresAt() {
        return expiresAt;
    }

    public boolean isActive() {
        return isActive;
    }

    @Override
    public String toString() {
        return "UserPermissionDto{" +
                "id=" + id +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", userEmail='" + userEmail + '\'' +
                ", serverId=" + serverId +
                ", serverName='" + serverName + '\'' +
                ", databaseType='" + databaseType + '\'' +
                ", roleName='" + roleName + '\'' +
                ", roleDescription='" + roleDescription + '\'' +
                ", sqlQuery='" + sqlQuery + '\'' +
                ", grantedAt=" + grantedAt +
                ", expiresAt=" + expiresAt +
                ", isActive=" + isActive +
                '}';
    }
}
