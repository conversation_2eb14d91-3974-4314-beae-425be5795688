package com.dbauth.user.application.service;

import com.dbauth.user.api.exception.DuplicateUserException;
import com.dbauth.user.api.exception.UserNotFoundException;
import com.dbauth.user.api.exception.UserValidationException;
import com.dbauth.user.application.dto.CreateUserRequest;
import com.dbauth.user.application.dto.UpdateUserRequest;
import com.dbauth.user.application.dto.UserDto;
import com.dbauth.user.domain.User;
import com.dbauth.user.domain.UserRepository;
import com.dbauth.user.infrastructure.repository.UserEntityMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * User Service Implementation - Application Layer
 * Onion Architecture: Application katmanında business logic
 * <PERSON><PERSON> mantığını implement eder
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserRepository userRepository;
    private final UserEntityMapper userMapper;

    public UserServiceImpl(UserRepository userRepository, UserEntityMapper userMapper) {
        this.userRepository = userRepository;
        this.userMapper = userMapper;
    }

    @Override
    public UserDto createUser(CreateUserRequest request) {
        logger.debug("Creating user with username: {}", request.getUsername());
        
        // Username ve email kontrolü
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new DuplicateUserException("Username already exists: " + request.getUsername());
        }
        
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new DuplicateUserException("Email already exists: " + request.getEmail());
        }
        
        // User domain entity oluştur
        User user = new User(request.getFirstName(), request.getLastName(),
                           request.getUsername(), request.getEmail());
        
        // Domain validation
        if (!user.isValidForCreation()) {
            throw new UserValidationException("Invalid user data for creation");
        }
        
        // Kaydet
        User savedUser = userRepository.save(user);
        logger.info("User created successfully with ID: {}", savedUser.getId());
        
        return userMapper.toDto(savedUser);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto getUserById(UUID id) {
        logger.debug("Getting user by ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        return userMapper.toDto(user);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto getUserByUsername(String username) {
        logger.debug("Getting user by username: {}", username);
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));
        
        return userMapper.toDto(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserDto> getAllUsers() {
        logger.debug("Getting all users");
        
        List<User> users = userRepository.findAll();
        return users.stream()
                .map(userMapper::toDto)
                .collect(Collectors.toList());
    }



    @Override
    public UserDto updateUser(UUID id, UpdateUserRequest request) {
        logger.debug("Updating user with ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + id));
        
        // First name güncelleme
        if (request.getFirstName() != null) {
            user.updateFirstName(request.getFirstName());
        }

        // Last name güncelleme
        if (request.getLastName() != null) {
            user.updateLastName(request.getLastName());
        }

        // Username güncelleme
        if (request.getUsername() != null && !request.getUsername().equals(user.getUsername())) {
            if (userRepository.existsByUsername(request.getUsername())) {
                throw new IllegalArgumentException("Username already exists: " + request.getUsername());
            }
            user.setUsername(request.getUsername());
        }

        // Email güncelleme
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new IllegalArgumentException("Email already exists: " + request.getEmail());
            }
            user.updateEmail(request.getEmail());
        }

        // Active durumu güncelleme
        if (request.getActive() != null) {
            user.setActive(request.getActive());
        }

        User savedUser = userRepository.save(user);
        logger.info("User updated successfully with ID: {}", savedUser.getId());
        
        return userMapper.toDto(savedUser);
    }

    @Override
    public void deleteUser(UUID id) {
        logger.debug("Deleting user with ID: {}", id);
        
        if (!userRepository.existsById(id)) {
            throw new UserNotFoundException("User not found with ID: " + id);
        }
        
        userRepository.deleteById(id);
        logger.info("User deleted successfully with ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean userExists(UUID id) {
        return userRepository.existsById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean usernameExists(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }
}
