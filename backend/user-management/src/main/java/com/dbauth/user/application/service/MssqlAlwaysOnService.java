package com.dbauth.user.application.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * MSSQL Always On Availability Groups LOGIN Yönetim Servisi
 * 
 * Bu servis MSSQL Always On ortamında LOGIN'lerin yönetimini sağlar:
 * - Primary/Secondary replica tespiti
 * - LOGIN oluşturma ve kopyalama
 * - Orphaned user düzeltme
 * - SID senkronizasyonu
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MssqlAlwaysOnService {

    /**
     * Sunucunun Always On Availability Groups'ta primary replica olup olmadığını kontrol eder
     */
    public boolean isPrimaryReplica(Connection connection) {
        String query = """
            SELECT 
                ar.role_desc,
                ar.is_local
            FROM sys.dm_hadr_availability_replica_states ar
            INNER JOIN sys.availability_replicas r ON ar.replica_id = r.replica_id
            WHERE ar.is_local = 1
            """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                String roleDesc = rs.getString("role_desc");
                boolean isLocal = rs.getBoolean("is_local");
                
                log.info("Always On role: {}, is_local: {}", roleDesc, isLocal);
                return "PRIMARY".equals(roleDesc) && isLocal;
            }
            
            // Always On yapılandırması yoksa, standalone instance olarak kabul et (primary gibi davran)
            log.info("Always On yapılandırması bulunamadı, standalone instance olarak kabul ediliyor");
            return true;
            
        } catch (SQLException e) {
            log.error("Primary replica kontrolü sırasında hata: {}", e.getMessage());
            // Hata durumunda güvenli tarafta kal, primary olarak kabul et
            return true;
        }
    }

    /**
     * Tüm replica'ları listeler
     */
    public List<ReplicaInfo> getAvailabilityReplicas(Connection connection) {
        String query = """
            SELECT 
                r.replica_server_name,
                ar.role_desc,
                ar.operational_state_desc,
                ar.connected_state_desc,
                ar.is_local
            FROM sys.dm_hadr_availability_replica_states ar
            INNER JOIN sys.availability_replicas r ON ar.replica_id = r.replica_id
            ORDER BY ar.is_local DESC, r.replica_server_name
            """;

        List<ReplicaInfo> replicas = new ArrayList<>();
        
        try (PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                ReplicaInfo replica = ReplicaInfo.builder()
                    .serverName(rs.getString("replica_server_name"))
                    .role(rs.getString("role_desc"))
                    .operationalState(rs.getString("operational_state_desc"))
                    .connectedState(rs.getString("connected_state_desc"))
                    .isLocal(rs.getBoolean("is_local"))
                    .build();
                
                replicas.add(replica);
            }
            
        } catch (SQLException e) {
            log.error("Replica listesi alınırken hata: {}", e.getMessage());
        }
        
        return replicas;
    }

    /**
     * LOGIN'in var olup olmadığını kontrol eder
     */
    public boolean loginExists(Connection connection, String loginName) {
        String query = "SELECT COUNT(*) FROM sys.server_principals WHERE name = ? AND type IN ('S', 'U')";
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, loginName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            log.error("LOGIN varlık kontrolü sırasında hata: {}", e.getMessage());
        }
        
        return false;
    }

    /**
     * LOGIN oluşturur (sadece primary replica'da)
     */
    public boolean createLogin(Connection connection, String loginName, String password) {
        if (!isPrimaryReplica(connection)) {
            log.warn("LOGIN oluşturma işlemi sadece primary replica'da yapılabilir");
            return false;
        }

        if (loginExists(connection, loginName)) {
            log.info("LOGIN zaten mevcut: {}", loginName);
            return true;
        }

        String createLoginSql = String.format(
            "CREATE LOGIN [%s] WITH PASSWORD = '%s', CHECK_POLICY = OFF, CHECK_EXPIRATION = OFF",
            loginName, password.replace("'", "''") // SQL injection koruması
        );

        try (PreparedStatement stmt = connection.prepareStatement(createLoginSql)) {
            stmt.executeUpdate();
            log.info("LOGIN başarıyla oluşturuldu: {}", loginName);
            return true;
            
        } catch (SQLException e) {
            log.error("LOGIN oluşturma hatası: {}", e.getMessage());
            return false;
        }
    }

    /**
     * LOGIN'in SID'ini alır
     */
    public String getLoginSid(Connection connection, String loginName) {
        String query = "SELECT CONVERT(VARCHAR(85), sid, 1) as sid_hex FROM sys.server_principals WHERE name = ?";
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, loginName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("sid_hex");
                }
            }
        } catch (SQLException e) {
            log.error("LOGIN SID alınırken hata: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * sp_help_revlogin kullanarak LOGIN'i kopyalamak için SQL script oluşturur
     */
    public String generateLoginScript(Connection connection, String loginName) {
        // sp_help_revlogin stored procedure'ü kullanarak LOGIN script'i oluştur
        String query = "EXEC sp_help_revlogin @login_name = ?";
        
        StringBuilder script = new StringBuilder();
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, loginName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    script.append(rs.getString(1)).append("\n");
                }
            }
        } catch (SQLException e) {
            log.error("LOGIN script oluşturma hatası: {}", e.getMessage());
        }
        
        return script.toString();
    }

    /**
     * Orphaned user'ları tespit eder
     */
    public List<OrphanedUser> findOrphanedUsers(Connection connection, String databaseName) {
        String query = String.format("""
            USE [%s];
            SELECT 
                dp.name as user_name,
                dp.sid as user_sid,
                CONVERT(VARCHAR(85), dp.sid, 1) as user_sid_hex
            FROM sys.database_principals dp
            LEFT JOIN sys.server_principals sp ON dp.sid = sp.sid
            WHERE dp.type IN ('S', 'U') 
                AND dp.name NOT IN ('guest', 'INFORMATION_SCHEMA', 'sys', 'dbo')
                AND sp.sid IS NULL
            """, databaseName);

        List<OrphanedUser> orphanedUsers = new ArrayList<>();
        
        try (PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                OrphanedUser user = OrphanedUser.builder()
                    .userName(rs.getString("user_name"))
                    .userSidHex(rs.getString("user_sid_hex"))
                    .databaseName(databaseName)
                    .build();
                
                orphanedUsers.add(user);
            }
            
        } catch (SQLException e) {
            log.error("Orphaned user tespiti sırasında hata: {}", e.getMessage());
        }
        
        return orphanedUsers;
    }

    /**
     * Orphaned user'ı düzeltir (sp_change_users_login kullanarak)
     */
    public boolean fixOrphanedUser(Connection connection, String databaseName, String userName, String loginName) {
        String query = String.format("USE [%s]; EXEC sp_change_users_login 'Update_One', '%s', '%s'", 
            databaseName, userName, loginName);

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.executeUpdate();
            log.info("Orphaned user düzeltildi: {} -> {} (Database: {})", userName, loginName, databaseName);
            return true;
            
        } catch (SQLException e) {
            log.error("Orphaned user düzeltme hatası: {}", e.getMessage());
            return false;
        }
    }

    // Inner classes for data transfer
    
    @lombok.Data
    @lombok.Builder
    public static class ReplicaInfo {
        private String serverName;
        private String role;
        private String operationalState;
        private String connectedState;
        private boolean isLocal;
    }

    @lombok.Data
    @lombok.Builder
    public static class OrphanedUser {
        private String userName;
        private String userSidHex;
        private String databaseName;
    }
}
