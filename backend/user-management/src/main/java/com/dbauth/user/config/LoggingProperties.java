package com.dbauth.user.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Logging Configuration Properties
 * Onion Architecture: Configuration katmanında logging ayarları
 * Single Responsibility: Sadece logging ile ilgili ayarlar
 */
@ConfigurationProperties(prefix = "user.management.logging")
public class LoggingProperties {

    /**
     * SQL logging aktif/pasif
     * Varsayılan: false
     */
    private boolean showSql = false;

    /**
     * SQL formatting aktif/pasif
     * Varsayılan: true
     */
    private boolean formatSql = true;

    /**
     * SQL comments aktif/pasif
     * Varsayılan: true
     */
    private boolean useSqlComments = true;

    /**
     * Debug logging aktif/pasif
     * Varsayılan: false
     */
    private boolean debugLogging = false;

    /**
     * Log file path
     * Varsayılan: "logs/user-management"
     */
    private String logPath = "logs/user-management";

    /**
     * Log level
     * Varsayılan: "INFO"
     */
    private String level = "INFO";

    /**
     * Log pattern
     * Varsayılan: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
     */
    private String pattern = "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n";

    // Getters and Setters
    public boolean isShowSql() {
        return showSql;
    }

    public void setShowSql(boolean showSql) {
        this.showSql = showSql;
    }

    public boolean isFormatSql() {
        return formatSql;
    }

    public void setFormatSql(boolean formatSql) {
        this.formatSql = formatSql;
    }

    public boolean isUseSqlComments() {
        return useSqlComments;
    }

    public void setUseSqlComments(boolean useSqlComments) {
        this.useSqlComments = useSqlComments;
    }

    public boolean isDebugLogging() {
        return debugLogging;
    }

    public void setDebugLogging(boolean debugLogging) {
        this.debugLogging = debugLogging;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }
}
