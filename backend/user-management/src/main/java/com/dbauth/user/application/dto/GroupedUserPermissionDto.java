package com.dbauth.user.application.dto;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Grouped User Permission DTO
 * Groups permissions by user and server, showing all roles together
 */
public class GroupedUserPermissionDto {

    private final UUID id;
    private final UUID userId;
    private final String firstName;
    private final String lastName;
    private final String email;
    private final UUID serverId;
    private final String roleNames; // Comma-separated role names
    private final LocalDateTime grantedAt;
    private final LocalDateTime expiresAt;
    private final boolean hasActivePermissions;
    private final String sqlQuery; // SQL query if permissions are applied

    public GroupedUserPermissionDto(UUID id, UUID userId, String firstName, String lastName, String email,
                                  UUID serverId, String roleNames,
                                  LocalDateTime grantedAt, LocalDateTime expiresAt, boolean hasActivePermissions, String sqlQuery) {
        this.id = id;
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.serverId = serverId;
        this.roleNames = roleNames;
        this.grantedAt = grantedAt;
        this.expiresAt = expiresAt;
        this.hasActivePermissions = hasActivePermissions;
        this.sqlQuery = sqlQuery;
    }

    // Getters
    public UUID getId() {
        return id;
    }

    public UUID getUserId() {
        return userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getEmail() {
        return email;
    }

    public UUID getServerId() {
        return serverId;
    }



    public String getRoleNames() {
        return roleNames;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public boolean isHasActivePermissions() {
        return hasActivePermissions;
    }

    public String getSqlQuery() {
        return sqlQuery;
    }

    public String getUserName() {
        return firstName + " " + lastName;
    }

    @Override
    public String toString() {
        return "GroupedUserPermissionDto{" +
                "id=" + id +
                ", userId=" + userId +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", email='" + email + '\'' +
                ", serverId=" + serverId +
                ", roleNames='" + roleNames + '\'' +
                ", grantedAt=" + grantedAt +
                ", expiresAt=" + expiresAt +
                ", hasActivePermissions=" + hasActivePermissions +
                ", sqlQuery='" + sqlQuery + '\'' +
                '}';
    }
}
