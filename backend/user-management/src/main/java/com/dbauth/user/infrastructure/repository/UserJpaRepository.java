package com.dbauth.user.infrastructure.repository;

import com.dbauth.user.infrastructure.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * User JPA Repository - Spring Data JPA kullanır
 * Infrastructure katmanında tanımlanır
 */
@Repository
public interface UserJpaRepository extends JpaRepository<UserEntity, UUID> {
    
    /**
     * Username ile kullanıcı bul
     */
    Optional<UserEntity> findByUsername(String username);
    
    /**
     * Email ile kullanıcı bul
     */
    Optional<UserEntity> findByEmail(String email);
    
    /**
     * Username'in var olup olmadığını kontrol et
     */
    boolean existsByUsername(String username);

    /**
     * Email'in var olup olmadığını kontrol et
     */
    boolean existsByEmail(String email);
}
