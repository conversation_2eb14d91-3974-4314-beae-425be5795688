package com.dbauth.user.infrastructure.repository;

import com.dbauth.user.application.dto.GroupedUserPermissionDto;
import com.dbauth.user.api.dto.GroupedPermissionHistoryDto;
import com.dbauth.user.api.dto.PermissionHistoryDto;
import com.dbauth.user.infrastructure.entity.UserPermissionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * JPA User Permission Repository - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında JPA entity kullanır
 * Clean Code: UserPermissionEntity kullanır, UserPermission domain entity değil
 */
@Repository
public interface JpaUserPermissionRepository extends JpaRepository<UserPermissionEntity, UUID> {

    /**
     * Find permissions by user ID
     */
    List<UserPermissionEntity> findByUserId(UUID userId);

    /**
     * Find permissions by server ID
     */
    List<UserPermissionEntity> findByServerId(UUID serverId);

    /**
     * Find active permissions by user ID
     */
    List<UserPermissionEntity> findByUserIdAndIsActiveTrue(UUID userId);

    /**
     * Find active permissions by server ID
     */
    List<UserPermissionEntity> findByServerIdAndIsActiveTrue(UUID serverId);

    /**
     * Find permissions by user and server
     */
    List<UserPermissionEntity> findByUserIdAndServerId(UUID userId, UUID serverId);

    /**
     * Find active permissions by user and server
     */
    List<UserPermissionEntity> findByUserIdAndServerIdAndIsActiveTrue(UUID userId, UUID serverId);

    /**
     * Find permissions by role name
     */
    List<UserPermissionEntity> findByRoleName(String roleName);

    /**
     * Find expired permissions
     */
    @Query("SELECT up FROM UserPermissionEntity up WHERE up.expiresAt < :now AND up.isActive = true")
    List<UserPermissionEntity> findExpiredPermissions(@Param("now") LocalDateTime now);

    /**
     * Check if user has active permission for server and role
     */
    boolean existsByUserIdAndServerIdAndRoleNameAndIsActiveTrue(UUID userId, UUID serverId, String roleName);

    /**
     * Find grouped permissions with user details
     * Groups permissions by user and server, concatenating role names
     */
    @Query("""
        SELECT new com.dbauth.user.application.dto.GroupedUserPermissionDto(
            MIN(up.id),
            up.userId,
            u.firstName,
            u.lastName,
            u.email,
            up.serverId,
            CAST(string_agg(up.roleName, ', ') AS string),
            MIN(up.grantedAt),
            MAX(up.expiresAt),
            CASE WHEN COUNT(CASE WHEN up.isActive = true AND up.expiresAt > CURRENT_TIMESTAMP THEN 1 END) > 0 THEN true ELSE false END,
            CAST(string_agg(CASE WHEN up.sqlQuery IS NOT NULL THEN up.sqlQuery ELSE '' END, '; ') AS string)
        )
        FROM UserPermissionEntity up
        JOIN UserEntity u ON up.userId = u.id
        WHERE up.deletedAt IS NULL
        GROUP BY up.userId, u.firstName, u.lastName, u.email, up.serverId
        ORDER BY u.firstName, u.lastName
        """)
    List<GroupedUserPermissionDto> findGroupedPermissions();

    /**
     * Find all permissions ordered by created date desc (excluding deleted)
     */
    List<UserPermissionEntity> findAllByDeletedAtIsNullOrderByCreatedAtDesc();

    /**
     * Find permissions by user ID ordered by created date desc (excluding deleted)
     */
    List<UserPermissionEntity> findByUserIdAndDeletedAtIsNullOrderByCreatedAtDesc(UUID userId);

    /**
     * Find permissions by server ID ordered by created date desc (excluding deleted)
     */
    List<UserPermissionEntity> findByServerIdAndDeletedAtIsNullOrderByCreatedAtDesc(UUID serverId);

    /**
     * Count active permissions by user
     */
    @Query("SELECT COUNT(up) FROM UserPermissionEntity up WHERE up.userId = :userId AND up.isActive = true AND up.expiresAt > :now")
    long countActivePermissionsByUser(@Param("userId") UUID userId, @Param("now") LocalDateTime now);

    /**
     * Count active permissions by server
     */
    @Query("SELECT COUNT(up) FROM UserPermissionEntity up WHERE up.serverId = :serverId AND up.isActive = true AND up.expiresAt > :now")
    long countActivePermissionsByServer(@Param("serverId") UUID serverId, @Param("now") LocalDateTime now);

    /**
     * Find permission history with user and server details
     */
    @Query("""
        SELECT new com.dbauth.user.api.dto.PermissionHistoryDto(
            up.id,
            up.userId,
            u.firstName,
            u.lastName,
            u.email,
            up.serverId,
            CAST('' AS string),
            CAST('' AS string),
            up.roleName,
            CAST('' AS string),
            up.sqlQuery,
            up.grantedAt,
            up.expiresAt,
            up.isActive,
            CASE
                WHEN up.isActive = true AND up.expiresAt > :now THEN 'ACTIVE'
                WHEN up.isActive = false THEN 'REVOKED'
                WHEN up.expiresAt <= :now THEN 'EXPIRED'
                ELSE 'GRANTED'
            END,
            CASE
                WHEN up.isActive = false THEN up.updatedAt
                WHEN up.expiresAt <= :now THEN up.expiresAt
                ELSE up.grantedAt
            END,
            up.createdAt,
            up.updatedAt
        )
        FROM UserPermissionEntity up
        JOIN UserEntity u ON up.userId = u.id
        ORDER BY up.updatedAt DESC, up.createdAt DESC
        """)
    List<PermissionHistoryDto> findPermissionHistory(@Param("now") LocalDateTime now);

    /**
     * Find grouped permission history with user and server details
     */
    @Query(value = """
        SELECT
            (array_agg(up.id ORDER BY up.created_at))[1] as id,
            up.user_id as userId,
            u.first_name as firstName,
            u.last_name as lastName,
            u.email as email,
            up.server_id as serverId,
            '' as serverName,
            '' as databaseType,
            string_agg(DISTINCT up.role_name, ', ') as roleNames,
            MIN(up.granted_at) as firstGrantedAt,
            MAX(up.updated_at) as lastActionAt,
            COUNT(up.id) as totalPermissions,
            COUNT(CASE WHEN up.is_active = true AND up.expires_at > :now THEN 1 END) as activePermissions,
            COUNT(CASE WHEN up.is_active = false THEN 1 END) as revokedPermissions,
            COUNT(CASE WHEN up.expires_at <= :now THEN 1 END) as expiredPermissions,
            CASE
                WHEN COUNT(CASE WHEN up.is_active = true AND up.expires_at > :now THEN 1 END) > 0 THEN 'ACTIVE'
                WHEN COUNT(CASE WHEN up.is_active = false THEN 1 END) > 0 THEN 'REVOKED'
                WHEN COUNT(CASE WHEN up.expires_at <= :now THEN 1 END) > 0 THEN 'EXPIRED'
                ELSE 'GRANTED'
            END as lastAction,
            MIN(up.created_at) as createdAt,
            MAX(up.updated_at) as updatedAt
        FROM user_permissions up
        JOIN users u ON up.user_id = u.id
        GROUP BY up.user_id, u.first_name, u.last_name, u.email, up.server_id
        ORDER BY MAX(up.updated_at) DESC
        """, nativeQuery = true)
    List<Object[]> findGroupedPermissionHistoryNative(@Param("now") LocalDateTime now);
}
