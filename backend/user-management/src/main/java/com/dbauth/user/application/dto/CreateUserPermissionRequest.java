package com.dbauth.user.application.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.util.List;
import java.util.UUID;

/**
 * Create User Permission Request DTO
 * Onion Architecture: Application katmanında DTO
 * Clean Code: Immutable request object with validation
 */
public class CreateUserPermissionRequest {

    @NotNull(message = "Server ID is required")
    private UUID serverId;

    @NotNull(message = "User ID is required")
    private UUID userId;

    @NotEmpty(message = "At least one role must be selected")
    private List<String> roleNames;

    @Positive(message = "Duration must be positive")
    private int durationDays;

    // Default constructor for JSON deserialization
    public CreateUserPermissionRequest() {
    }

    public CreateUserPermissionRequest(UUID serverId, UUID userId, List<String> roleNames, int durationDays) {
        this.serverId = serverId;
        this.userId = userId;
        this.roleNames = roleNames;
        this.durationDays = durationDays;
    }

    // Getters and Setters
    public UUID getServerId() {
        return serverId;
    }

    public void setServerId(UUID serverId) {
        this.serverId = serverId;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public List<String> getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(List<String> roleNames) {
        this.roleNames = roleNames;
    }

    public int getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(int durationDays) {
        this.durationDays = durationDays;
    }

    @Override
    public String toString() {
        return "CreateUserPermissionRequest{" +
                "serverId=" + serverId +
                ", userId=" + userId +
                ", roleNames=" + roleNames +
                ", durationDays=" + durationDays +
                '}';
    }
}
