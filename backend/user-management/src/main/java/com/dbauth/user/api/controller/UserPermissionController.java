package com.dbauth.user.api.controller;

import com.dbauth.user.application.dto.CreateUserPermissionRequest;
import com.dbauth.user.application.dto.DatabaseRoleDto;
import com.dbauth.user.application.dto.GroupedUserPermissionDto;
import com.dbauth.user.application.dto.UserPermissionDto;
import com.dbauth.user.api.dto.GroupedPermissionHistoryDto;
import com.dbauth.user.api.dto.PermissionHistoryDto;
import com.dbauth.user.application.service.UserPermissionService;
import com.dbauth.user.domain.valueobject.DatabaseRole;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * User Permission REST Controller - API Layer
 * Onion Architecture: API katmanı sadece Application katmanının interface'le<PERSON> bağı<PERSON>lı
 * Kullanıcı yetki işlemleri için REST API endpoints
 */
@RestController
@RequestMapping("/api/user-permissions")
public class UserPermissionController {

    private final UserPermissionService userPermissionService;

    public UserPermissionController(UserPermissionService userPermissionService) {
        this.userPermissionService = userPermissionService;
    }

    /**
     * Tüm kullanıcı yetkilerini listele
     */
    @GetMapping
    public ResponseEntity<List<UserPermissionDto>> getAllUserPermissions() {
        List<UserPermissionDto> permissions = userPermissionService.getAllUserPermissions();
        return ResponseEntity.ok(permissions);
    }

    /**
     * Gruplu kullanıcı yetkilerini getir (join ile)
     */
    @GetMapping("/grouped")
    public ResponseEntity<List<GroupedUserPermissionDto>> getGroupedUserPermissions() {
        List<GroupedUserPermissionDto> permissions = userPermissionService.getGroupedPermissions();
        return ResponseEntity.ok(permissions);
    }

    /**
     * ID'ye göre kullanıcı yetkisi getir
     */
    @GetMapping("/{id}")
    public ResponseEntity<UserPermissionDto> getUserPermissionById(@PathVariable UUID id) {
        UserPermissionDto permission = userPermissionService.getUserPermissionById(id);
        return ResponseEntity.ok(permission);
    }

    /**
     * Kullanıcıya yetki ver
     */
    @PostMapping
    public ResponseEntity<List<UserPermissionDto>> grantPermissions(@Valid @RequestBody CreateUserPermissionRequest request) {
        List<UserPermissionDto> permissions = userPermissionService.grantPermissions(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(permissions);
    }

    /**
     * Kullanıcı yetkisini sil
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUserPermission(@PathVariable UUID id) {
        userPermissionService.deletePermission(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Kullanıcının yetkilerini getir
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<UserPermissionDto>> getPermissionsByUser(@PathVariable UUID userId) {
        List<UserPermissionDto> permissions = userPermissionService.getPermissionsByUserId(userId);
        return ResponseEntity.ok(permissions);
    }

    /**
     * Sunucunun yetkilerini getir
     */
    @GetMapping("/server/{serverId}")
    public ResponseEntity<List<UserPermissionDto>> getPermissionsByServer(@PathVariable UUID serverId) {
        List<UserPermissionDto> permissions = userPermissionService.getPermissionsByServerId(serverId);
        return ResponseEntity.ok(permissions);
    }

    /**
     * Sunucunun veritabanı rollerini getir (pg_roles tablosundan)
     */
    @GetMapping("/database-roles/{serverId}")
    public ResponseEntity<List<DatabaseRoleDto>> getDatabaseRoles(@PathVariable UUID serverId) {
        List<DatabaseRole> roles = userPermissionService.getDatabaseRoles(serverId);
        List<DatabaseRoleDto> roleDtos = roles.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(roleDtos);
    }

    /**
     * Süresi dolmuş yetkileri temizle
     */
    @PostMapping("/cleanup-expired")
    public ResponseEntity<Void> cleanupExpiredPermissions() {
        userPermissionService.cleanupExpiredPermissions();
        return ResponseEntity.ok().build();
    }

    /**
     * Kullanıcı yetkilerini veritabanına uygula
     */
    @PostMapping("/{id}/apply")
    public ResponseEntity<Void> applyUserPermissions(@PathVariable UUID id) {
        userPermissionService.applyPermissions(id);
        return ResponseEntity.ok().build();
    }

    /**
     * Kullanıcı yetkilerini veritabanından iptal et
     */
    @PostMapping("/{id}/revoke")
    public ResponseEntity<Void> revokeUserPermissions(@PathVariable UUID id) {
        userPermissionService.revokePermissions(id);
        return ResponseEntity.ok().build();
    }

    /**
     * Kullanıcının bir sunucudaki tüm yetkilerini uygula
     */
    @PostMapping("/user/{userId}/server/{serverId}/apply")
    public ResponseEntity<Void> applyUserServerPermissions(@PathVariable UUID userId, @PathVariable UUID serverId) {
        userPermissionService.applyUserServerPermissions(userId, serverId);
        return ResponseEntity.ok().build();
    }

    /**
     * Kullanıcının bir sunucudaki tüm yetkilerini iptal et
     */
    @PostMapping("/user/{userId}/server/{serverId}/revoke")
    public ResponseEntity<Void> revokeUserServerPermissions(@PathVariable UUID userId, @PathVariable UUID serverId) {
        userPermissionService.revokeUserServerPermissions(userId, serverId);
        return ResponseEntity.ok().build();
    }

    /**
     * Yetki geçmişini getir
     */
    @GetMapping("/history")
    public ResponseEntity<List<PermissionHistoryDto>> getPermissionHistory() {
        List<PermissionHistoryDto> history = userPermissionService.getPermissionHistory();
        return ResponseEntity.ok(history);
    }

    /**
     * Gruplu yetki geçmişini getir
     */
    @GetMapping("/history/grouped")
    public ResponseEntity<List<GroupedPermissionHistoryDto>> getGroupedPermissionHistory() {
        List<GroupedPermissionHistoryDto> groupedHistory = userPermissionService.getGroupedPermissionHistory();
        return ResponseEntity.ok(groupedHistory);
    }

    /**
     * Belirli kullanıcı ve sunucu için yetki geçmişini getir
     */
    @GetMapping("/history/user/{userId}/server/{serverId}")
    public ResponseEntity<List<PermissionHistoryDto>> getPermissionHistoryByUserAndServer(
            @PathVariable UUID userId, @PathVariable UUID serverId) {
        List<PermissionHistoryDto> history = userPermissionService.getPermissionHistoryByUserAndServer(userId, serverId);
        return ResponseEntity.ok(history);
    }

    /**
     * Kullanıcı ve sunucu bazında detaylı yetki geçmişini getir (her işlem ayrı kayıt)
     */
    @GetMapping("/history/detailed/user/{userId}/server/{serverId}")
    public ResponseEntity<List<PermissionHistoryDto>> getDetailedPermissionHistoryByUserAndServer(
            @PathVariable UUID userId, @PathVariable UUID serverId) {
        List<PermissionHistoryDto> history = userPermissionService.getDetailedPermissionHistoryByUserAndServer(userId, serverId);
        return ResponseEntity.ok(history);
    }

    /**
     * Domain entity'yi DTO'ya çevir
     */
    private DatabaseRoleDto toDto(DatabaseRole role) {
        return new DatabaseRoleDto(
                role.getRoleName(),
                role.getDescription(),
                role.isSuper(),
                role.canCreateRole(),
                role.canCreateDb(),
                role.canLogin(),
                role.isReplication(),
                role.bypassRls()
        );
    }
}
