package com.dbauth.user.application.service;

import com.dbauth.user.config.UserManagementProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.UUID;

/**
 * Database Connection Service
 * Hedef veritabanlarına bağlantı kurma ve SQL komutlarını çalıştırma servisi
 */
@Service
@Slf4j
public class DatabaseConnectionService {

    private final UserManagementProperties userManagementProperties;
    
    public DatabaseConnectionService(UserManagementProperties userManagementProperties) {
        this.userManagementProperties = userManagementProperties;
    }
    
    /**
     * Veritabanı bağlantısı oluşturur
     */
    public Connection createConnection(String host, Integer port, String databaseName, 
                                     String username, String password, String databaseType) throws SQLException {
        String jdbcUrl = buildJdbcUrl(host, port, databaseName, databaseType);
        
        log.debug("Creating database connection to: {}", jdbcUrl);
        
        try {
            Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
            log.info("Database connection established successfully to: {}:{}/{}", host, port, databaseName);
            return connection;
        } catch (SQLException e) {
            log.error("Failed to create database connection to {}:{}/{} - Error: {}",
                        host, port, databaseName, e.getMessage());
            throw e;
        }
    }
    
    /**
     * SQL komutunu çalıştırır
     */
    public boolean executeSql(Connection connection, String sql) throws SQLException {
        log.debug("Executing SQL: {}", sql);

        try (Statement statement = connection.createStatement()) {
            boolean result = statement.execute(sql);
            log.info("SQL executed successfully: {}", sql);
            return result;
        } catch (SQLException e) {
            log.error("Failed to execute SQL: {} - Error: {}", sql, e.getMessage());
            throw e;
        }
    }
    
    /**
     * Bağlantıyı güvenli şekilde kapatır
     */
    public void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
                log.debug("Database connection closed successfully");
            } catch (SQLException e) {
                log.warn("Error closing database connection: {}", e.getMessage());
            }
        }
    }
    
    /**
     * Bağlantı durumunu test eder
     */
    public boolean testConnection(String host, Integer port, String databaseName, 
                                String username, String password, String databaseType) {
        Connection connection = null;
        try {
            connection = createConnection(host, port, databaseName, username, password, databaseType);
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            log.warn("Connection test failed for {}:{}/{} - Error: {}",
                       host, port, databaseName, e.getMessage());
            return false;
        } finally {
            closeConnection(connection);
        }
    }
    
    /**
     * JDBC URL'ini oluşturur
     */
    private String buildJdbcUrl(String host, Integer port, String databaseName, String databaseType) {
        switch (databaseType.toUpperCase()) {
            case "POSTGRESQL":
                return String.format("jdbc:postgresql://%s:%d/%s", host, port, databaseName);
            case "MYSQL":
                return String.format("jdbc:mysql://%s:%d/%s", host, port, databaseName);
            case "ORACLE":
                return String.format("**************************", host, port, databaseName);
            case "SQLSERVER":
            case "SQL_SERVER":
                // MSSQL için doğru format: ****************************************************
                return String.format("********************************************************************************",
                    host, port, databaseName);
            default:
                throw new IllegalArgumentException("Desteklenmeyen veritabanı türü: " + databaseType);
        }
    }
}
