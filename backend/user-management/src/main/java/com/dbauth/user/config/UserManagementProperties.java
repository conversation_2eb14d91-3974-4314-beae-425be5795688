package com.dbauth.user.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * User Management Configuration Properties
 * Onion Architecture: Configuration katmanında ana ayarlar
 * Clean Code: Lombok bağımlılığı kaldır<PERSON>ld<PERSON>, manuel getter/setter
 * User management modülü için dinamik konfigürasyon değerleri
 */
@ConfigurationProperties(prefix = "user.management")
public class UserManagementProperties {



    /**
     * Application name
     * Varsayılan: "user-management"
     */
    private String applicationName = "user-management";

    /**
     * Application version
     * Varsayılan: "1.0.0"
     */
    private String applicationVersion = "1.0.0";

    // Getters and Setters

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getApplicationVersion() {
        return applicationVersion;
    }

    public void setApplicationVersion(String applicationVersion) {
        this.applicationVersion = applicationVersion;
    }
}
