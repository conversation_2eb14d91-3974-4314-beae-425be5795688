package com.dbauth.user.domain;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * User Repository Interface - Domain katmanında tanımlanır
 * Infrastructure katmanında implement edilir (Dependency Inversion)
 */
public interface UserRepository {

    /**
     * Kullanıcıyı kaydet
     */
    User save(User user);

    /**
     * ID ile kullanıcı bul
     */
    Optional<User> findById(UUID id);

    /**
     * Username ile kullanıcı bul
     */
    Optional<User> findByUsername(String username);

    /**
     * Email ile kullanıcı bul
     */
    Optional<User> findByEmail(String email);

    /**
     * Tüm kullanıcıları listele
     */
    List<User> findAll();

    /**
     * Kullanıcıyı sil
     */
    void deleteById(UUID id);

    /**
     * Kullanıcının var olup olmadığını kontrol et
     */
    boolean existsById(UUID id);

    /**
     * Username'in var olup olmadığını kontrol et
     */
    boolean existsByUsername(String username);

    /**
     * Email'in var olup olmadığını kontrol et
     */
    boolean existsByEmail(String email);
}
