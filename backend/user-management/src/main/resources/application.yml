server:
  port: 8081
  name: user-service

spring:
  application:
    name: user-management

    # Active profile (can be overridden by environment variable)
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # PostgreSQL Database Configuration
  datasource:
    url: ****************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: none  # Docker handles schema creation
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Task Scheduling Configuration
  task:
    scheduling:
      enabled: true

# Logging Configuration
logging:
  config: classpath:logback-spring.xml
  level:
    com.dbauth.user: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Environment variable for log directory
LOG_DIR: ../logs/user-management

# Gateway Configuration
gateway:
  security:
    # JWT authentication aktif/pasif durumu
    jwt-enabled: true
    # JWT secret key - Gateway ile aynı olmalı
    jwt-secret: default-jwt-secret-key-for-gateway-authentication-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-exactly-64-chars
    # JWT token expiration time (milisaniye) - 1 gün
    jwt-expiration: 86400000

# User Management Configuration
user:
  management:
    # Database connection timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
    database-connection-timeout-ms: 30000
    # Database query timeout (saniye) - Varsayılan: 30 saniye
    database-query-timeout-seconds: 30

    # Database Pool Configuration
    database-pool:
      # Maksimum pool boyutu - Varsayılan: 10
      maximum-pool-size: 10
      # Minimum idle connection sayısı - Varsayılan: 5
      minimum-idle: 5
      # Connection timeout (milisaniye) - Varsayılan: 20000 (20 saniye)
      connection-timeout-ms: 20000
      # Idle timeout (milisaniye) - Varsayılan: 300000 (5 dakika)
      idle-timeout-ms: 300000
      # Max lifetime (milisaniye) - Varsayılan: 1200000 (20 dakika)
      max-lifetime-ms: 1200000

    # Logging Configuration
    logging:
      # SQL logging aktif/pasif - Varsayılan: false
      show-sql: false
      # SQL formatting aktif/pasif - Varsayılan: true
      format-sql: true
      # SQL comments aktif/pasif - Varsayılan: true
      use-sql-comments: true
      # Debug logging aktif/pasif - Varsayılan: false
      debug-logging: false

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test

  datasource:
    # TestContainers will override these values
    url: ****************************************
    username: test
    password: test
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true



logging:
  level:
    com.dbauth.user: DEBUG
    org.testcontainers: INFO
