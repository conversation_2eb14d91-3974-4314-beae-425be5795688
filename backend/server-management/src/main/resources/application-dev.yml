# Development Environment Configuration for Server Management

spring:
  # PostgreSQL Database Configuration for Development
  datasource:
    url: ****************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      
  # JPA Configuration for Development
  jpa:
    hibernate:
      ddl-auto: update  # Auto-update schema in development
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

# Development Logging Configuration
logging:
  level:
    com.dbauth.servermanagement: DEBUG
    org.springframework.scheduling: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.orm.jpa: DEBUG

# Gateway Configuration for Development
gateway:
  url: http://localhost:8080
  security:
    # JWT authentication aktif/pasif durumu
    jwt-enabled: true
    # JWT secret key - Gateway ile aynı olmalı
    jwt-secret: ${JWT_SECRET:dev-jwt-secret-key-for-development-environment-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-this-is-exactly-64-characters-long}
    # JWT token expiration time (milisaniye) - 1 gün
    jwt-expiration: 86400000
