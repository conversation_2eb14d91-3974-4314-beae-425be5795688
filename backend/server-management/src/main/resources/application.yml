server:
  port: 8082

spring:
  application:
    name: server-management

  # Active profile (can be overridden by environment variable)
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: ****************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver
    
  jpa:
    hibernate:
      ddl-auto: none  # <PERSON><PERSON> handles schema creation
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    
logging:
  config: classpath:logback-spring.xml
  level:
    com.dbauth.servermanagement: INFO
    org.springframework.scheduling: DEBUG

# Environment variable for log directory
LOG_DIR: ../logs/server-management
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Gateway Configuration
gateway:
  url: http://localhost:8080
  security:
    # JWT authentication aktif/pasif durumu
    jwt-enabled: true
    # JWT secret key - Gateway ile aynı olmalı
    jwt-secret: default-jwt-secret-key-for-gateway-authentication-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-exactly-64-chars
    # JWT token expiration time (milisaniye) - 1 gün
    jwt-expiration: 86400000

# Metric Collection Configuration
metric:
  collection:
    # Metrik toplama interval'ı (milisaniye) - Varsayılan: 300000 (5 dakika)
    interval-ms: 300000
    # Database connection timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
    connection-timeout-ms: 30000
    # SQL query timeout (saniye) - Varsayılan: 30 saniye
    query-timeout-seconds: 30
    # Maksimum sonuç satır sayısı - Varsayılan: 1000
    max-result-rows: 1000
    # String değerleri kısaltma limiti - Varsayılan: 1000 karakter
    string-truncate-limit: 1000
    # Eski kayıtları temizleme cron expression - Varsayılan: "0 0 2 * * ?" (Her gün saat 02:00)
    cleanup-cron: "0 0 2 * * ?"
    # Eski kayıtları temizleme süresi (gün) - Varsayılan: 30 gün
    cleanup-retention-days: 30
    # Metrik toplama aktif/pasif durumu - Varsayılan: true
    enabled: true
    # Debug logging aktif/pasif durumu - Varsayılan: false
    debug-logging: false

  # Metrik türlerine göre davranış ayarları
  display:
    # LATEST metrikler için otomatik yenileme interval'ı (milisaniye) - Varsayılan: 300000 (5 dakika)
    latest-refresh-interval-ms: 300000
    # AVERAGE/RANGE metrikler için varsayılan zaman aralığı - Varsayılan: "1h"
    default-time-range: "1h"
    # Kullanılabilir zaman aralıkları
    available-time-ranges:
      - "15m"   # 15 dakika
      - "1h"    # 1 saat
      - "6h"    # 6 saat
      - "24h"   # 24 saat
      - "7d"    # 7 gün
    # RANGE metrikler için maksimum veri noktası sayısı - Varsayılan: 1000
    max-range-data-points: 1000
    # Grafik türü tercihleri
    chart-preferences:
      # Sayısal veriler için varsayılan grafik türü - Varsayılan: "line"
      numeric-chart-type: "line"
      # String veriler için varsayılan görünüm türü - Varsayılan: "table"
      string-display-type: "table"

# Scheduler Configuration
scheduler:
  # Expired permission cleanup cron expression - Varsayılan: "0 0 3 * * ?" (Her gün saat 03:00)
  expired-permission-cleanup-cron: "0 0 3 * * ?"
  # Gateway URL - Varsayılan: "http://localhost:8080"
  gateway-url: http://localhost:8080
  # Scheduled task'lar aktif/pasif durumu - Varsayılan: true
  enabled: true
  # HTTP request timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
  http-timeout-ms: 30000
  # HTTP connection timeout (milisaniye) - Varsayılan: 10000 (10 saniye)
  http-connection-timeout-ms: 10000
  # HTTP read timeout (milisaniye) - Varsayılan: 30000 (30 saniye)
  http-read-timeout-ms: 30000
