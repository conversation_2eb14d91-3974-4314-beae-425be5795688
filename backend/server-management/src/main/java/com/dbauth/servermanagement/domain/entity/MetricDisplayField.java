package com.dbauth.servermanagement.domain.entity;

import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "metric_display_fields")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class MetricDisplayField {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @NotNull(message = "Metrik sorgusu ID'si boş olamaz")
    @Column(name = "metric_query_id", nullable = false)
    private UUID metricQueryId;

    @NotBlank(message = "Kolon adı boş olamaz")
    @Size(max = 100, message = "Kolon adı en fazla 100 karakter olabilir")
    @Column(name = "column_name", nullable = false, length = 100)
    private String columnName;

    @NotBlank(message = "Görüntülenecek ad boş olamaz")
    @Size(max = 100, message = "Görüntülenecek ad en fazla 100 karakter olabilir")
    @Column(name = "display_name", nullable = false, length = 100)
    private String displayName;

    @NotBlank(message = "Veri tipi boş olamaz")
    @Size(max = 20, message = "Veri tipi en fazla 20 karakter olabilir")
    @Column(name = "data_type", nullable = false, length = 20)
    private String dataType; // string, int

    @NotNull(message = "Metrik tipi boş olamaz")
    @Enumerated(EnumType.STRING)
    @Column(name = "metric_type", nullable = false)
    @Builder.Default
    private MetricType metricType = MetricType.LATEST; // LATEST, AVERAGE, RANGE - her kolon için ayrı

    @NotNull(message = "Bileşen tipi boş olamaz")
    @Enumerated(EnumType.STRING)
    @Column(name = "component_type", nullable = false, length = 50)
    @Builder.Default
    private ComponentType componentType = ComponentType.NUMBER_CARD; // UI bileşen türü

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "component_config", columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> componentConfig = new HashMap<>(); // Bileşen konfigürasyonu

    @Size(max = 500, message = "Açıklama en fazla 500 karakter olabilir")
    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "display_order")
    @Builder.Default
    private Integer displayOrder = 0;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Business Methods

    /**
     * Bu alanın zaman aralığı gerektirip gerektirmediğini kontrol eder
     * Hem metrik tipi hem de bileşen tipine göre kontrol eder
     */
    public boolean requiresTimeRange() {
        return (metricType != null && metricType.needsTimeRange()) ||
               (componentType != null && componentType.needsTimeRange());
    }

    /**
     * Bu alanın sayısal veri gerektirip gerektirmediğini kontrol eder
     */
    public boolean requiresNumericData() {
        return metricType != null && metricType.needsNumericData();
    }

    /**
     * Bileşen konfigürasyonunu JSON string olarak getirir
     */
    public String getComponentConfigAsJson() {
        if (componentConfig == null || componentConfig.isEmpty()) {
            return componentType != null ? componentType.getDefaultConfig() : "{}";
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(componentConfig);
        } catch (JsonProcessingException e) {
            log.warn("Component config JSON'a dönüştürülemedi: {}", e.getMessage());
            return componentType != null ? componentType.getDefaultConfig() : "{}";
        }
    }

    /**
     * JSON string'den bileşen konfigürasyonunu set eder
     */
    public void setComponentConfigFromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            this.componentConfig = new HashMap<>();
            return;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            this.componentConfig = mapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            log.warn("JSON component config parse edilemedi: {}", e.getMessage());
            this.componentConfig = new HashMap<>();
        }
    }

    /**
     * Bileşen tipinin veri tipiyle uyumlu olup olmadığını kontrol eder
     */
    public boolean isComponentCompatibleWithDataType() {
        if (componentType == null || dataType == null) {
            return false;
        }

        boolean isNumeric = "int".equalsIgnoreCase(dataType) || "INT".equalsIgnoreCase(dataType);
        return isNumeric ? componentType.supportsNumeric() : componentType.supportsString();
    }

    /**
     * Varsayılan bileşen konfigürasyonunu uygular
     */
    public void applyDefaultComponentConfig() {
        if (componentType != null) {
            setComponentConfigFromJson(componentType.getDefaultConfig());
        }
    }

    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        if (this.updatedAt == null) {
            this.updatedAt = LocalDateTime.now();
        }

        // Varsayılan değerleri ayarla
        if (this.metricType == null) {
            this.metricType = MetricType.LATEST;
        }
        if (this.componentType == null) {
            this.componentType = ComponentType.NUMBER_CARD;
        }
        if (this.componentConfig == null || this.componentConfig.isEmpty()) {
            applyDefaultComponentConfig();
        }

        // Uyumluluk kontrolü
        if (!isComponentCompatibleWithDataType()) {
            log.warn("Bileşen tipi {} veri tipi {} ile uyumlu değil. Varsayılan bileşen atanıyor.",
                    componentType, dataType);

            // Veri tipine uygun varsayılan bileşen ata
            boolean isNumeric = "int".equalsIgnoreCase(dataType) || "INT".equalsIgnoreCase(dataType);
            this.componentType = isNumeric ? ComponentType.NUMBER_CARD : ComponentType.STATUS_CARD;
            applyDefaultComponentConfig();
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();

        // Güncelleme sırasında da uyumluluk kontrolü
        if (!isComponentCompatibleWithDataType()) {
            log.warn("Güncelleme sırasında bileşen tipi {} veri tipi {} ile uyumlu değil.",
                    componentType, dataType);
        }
    }
}
