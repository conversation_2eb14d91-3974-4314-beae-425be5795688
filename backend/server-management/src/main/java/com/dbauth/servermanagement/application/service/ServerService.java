package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.application.constants.ServerConstants;
import com.dbauth.servermanagement.application.dto.CreateServerApplicationDto;
import com.dbauth.servermanagement.application.dto.ServerApplicationDto;
import com.dbauth.servermanagement.application.dto.UpdateServerApplicationDto;
import com.dbauth.servermanagement.application.exception.ServerAlreadyExistsException;
import com.dbauth.servermanagement.application.exception.ServerNotFoundException;
import com.dbauth.servermanagement.application.mapper.ServerApplicationMapper;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import com.dbauth.servermanagement.infrastructure.mapper.ServerEntityMapper;
import com.dbauth.servermanagement.infrastructure.service.DatabaseConnectionService;
import com.dbauth.shared.security.PasswordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Server Service - Application Layer
 * Onion Architecture: Clean application service, sadece CRUD operations
 * Clean Code: Single Responsibility, küçük metodlar, Lombok kullanımı
 * Refactored: Büyük service küçük service'lere bölündü
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ServerService {

    private final ServerRepository serverRepository;
    private final ServerApplicationMapper applicationMapper;
    private final ServerEntityMapper entityMapper;
    private final DatabaseConnectionService connectionService;
    private final MetricCollectionService metricCollectionService;
    private final PasswordUtil passwordUtil;

    /**
     * Tüm sunucuları listeler
     */
    public List<ServerApplicationDto> getAllServers() {
        log.info("Tüm sunucular listeleniyor");
        List<Server> servers = serverRepository.findAll();
        return servers.stream()
                .map(applicationMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Aktif sunucuları listeler
     */
    public List<ServerApplicationDto> getActiveServers() {
        log.info("Aktif sunucular listeleniyor");
        List<Server> servers = serverRepository.findByIsActive(true);
        return servers.stream()
                .map(applicationMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Belirli veritabanı tipindeki aktif sunucuları listeler
     */
    public List<ServerApplicationDto> getServersByDatabaseType(DatabaseType databaseType) {
        log.info("Veritabanı tipi {} olan sunucular listeleniyor", databaseType);
        List<Server> servers = serverRepository.findByIsActiveAndDatabaseType(true, databaseType);
        return servers.stream()
                .map(applicationMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * ID ile sunucu getirir
     */
    public ServerApplicationDto getServerById(UUID id) {
        log.info("Sunucu ID: {} ile aranıyor", id);
        Server server = serverRepository.findById(id)
                .orElseThrow(() -> new RuntimeException(ServerConstants.ERROR_SERVER_NOT_FOUND + ": " + id));
        return applicationMapper.toDto(server);
    }

    /**
     * İsim ile sunucu getirir
     */
    public ServerApplicationDto getServerByName(String name) {
        log.info("Sunucu adı: {} ile aranıyor", name);
        Server server = serverRepository.findByName(name)
                .orElseThrow(() -> new RuntimeException(ServerConstants.ERROR_SERVER_NOT_FOUND + ": " + name));
        return applicationMapper.toDto(server);
    }

    /**
     * Yeni sunucu oluşturur
     */
    public ServerApplicationDto createServer(CreateServerApplicationDto createDto) {
        log.info("Yeni sunucu oluşturuluyor: {}", createDto.getName());

        // Validation
        if (!createDto.isValid()) {
            throw new IllegalArgumentException("Geçersiz sunucu bilgileri");
        }

        // Check if server name already exists
        if (serverRepository.existsByName(createDto.getName())) {
            throw new ServerAlreadyExistsException("Bu isimde bir sunucu zaten mevcut: " + createDto.getName());
        }

        // Convert to domain and encrypt password
        Server server = applicationMapper.toDomain(createDto);
        if (server.getPassword() != null && !server.getPassword().isEmpty()) {
            server.setPassword(passwordUtil.encrypt(server.getPassword()));
        }
        Server savedServer = serverRepository.save(server);

        log.info("Sunucu başarıyla oluşturuldu: {} (ID: {})", savedServer.getName(), savedServer.getId());

        // Trigger metric collection for active servers
        triggerMetricCollectionIfActive(savedServer);

        return applicationMapper.toDto(savedServer);
    }

    /**
     * Sunucu günceller
     */
    public ServerApplicationDto updateServer(UUID id, UpdateServerApplicationDto updateDto) {
        log.info("Sunucu güncelleniyor: {} (ID: {})", updateDto.getName(), id);

        // Validation
        if (!updateDto.isValid()) {
            throw new IllegalArgumentException("Geçersiz güncelleme bilgileri");
        }

        Server existingServer = serverRepository.findById(id)
                .orElseThrow(() -> new ServerNotFoundException("Sunucu bulunamadı: " + id));

        // Check name uniqueness if name is being updated
        if (updateDto.hasName() && !existingServer.getName().equals(updateDto.getName()) &&
                serverRepository.existsByNameAndIdNot(updateDto.getName(), id)) {
            throw new ServerAlreadyExistsException("Bu isimde bir sunucu zaten mevcut: " + updateDto.getName());
        }

        // Update server with partial data
        Server updatedServer = applicationMapper.updateDomain(existingServer, updateDto);

        // Encrypt password if it's being updated
        if (updateDto.hasPassword() && updateDto.getPassword() != null && !updateDto.getPassword().isEmpty()) {
            updatedServer.setPassword(passwordUtil.encrypt(updateDto.getPassword()));
        }

        Server savedServer = serverRepository.save(updatedServer);

        log.info("Sunucu başarıyla güncellendi: {} (ID: {})", savedServer.getName(), savedServer.getId());

        return applicationMapper.toDto(savedServer);
    }

    /**
     * Sunucu siler
     */
    public void deleteServer(UUID id) {
        log.info("Sunucu siliniyor: {}", id);

        if (!serverRepository.findById(id).isPresent()) {
            throw new ServerNotFoundException("Sunucu bulunamadı: " + id);
        }

        serverRepository.deleteById(id);
        log.info("Sunucu başarıyla silindi: {}", id);
    }

    /**
     * Sunucu durumunu değiştirir (aktif/pasif)
     */
    public void toggleServerStatus(UUID id) {
        log.info("Sunucu durumu değiştiriliyor: {}", id);

        Server server = serverRepository.findById(id)
                .orElseThrow(() -> new ServerNotFoundException("Sunucu bulunamadı: " + id));

        server.toggleStatus();
        serverRepository.save(server);

        log.info("Sunucu durumu değiştirildi: {} -> {}", server.getName(), server.getIsActive() ? "Aktif" : "Pasif");
    }

    /**
     * Sunucu bağlantısını test eder
     */
    public DatabaseConnectionService.ConnectionTestResult testConnection(CreateServerApplicationDto createDto) {
        log.info("Bağlantı testi başlatılıyor: {}:{}", createDto.getHost(), createDto.getPort());

        // Convert to domain for connection test
        Server server = applicationMapper.toDomain(createDto);
        return connectionService.testConnection(server);
    }

    /**
     * Mevcut sunucu bağlantısını test eder
     */
    public DatabaseConnectionService.ConnectionTestResult testConnection(UUID serverId) {
        log.info("Mevcut sunucu bağlantısı test ediliyor: {}", serverId);

        Server server = serverRepository.findById(serverId)
                .orElseThrow(() -> new ServerNotFoundException("Sunucu bulunamadı: " + serverId));

        return connectionService.testConnection(server);
    }

    /**
     * Aktif sunucu için metrik çekme işlemini tetikler
     */
    private void triggerMetricCollectionIfActive(Server server) {
        if (server.getIsActive()) {
            try {
                log.info("🚀 Yeni sunucu için metrik çekme işlemi tetikleniyor: {}", server.getName());
                metricCollectionService.collectMetricsForSingleServer(server.getId());
            } catch (Exception e) {
                log.error("⚠️ Yeni sunucu için metrik çekme işlemi başarısız: {} - {}",
                    server.getName(), e.getMessage());
                // Metrik çekme hatası sunucu oluşturma işlemini engellemez
            }
        } else {
            log.info("ℹ️ Sunucu pasif durumda olduğu için metrik çekme işlemi atlandı: {}", server.getName());
        }
    }


}
