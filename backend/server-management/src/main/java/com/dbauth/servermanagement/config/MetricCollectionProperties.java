package com.dbauth.servermanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Metric Collection Configuration Properties
 * Metrik toplama işlemi için dinamik konfigürasyon değerleri
 */
@Data
@ConfigurationProperties(prefix = "metric.collection")
public class MetricCollectionProperties {

    /**
     * Metrik toplama interval'ı (milisaniye)
     * Varsayılan: 300000 (5 dakika)
     */
    private long intervalMs = 300000L;

    /**
     * Database connection timeout (milisaniye)
     * Varsayılan: 30000 (30 saniye)
     */
    private int connectionTimeoutMs = 30000;

    /**
     * SQL query timeout (saniye)
     * Varsayılan: 30 saniye
     */
    private int queryTimeoutSeconds = 30;

    /**
     * Maksimum sonuç satır sayısı
     * Varsayılan: 1000
     */
    private int maxResultRows = 1000;

    /**
     * String değerleri kısaltma limiti
     * Varsayılan: 1000 karakter
     */
    private int stringTruncateLimit = 1000;

    /**
     * Eski kayıtları temizleme cron expression
     * Varsayılan: "0 0 2 * * ?" (Her gün saat 02:00)
     */
    private String cleanupCron = "0 0 2 * * ?";

    /**
     * Eski kayıtları temizleme süresi (gün)
     * Varsayılan: 30 gün
     */
    private int cleanupRetentionDays = 30;

    /**
     * Metrik toplama aktif/pasif durumu
     * Varsayılan: true
     */
    private boolean enabled = true;

    /**
     * Debug logging aktif/pasif durumu
     * Varsayılan: false
     */
    private boolean debugLogging = false;
}
