package com.dbauth.servermanagement.application.dto;

import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.UUID;

/**
 * Metric Display Field DTO - Application Layer
 * Frontend ile backend arasında metrik display field verilerini taşır
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricDisplayFieldDto {

    private UUID id;
    private UUID metricQueryId;
    private String columnName;
    private String displayName;
    private String dataType;
    private MetricType metricType;
    private ComponentType componentType;
    private Map<String, Object> componentConfig;
    private String description;
    private Integer displayOrder;
    private Boolean isActive;

    // UI için ek bilgiler
    private Boolean requiresTimeRange;
    private Boolean isCompatibleWithDataType;
    private ComponentType[] recommendedComponents;
    private ComponentType[] compatibleComponents;
    private String defaultComponentConfig;

    /**
     * Entity'den DTO'ya dönüştürme için factory method
     */
    public static MetricDisplayFieldDto fromEntity(com.dbauth.servermanagement.domain.entity.MetricDisplayField entity) {
        if (entity == null) {
            return null;
        }

        return MetricDisplayFieldDto.builder()
                .id(entity.getId())
                .metricQueryId(entity.getMetricQueryId())
                .columnName(entity.getColumnName())
                .displayName(entity.getDisplayName())
                .dataType(entity.getDataType())
                .metricType(entity.getMetricType())
                .componentType(entity.getComponentType())
                .componentConfig(entity.getComponentConfig())
                .description(entity.getDescription())
                .displayOrder(entity.getDisplayOrder())
                .isActive(entity.getIsActive())
                .requiresTimeRange(entity.requiresTimeRange())
                .isCompatibleWithDataType(entity.isComponentCompatibleWithDataType())
                .recommendedComponents(ComponentType.getRecommendedForMetricType(
                        entity.getMetricType(), entity.getDataType()))
                .compatibleComponents(ComponentType.getCompatibleComponents(entity.getDataType()))
                .defaultComponentConfig(entity.getComponentType() != null ? 
                        entity.getComponentType().getDefaultConfig() : "{}")
                .build();
    }

    /**
     * DTO'dan Entity'ye dönüştürme için factory method
     */
    public com.dbauth.servermanagement.domain.entity.MetricDisplayField toEntity() {
        return com.dbauth.servermanagement.domain.entity.MetricDisplayField.builder()
                .id(this.id)
                .metricQueryId(this.metricQueryId)
                .columnName(this.columnName)
                .displayName(this.displayName)
                .dataType(this.dataType)
                .metricType(this.metricType != null ? this.metricType : MetricType.LATEST)
                .componentType(this.componentType != null ? this.componentType : ComponentType.NUMBER_CARD)
                .componentConfig(this.componentConfig)
                .description(this.description)
                .displayOrder(this.displayOrder != null ? this.displayOrder : 0)
                .isActive(this.isActive != null ? this.isActive : true)
                .build();
    }
}
