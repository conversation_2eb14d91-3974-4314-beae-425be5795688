package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.api.dto.ServerDetailDto;
import com.dbauth.servermanagement.application.constants.ServerConstants;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricResult;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import com.dbauth.servermanagement.domain.repository.MetricResultRepository;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Server Detail Service - Application Layer
 * Onion Architecture: Server detay ve metrik işlemleri için ayrı service
 * Clean Code: Single Responsibility, sadece server detail operations
 * Refactored: Büyük ServerService'den ayrıldı
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ServerDetailService {

    private final ServerRepository serverRepository;
    private final MetricResultRepository metricResultRepository;
    private final MetricQueryRepository metricQueryRepository;
    private final MetricDisplayService metricDisplayService;

    /**
     * Server detaylarını metrik bilgileri ile birlikte getirir
     */
    public ServerDetailDto getServerDetailById(UUID id) {
        return getServerDetailById(id, null, null);
    }

    /**
     * Server detaylarını belirli zaman aralığındaki metrik bilgileri ile birlikte getirir
     */
    public ServerDetailDto getServerDetailById(UUID id, Long startTime, Long endTime) {
        log.info("Sunucu detayları ID: {} ile getiriliyor (startTime: {}, endTime: {})", id, startTime, endTime);

        Server server = serverRepository.findById(id)
                .orElseThrow(() -> new RuntimeException(ServerConstants.ERROR_SERVER_NOT_FOUND + ": " + id));

        // Metrik sonuçlarını getir
        List<MetricResult> metrics = getMetricResults(id, startTime, endTime);

        // Metrik query'lerini getir
        Map<UUID, MetricQuery> queryMap = getMetricQueries(metrics);

        // Metrik verilerini display format'a çevir
        List<ServerDetailDto.MetricDisplayData> metricDisplayData = metrics.stream()
                .map(metric -> metricDisplayService.convertToMetricDisplayData(metric, queryMap.get(metric.getMetricQueryId())))
                .collect(Collectors.toList());

        // İstatistikleri hesapla
        Map<String, Object> stats = calculateServerStats(id);

        return ServerDetailDto.builder()
                .id(server.getId())
                .name(server.getName())
                .host(server.getHost())
                .port(server.getPort())
                .databaseType(server.getDatabaseType().toString())
                .username(server.getUsername())
                .description(server.getDescription())
                .isActive(server.getIsActive())
                .createdAt(server.getCreatedAt())
                .updatedAt(server.getUpdatedAt())
                .metrics(metricDisplayData)
                .stats(stats)
                .build();
    }

    /**
     * Metrik sonuçlarını getirir
     */
    private List<MetricResult> getMetricResults(UUID serverId, Long startTime, Long endTime) {
        if (startTime != null && endTime != null) {
            return metricResultRepository.findByServerIdAndRecordedAtBetween(serverId, startTime, endTime);
        } else {
            return metricResultRepository.findLatestSuccessfulMetricsByServerId(serverId);
        }
    }

    /**
     * Metrik query'lerini map olarak getirir
     */
    private Map<UUID, MetricQuery> getMetricQueries(List<MetricResult> metrics) {
        Map<UUID, MetricQuery> queryMap = new HashMap<>();
        for (MetricResult metric : metrics) {
            if (metric.getMetricQueryId() != null && !queryMap.containsKey(metric.getMetricQueryId())) {
                MetricQuery query = metricQueryRepository.findById(metric.getMetricQueryId()).orElse(null);
                if (query != null) {
                    queryMap.put(metric.getMetricQueryId(), query);
                }
            }
        }
        return queryMap;
    }

    /**
     * Server istatistiklerini hesaplar
     */
    private Map<String, Object> calculateServerStats(UUID serverId) {
        long currentTime = System.currentTimeMillis();
        long last24Hours = currentTime - ServerConstants.ONE_DAY_MS;
        long lastHour = currentTime - ServerConstants.ONE_HOUR_MS;

        Map<String, Object> stats = new HashMap<>();

        // Son 24 saat istatistikleri
        ServerStats stats24h = getServerStatsForPeriod(serverId, last24Hours);
        // Son 1 saat istatistikleri  
        ServerStats stats1h = getServerStatsForPeriod(serverId, lastHour);

        stats.put("last24Hours", stats24h.toMap());
        stats.put("lastHour", stats1h.toMap());

        return stats;
    }

    /**
     * Belirli dönem için server istatistiklerini getirir
     */
    private ServerStats getServerStatsForPeriod(UUID serverId, long fromTime) {
        Long successful = metricResultRepository.getSuccessfulMetricCount(serverId, fromTime);
        Long failed = metricResultRepository.getFailedMetricCount(serverId, fromTime);
        Double avgExecution = metricResultRepository.getAverageExecutionTime(serverId, fromTime);

        return new ServerStats(
                successful != null ? successful : 0,
                failed != null ? failed : 0,
                avgExecution != null ? avgExecution.longValue() : 0
        );
    }

    /**
     * Server istatistikleri için helper class
     */
    private static class ServerStats {
        private final long successful;
        private final long failed;
        private final long avgExecutionTimeMs;

        public ServerStats(long successful, long failed, long avgExecutionTimeMs) {
            this.successful = successful;
            this.failed = failed;
            this.avgExecutionTimeMs = avgExecutionTimeMs;
        }

        public Map<String, Object> toMap() {
            long total = successful + failed;
            double successRate = total > 0 ? (successful * 100.0) / total : 0.0;

            return Map.of(
                    "successful", successful,
                    "failed", failed,
                    "total", total,
                    "successRate", successRate,
                    "avgExecutionTimeMs", avgExecutionTimeMs
            );
        }
    }
}
