package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.config.MetricCollectionProperties;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricResult;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;

import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import com.dbauth.servermanagement.domain.repository.MetricResultRepository;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import com.dbauth.shared.security.PasswordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MetricCollectionService {

    private final ServerRepository serverRepository;
    private final MetricQueryRepository metricQueryRepository;
    private final MetricResultRepository metricResultRepository;
    private final MetricCollectionProperties metricProperties;
    private final PasswordUtil passwordUtil;

    @Scheduled(fixedRateString = "${metric.collection.interval-ms:300000}")
    public void collectMetricsForAllServers() {
        if (!metricProperties.isEnabled()) {
            log.debug("⏸️ Metrik toplama devre dışı, işlem atlanıyor");
            return;
        }

        log.info("🚀 Tüm sunucular için metrik toplama işlemi başlatıldı (interval: {}ms)",
            metricProperties.getIntervalMs());
        
        List<Server> activeServers = serverRepository.findByIsActive(true);
        
        if (activeServers.isEmpty()) {
            log.warn("⚠️ Aktif sunucu bulunamadı, metrik toplama işlemi atlanıyor");
            return;
        }

        int totalMetrics = 0;
        int successfulMetrics = 0;
        int failedMetrics = 0;

        for (Server server : activeServers) {
            try {
                log.info("📊 Sunucu için metrik toplama başlatıldı: {} ({})", 
                    server.getName(), server.getDatabaseType());
                
                List<MetricQuery> activeQueries = metricQueryRepository
                    .findByDatabaseTypeAndIsActiveOrderByExecutionOrder(server.getDatabaseType(), true);
                
                if (activeQueries.isEmpty()) {
                    log.warn("⚠️ Sunucu {} için aktif metrik sorgusu bulunamadı", server.getName());
                    continue;
                }

                for (MetricQuery query : activeQueries) {
                    totalMetrics++;
                    try {
                        // Tüm metrikler scheduled olarak çalışabilir

                        MetricResult result = executeMetricQuery(server, query);
                        metricResultRepository.save(result);

                        if (result.getSuccess()) {
                            successfulMetrics++;
                            if (metricProperties.isDebugLogging()) {
                                log.debug("✅ Metrik başarılı: {} - {} ({}ms)",
                                    server.getName(), query.getQueryName(), result.getExecutionTimeMs());
                            }
                        } else {
                            failedMetrics++;
                            log.warn("❌ Metrik başarısız: {} - {} - {}",
                                server.getName(), query.getQueryName(), result.getErrorMessage());
                        }
                    } catch (Exception e) {
                        failedMetrics++;
                        log.error("💥 Metrik çalıştırma hatası: {} - {} - {}", 
                            server.getName(), query.getQueryName(), e.getMessage());
                        
                        // Hata durumunda da kaydet
                        MetricResult errorResult = MetricResult.builder()
                            .serverId(server.getId())
                            .metricQueryId(query.getId())
                            .metricName(query.getQueryName())
                            .sqlQuery(query.getSqlQuery())
                            .success(false)
                            .errorMessage("Sistem hatası: " + e.getMessage())
                            .executionTimeMs(0L)
                            .recordedAtNow()
                            .build();
                        
                        metricResultRepository.save(errorResult);
                    }
                }
                
            } catch (Exception e) {
                log.error("💥 Sunucu {} için metrik toplama genel hatası: {}", 
                    server.getName(), e.getMessage());
            }
        }

        log.info("🏁 Metrik toplama işlemi tamamlandı - {} sunucu, {} toplam metrik, {} başarılı, {} başarısız", 
            activeServers.size(), totalMetrics, successfulMetrics, failedMetrics);
    }

    private MetricResult executeMetricQuery(Server server, MetricQuery query) {
        long startTime = System.currentTimeMillis();

        try {
            String jdbcUrl = buildJdbcUrl(server);

            // Handle password decryption - check if it's already encrypted
            String decryptedPassword = server.getPassword();
            if (decryptedPassword != null && !decryptedPassword.isEmpty()) {
                try {
                    // Check if password looks like it's encrypted (Base64 encoded)
                    if (isEncryptedPassword(decryptedPassword)) {
                        decryptedPassword = passwordUtil.decrypt(decryptedPassword);
                        log.debug("Successfully decrypted password for metric collection: {}", server.getName());
                    } else {
                        log.debug("Using plain text password for metric collection: {} (likely new server)", server.getName());
                    }
                } catch (Exception e) {
                    log.warn("Failed to decrypt password for server {}, trying as plain text: {}",
                        server.getName(), e.getMessage());
                    // If decryption fails, use the original password (might be plain text)
                }
            }

            try (Connection connection = DriverManager.getConnection(
                    jdbcUrl, server.getUsername(), decryptedPassword)) {
                
                // Connection timeout - dinamik değer
                connection.setNetworkTimeout(null, metricProperties.getConnectionTimeoutMs());
                
                List<Map<String, Object>> resultData = executeQuery(connection, query.getSqlQuery());
                long executionTime = System.currentTimeMillis() - startTime;
                
                return MetricResult.builder()
                    .serverId(server.getId())
                    .metricQueryId(query.getId())
                    .metricName(query.getQueryName())
                    .sqlQuery(query.getSqlQuery())
                    .resultData(createResultMap(resultData))
                    .rowCount((long) resultData.size())
                    .executionTimeMs(executionTime)
                    .success(true)
                    .recordedAtNow()
                    .build();
                    
            }
        } catch (SQLException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            return MetricResult.builder()
                .serverId(server.getId())
                .metricQueryId(query.getId())
                .metricName(query.getQueryName())
                .sqlQuery(query.getSqlQuery())
                .executionTimeMs(executionTime)
                .success(false)
                .errorMessage("SQL Hatası: " + e.getMessage())
                .errorCode(String.valueOf(e.getErrorCode()))
                .recordedAtNow()
                .build();
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            return MetricResult.builder()
                .serverId(server.getId())
                .metricQueryId(query.getId())
                .metricName(query.getQueryName())
                .sqlQuery(query.getSqlQuery())
                .executionTimeMs(executionTime)
                .success(false)
                .errorMessage("Bağlantı hatası: " + e.getMessage())
                .recordedAtNow()
                .build();
        }
    }

    private List<Map<String, Object>> executeQuery(Connection connection, String sqlQuery) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (PreparedStatement statement = connection.prepareStatement(sqlQuery)) {
            statement.setQueryTimeout(metricProperties.getQueryTimeoutSeconds());
            
            try (ResultSet resultSet = statement.executeQuery()) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                // Maksimum satır sayısı - dinamik değer
                int maxRows = metricProperties.getMaxResultRows();
                int rowCount = 0;
                
                while (resultSet.next() && rowCount < maxRows) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnLabel(i);
                        Object value = resultSet.getObject(i);
                        
                        // Büyük string'leri kısalt - dinamik limit
                        if (value instanceof String && ((String) value).length() > metricProperties.getStringTruncateLimit()) {
                            value = ((String) value).substring(0, metricProperties.getStringTruncateLimit()) + "... (kısaltıldı)";
                        }
                        
                        row.put(columnName, value);
                    }
                    
                    results.add(row);
                    rowCount++;
                }
                
                if (rowCount >= maxRows) {
                    Map<String, Object> warningRow = new HashMap<>();
                    warningRow.put("UYARI", "Sonuç " + maxRows + " satırla sınırlandırıldı");
                    results.add(warningRow);
                }
            }
        }
        
        return results;
    }

    private Map<String, Object> createResultMap(List<Map<String, Object>> data) {
        Map<String, Object> result = new HashMap<>();
        result.put("rows", data);
        result.put("rowCount", data.size());
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    private String buildJdbcUrl(Server server) {
        switch (server.getDatabaseType()) {
            case POSTGRESQL:
                return String.format("jdbc:postgresql://%s:%d/%s",
                    server.getHost(), server.getPort(), server.getActualDatabaseName());
            case MYSQL:
                return String.format("jdbc:mysql://%s:%d/%s",
                    server.getHost(), server.getPort(), server.getActualDatabaseName());
            case ORACLE:
                return String.format("**************************",
                    server.getHost(), server.getPort(), server.getActualDatabaseName());
            case SQL_SERVER:
                return String.format("********************************************************************************",
                    server.getHost(), server.getPort(), server.getActualDatabaseName());
            default:
                throw new RuntimeException("Desteklenmeyen veritabanı tipi: " + server.getDatabaseType());
        }
    }

    /**
     * Belirli bir sunucu için metrik toplama işlemi yapar
     * Yeni sunucu eklendiğinde hemen metrik çekmek için kullanılır
     */
    public void collectMetricsForSingleServer(UUID serverId) {
        log.info("🎯 Tek sunucu için metrik toplama işlemi başlatıldı: {}", serverId);

        Server server = serverRepository.findById(serverId)
                .orElseThrow(() -> new RuntimeException("Sunucu bulunamadı: " + serverId));

        if (!server.getIsActive()) {
            log.warn("⚠️ Sunucu aktif değil, metrik toplama işlemi atlanıyor: {}", server.getName());
            return;
        }

        List<MetricQuery> activeQueries = metricQueryRepository
                .findByDatabaseTypeAndIsActiveOrderByExecutionOrder(server.getDatabaseType(), true);

        if (activeQueries.isEmpty()) {
            log.warn("⚠️ Sunucu {} için aktif metrik sorgusu bulunamadı", server.getName());
            return;
        }

        int totalMetrics = 0;
        int successfulMetrics = 0;
        int failedMetrics = 0;

        try {
            log.info("📊 Sunucu için metrik toplama başlatıldı: {} ({})",
                server.getName(), server.getDatabaseType());

            for (MetricQuery query : activeQueries) {
                totalMetrics++;
                try {
                    // Tüm metrikler çalışabilir

                    MetricResult result = executeMetricQuery(server, query);
                    metricResultRepository.save(result);

                    if (result.getSuccess()) {
                        successfulMetrics++;
                        if (metricProperties.isDebugLogging()) {
                            log.debug("✅ Metrik başarılı: {} - {} ({}ms)",
                                server.getName(), query.getQueryName(), result.getExecutionTimeMs());
                        }
                    } else {
                        failedMetrics++;
                        log.warn("❌ Metrik başarısız: {} - {} - {}",
                            server.getName(), query.getQueryName(), result.getErrorMessage());
                    }
                } catch (Exception e) {
                    failedMetrics++;
                    log.error("💥 Metrik çalıştırma hatası: {} - {} - {}",
                        server.getName(), query.getQueryName(), e.getMessage());

                    // Hata durumunda da kaydet
                    MetricResult errorResult = MetricResult.builder()
                        .serverId(server.getId())
                        .metricQueryId(query.getId())
                        .metricName(query.getQueryName())
                        .sqlQuery(query.getSqlQuery())
                        .success(false)
                        .errorMessage("Sistem hatası: " + e.getMessage())
                        .executionTimeMs(0L)
                        .recordedAtNow()
                        .build();

                    metricResultRepository.save(errorResult);
                }
            }

        } catch (Exception e) {
            log.error("💥 Sunucu {} için metrik toplama genel hatası: {}",
                server.getName(), e.getMessage());
        }

        log.info("🏁 Tek sunucu metrik toplama tamamlandı: {} - {} toplam metrik, {} başarılı, {} başarısız",
            server.getName(), totalMetrics, successfulMetrics, failedMetrics);
    }

    // Eski kayıtları temizleme - dinamik konfigürasyon
    @Scheduled(cron = "${metric.collection.cleanup-cron:0 0 2 * * ?}")
    public void cleanupOldMetrics() {
        if (!metricProperties.isEnabled()) {
            log.debug("⏸️ Metrik toplama devre dışı, cleanup işlemi atlanıyor");
            return;
        }

        long retentionMs = metricProperties.getCleanupRetentionDays() * 24L * 60 * 60 * 1000;
        long cutoffTime = System.currentTimeMillis() - retentionMs;
        int deletedCount = metricResultRepository.deleteOldMetrics(cutoffTime);
        log.info("🧹 Eski metrik kayıtları temizlendi: {} kayıt silindi ({}+ gün öncesi)",
            deletedCount, metricProperties.getCleanupRetentionDays());
    }

    /**
     * Check if password appears to be encrypted (Base64 encoded)
     */
    private boolean isEncryptedPassword(String password) {
        if (password == null || password.isEmpty()) {
            return false;
        }

        // Simple check: if it's valid Base64 and ends with padding, it's likely encrypted
        try {
            java.util.Base64.getDecoder().decode(password);
            // AES encrypted passwords always end with = or == (Base64 padding)
            return password.endsWith("=") || password.endsWith("==");
        } catch (IllegalArgumentException e) {
            // Not valid Base64, so it's plain text
            return false;
        }
    }
}
