package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServerDto {
    
    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private DatabaseType databaseType;
    private String username;
    private String password; // Encrypted password for internal service communication
    private String description;

    // MSSQL Always On Availability Groups için replica tipi
    private ReplicaType replicaType;

    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
