package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.api.dto.ServerDetailDto;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricResult;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Metric Display Service - Application Layer
 * Onion Architecture: Metrik display logic için ayrı service
 * Clean Code: Single Responsibility, sadece metric display operations
 * Refactored: Büyük ServerService'den ayrıldı
 */
@Service
@NoArgsConstructor
@Slf4j
public class MetricDisplayService {

    /**
     * MetricResult ve MetricQuery'yi display data'ya dönüştürür
     */
    public ServerDetailDto.MetricDisplayData convertToMetricDisplayData(MetricResult metricResult, MetricQuery metricQuery) {
        List<ServerDetailDto.MetricDisplayData.DisplayField> displayFields = new ArrayList<>();
        Map<String, Object> groupConfig = null;
        Map<String, Object> tableConfig = null;

        if (metricQuery != null && metricQuery.getDisplayMapping() != null && metricResult.getResultData() != null) {
            Map<String, Object> displayMapping = metricQuery.getDisplayMapping();

            // Group config ve table config'i al
            groupConfig = (Map<String, Object>) displayMapping.get("groupConfig");
            tableConfig = (Map<String, Object>) displayMapping.get("tableConfig");

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> fieldMappings = (List<Map<String, Object>>) displayMapping.get("displayFields");

            if (fieldMappings != null && metricResult.getResultData().get("rows") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> rows = (List<Map<String, Object>>) metricResult.getResultData().get("rows");

                // Data type'a göre işle
                if ("comparative".equals(metricQuery.getDataType()) && rows.size() > 1) {
                    displayFields = processComparativeData(rows, fieldMappings);
                } else if (!rows.isEmpty()) {
                    displayFields = processSnapshotData(rows.get(0), fieldMappings);
                }
            }
        }

        return ServerDetailDto.MetricDisplayData.builder()
                .metricName(metricResult.getMetricName())
                .metricType("MIXED") // Display field'larda farklı tipler olabilir
                .dataType(metricQuery != null ? metricQuery.getDataType() : "snapshot")
                .defaultTimeRange(metricQuery != null ? metricQuery.getDefaultTimeRange() : "1h")
                .supportsTimeRange(metricQuery != null ? metricQuery.getSupportsTimeRange() : false)
                .success(metricResult.getSuccess())
                .executionTimeMs(metricResult.getExecutionTimeMs())
                .recordedAt(metricResult.getRecordedAt())
                .displayFields(displayFields)
                .groupConfig(groupConfig)
                .tableConfig(tableConfig)
                .build();
    }

    /**
     * Snapshot/aggregated data için tek satırı işler
     */
    private List<ServerDetailDto.MetricDisplayData.DisplayField> processSnapshotData(
            Map<String, Object> row, List<Map<String, Object>> fieldMappings) {

        List<ServerDetailDto.MetricDisplayData.DisplayField> displayFields = new ArrayList<>();

        for (Map<String, Object> fieldMapping : fieldMappings) {
            String fieldName = (String) fieldMapping.get("fieldName");
            String displayName = (String) fieldMapping.get("displayName");
            String unit = (String) fieldMapping.get("unit");
            String type = (String) fieldMapping.get("type");
            String category = (String) fieldMapping.get("category");
            String description = (String) fieldMapping.get("description");
            String visualType = (String) fieldMapping.get("visualType");
            String dataType = (String) fieldMapping.get("dataType");

            @SuppressWarnings("unchecked")
            Map<String, Object> thresholds = (Map<String, Object>) fieldMapping.get("thresholds");
            @SuppressWarnings("unchecked")
            Map<String, Object> chartConfig = (Map<String, Object>) fieldMapping.get("chartConfig");

            Object value = row.get(fieldName);
            String formattedValue = formatValue(value, type, unit);

            displayFields.add(ServerDetailDto.MetricDisplayData.DisplayField.builder()
                    .fieldName(fieldName)
                    .displayName(displayName)
                    .value(value)
                    .unit(unit)
                    .type(type)
                    .category(category)
                    .description(description)
                    .formattedValue(formattedValue)
                    .visualType(visualType)
                    .dataType(dataType)
                    .thresholds(thresholds)
                    .chartConfig(chartConfig)
                    .build());
        }

        return displayFields;
    }

    /**
     * Comparative data için tüm satırları işler
     */
    private List<ServerDetailDto.MetricDisplayData.DisplayField> processComparativeData(
            List<Map<String, Object>> rows, List<Map<String, Object>> fieldMappings) {

        List<ServerDetailDto.MetricDisplayData.DisplayField> displayFields = new ArrayList<>();

        // Comparative data için tüm satırları tek bir field olarak döndür
        if (!fieldMappings.isEmpty()) {
            Map<String, Object> firstFieldMapping = fieldMappings.get(0);

            displayFields.add(ServerDetailDto.MetricDisplayData.DisplayField.builder()
                    .fieldName("comparative_data")
                    .displayName((String) firstFieldMapping.get("displayName"))
                    .value(rows) // Tüm satırları value olarak ver
                    .type("comparative")
                    .category((String) firstFieldMapping.get("category"))
                    .visualType("table")
                    .dataType("comparative")
                    .build());
        }

        return displayFields;
    }

    /**
     * Value'yu type ve unit'e göre formatlar
     */
    private String formatValue(Object value, String type, String unit) {
        if (value == null) return "N/A";

        try {
            return switch (type != null ? type.toLowerCase() : "string") {
                case "bytes" -> formatBytes(((Number) value).longValue());
                case "percentage" -> {
                    DecimalFormat percentFormat = new DecimalFormat("#0.00");
                    yield percentFormat.format(((Number) value).doubleValue()) + "%";
                }
                case "number" -> {
                    DecimalFormat numberFormat = new DecimalFormat("#,##0");
                    yield numberFormat.format(((Number) value).longValue()) + (unit != null ? " " + unit : "");
                }
                default -> value.toString() + (unit != null && !unit.isEmpty() ? " " + unit : "");
            };
        } catch (Exception e) {
            log.warn("Error formatting value: {} with type: {} and unit: {}", value, type, unit, e);
            return value.toString();
        }
    }

    /**
     * Byte değerlerini human-readable formata çevirir
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
