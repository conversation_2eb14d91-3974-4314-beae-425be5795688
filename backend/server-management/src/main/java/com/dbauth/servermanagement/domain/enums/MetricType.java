package com.dbauth.servermanagement.domain.enums;

import lombok.Getter;

/**
 * Metric Type Enum - Domain Layer
 * Metrik çekme ve kaydetme türlerini tanımlar
 */
@Getter
public enum MetricType {
    /**
     * Son metrik sonucu - Zaman aralığı gerektirmez
     * Örnek: Anlık CPU kullanımı, mevcut bağlantı sayısı
     */
    LATEST("Latest", "Son Değer", false, false),
    
    /**
     * Ortalama sorgu sonucu - Zaman aralığı gerektirir, sayısal ortalama
     * Örnek: Son 1 saatteki ortalama CPU kullanımı
     */
    AVERAGE("Average", "Ortalama", true, true),
    
    /**
     * Aralık değeri sonucu - Zaman aralığı gerektirir, aralıktaki tüm veriler
     * Örnek: Son 1 saatteki tüm CPU değerleri
     */
    RANGE("Range", "Aralık", true, false);

    private final String code;
    private final String displayName;
    private final boolean requiresTimeRange;
    private final boolean requiresNumericData;

    MetricType(String code, String displayName, boolean requiresTimeRange, boolean requiresNumericData) {
        this.code = code;
        this.displayName = displayName;
        this.requiresTimeRange = requiresTimeRange;
        this.requiresNumericData = requiresNumericData;
    }

    /**
     * Metrik türünün zaman aralığı gerektirip gerektirmediğini kontrol eder
     */
    public boolean needsTimeRange() {
        return requiresTimeRange;
    }

    /**
     * Metrik türünün sayısal veri gerektirip gerektirmediğini kontrol eder
     */
    public boolean needsNumericData() {
        return requiresNumericData;
    }

    /**
     * String değerden MetricType'a dönüştürme
     */
    public static MetricType fromCode(String code) {
        for (MetricType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Geçersiz metrik türü: " + code);
    }
}
