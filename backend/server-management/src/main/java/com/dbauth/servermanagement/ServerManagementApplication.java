package com.dbauth.servermanagement;

import com.dbauth.servermanagement.config.MetricCollectionProperties;
import com.dbauth.servermanagement.config.SchedulerProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Server Management Application - Sunucu izleme ve yönetimi
 * Merkezi CORS sistemi ile entegre
 */
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({MetricCollectionProperties.class, SchedulerProperties.class})
@ComponentScan(basePackages = {
    "com.dbauth.servermanagement",
    "com.dbauth.shared"
})
public class ServerManagementApplication {

    public static void main(String[] args) {
        // Server adını sistem property olarak ayarla (loglama için)
        System.setProperty("server.name", "server-management");
        SpringApplication.run(ServerManagementApplication.class, args);
    }
}
