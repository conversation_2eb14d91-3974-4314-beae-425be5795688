package com.dbauth.servermanagement.infrastructure.mapper;

import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.infrastructure.entity.ServerEntity;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Server Entity Mapper - Infrastructure Layer
 * Onion Architecture: Domain ve Infrastructure entity'leri arasında mapping
 * Clean Code: Lombok ile temiz kod, Single Responsibility
 */
@Component
@NoArgsConstructor
public class ServerEntityMapper {

    /**
     * Domain Server'ı Infrastructure ServerEntity'ye dönüştürür
     */
    public ServerEntity toEntity(Server domain) {
        if (domain == null) {
            return null;
        }

        return ServerEntity.builder()
                .id(domain.getId())
                .name(domain.getName())
                .host(domain.getHost())
                .port(domain.getPort())
                .databaseType(domain.getDatabaseType())
                .username(domain.getUsername())
                .password(domain.getPassword())
                .description(domain.getDescription())
                .replicaType(domain.getReplicaType())
                .isActive(domain.getIsActive())
                .createdAt(domain.getCreatedAt())
                .updatedAt(domain.getUpdatedAt())
                .build();
    }

    /**
     * Infrastructure ServerEntity'yi Domain Server'a dönüştürür
     */
    public Server toDomain(ServerEntity entity) {
        if (entity == null) {
            return null;
        }

        return Server.builder()
                .id(entity.getId())
                .name(entity.getName())
                .host(entity.getHost())
                .port(entity.getPort())
                .databaseType(entity.getDatabaseType())
                .username(entity.getUsername())
                .password(entity.getPassword())
                .description(entity.getDescription())
                .replicaType(entity.getReplicaType())
                .isActive(entity.getIsActive())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}
