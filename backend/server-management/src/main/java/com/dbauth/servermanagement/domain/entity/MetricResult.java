package com.dbauth.servermanagement.domain.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "metric_results", indexes = {
    @Index(name = "idx_metric_result_server_id", columnList = "server_id"),
    @Index(name = "idx_metric_result_metric_query_id", columnList = "metric_query_id"),
    @Index(name = "idx_metric_result_recorded_at", columnList = "recorded_at"),
    @Index(name = "idx_metric_result_server_recorded", columnList = "server_id, recorded_at"),
    @Index(name = "idx_metric_result_success", columnList = "success")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricResult {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "server_id", nullable = false)
    private UUID serverId;

    @Column(name = "metric_query_id")
    private UUID metricQueryId;

    @Column(name = "metric_name", nullable = false, length = 255)
    private String metricName;

    @Column(name = "sql_query", nullable = false, columnDefinition = "TEXT")
    private String sqlQuery;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "result_data", columnDefinition = "jsonb")
    private Map<String, Object> resultData;

    @Column(name = "row_count")
    private Long rowCount;

    @Column(name = "execution_time_ms", nullable = false)
    private Long executionTimeMs;

    @Column(name = "success", nullable = false)
    private Boolean success;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "recorded_at", nullable = false)
    private Long recordedAt; // Unix timestamp in milliseconds

    // Helper methods for timestamp conversion
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    public java.time.LocalDateTime getRecordedAtAsLocalDateTime() {
        return java.time.LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(recordedAt),
            java.time.ZoneId.systemDefault()
        );
    }

    public java.util.Date getRecordedAtAsDate() {
        return new java.util.Date(recordedAt);
    }

    // Builder helper for current timestamp
    public static class MetricResultBuilder {
        public MetricResultBuilder recordedAtNow() {
            this.recordedAt = getCurrentTimestamp();
            return this;
        }
    }
}
