package com.dbauth.servermanagement.application.mapper;

import com.dbauth.servermanagement.application.dto.CreateServerApplicationDto;
import com.dbauth.servermanagement.application.dto.ServerApplicationDto;
import com.dbauth.servermanagement.application.dto.UpdateServerApplicationDto;
import com.dbauth.servermanagement.domain.entity.Server;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Server Application Mapper - Application Layer
 * Onion Architecture: Domain ve Application DTO'ları arasında mapping
 * Clean Code: Lombok ile temiz kod, business logic mapping
 */
@Component
@NoArgsConstructor
public class ServerApplicationMapper {

    /**
     * Domain Server'ı Application DTO'ya dönüştürür
     */
    public ServerApplicationDto toDto(Server domain) {
        if (domain == null) {
            return null;
        }

        return ServerApplicationDto.builder()
                .id(domain.getId())
                .name(domain.getName())
                .host(domain.getHost())
                .port(domain.getPort())
                .databaseType(domain.getDatabaseType())
                .username(domain.getUsername())
                .password(domain.getPassword())
                .description(domain.getDescription())
                .replicaType(domain.getReplicaType())
                .isActive(domain.getIsActive())
                .createdAt(domain.getCreatedAt())
                .updatedAt(domain.getUpdatedAt())
                .build();
    }

    /**
     * Application DTO'yu Domain Server'a dönüştürür
     */
    public Server toDomain(ServerApplicationDto dto) {
        if (dto == null) {
            return null;
        }

        return Server.builder()
                .id(dto.getId())
                .name(dto.getName())
                .host(dto.getHost())
                .port(dto.getPort())
                .databaseType(dto.getDatabaseType())
                .username(dto.getUsername())
                .password(dto.getPassword())
                .description(dto.getDescription())
                .replicaType(dto.getReplicaType())
                .isActive(dto.getIsActive())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * Create DTO'yu Domain Server'a dönüştürür
     */
    public Server toDomain(CreateServerApplicationDto createDto) {
        if (createDto == null) {
            return null;
        }

        // Apply defaults before conversion
        createDto.applyDefaults();

        Server server = Server.builder()
                .name(createDto.getName())
                .host(createDto.getHost())
                .port(createDto.getPort())
                .databaseType(createDto.getDatabaseType())
                .username(createDto.getUsername())
                .password(createDto.getPassword())
                .description(createDto.getDescription())
                .replicaType(createDto.getReplicaType())
                .isActive(createDto.getIsActive())
                .build();

        // Set timestamps
        server.updateTimestamps();
        
        return server;
    }

    /**
     * Update DTO ile mevcut Domain Server'ı günceller
     */
    public Server updateDomain(Server existingServer, UpdateServerApplicationDto updateDto) {
        if (existingServer == null || updateDto == null) {
            return existingServer;
        }

        if (updateDto.hasName()) {
            existingServer.setName(updateDto.getName());
        }
        if (updateDto.hasHost()) {
            existingServer.setHost(updateDto.getHost());
        }
        if (updateDto.hasPort()) {
            existingServer.setPort(updateDto.getPort());
        }
        if (updateDto.hasDatabaseType()) {
            existingServer.setDatabaseType(updateDto.getDatabaseType());
        }
        if (updateDto.hasUsername()) {
            existingServer.setUsername(updateDto.getUsername());
        }
        if (updateDto.hasPassword()) {
            existingServer.setPassword(updateDto.getPassword());
        }
        if (updateDto.hasDescription()) {
            existingServer.setDescription(updateDto.getDescription());
        }
        if (updateDto.hasIsActive()) {
            existingServer.setIsActive(updateDto.getIsActive());
        }
        if (updateDto.hasReplicaType()) {
            existingServer.setReplicaType(updateDto.getReplicaType());
        }

        // Update timestamp
        existingServer.setUpdatedAt(LocalDateTime.now());

        return existingServer;
    }
}
