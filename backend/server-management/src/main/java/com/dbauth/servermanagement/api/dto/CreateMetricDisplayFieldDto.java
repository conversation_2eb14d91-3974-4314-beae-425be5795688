package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMetricDisplayFieldDto {

    @NotBlank(message = "Kolon adı boş olamaz")
    @Size(max = 100, message = "Kolon adı en fazla 100 karakter olabilir")
    private String columnName;

    @NotBlank(message = "Görüntülenecek ad boş olamaz")
    @Size(max = 100, message = "Görüntülenecek ad en fazla 100 karakter olabilir")
    private String displayName;

    @NotBlank(message = "Veri tipi boş olamaz")
    @Size(max = 20, message = "Veri tipi en fazla 20 karakter olabilir")
    private String dataType; // string, int

    @NotNull(message = "Metrik tipi boş olamaz")
    private MetricType metricType; // LATEST, AVERAGE, RANGE

    @NotNull(message = "Bileşen tipi boş olamaz")
    private ComponentType componentType; // UI bileşen türü

    private Map<String, Object> componentConfig; // Bileşen konfigürasyonu

    @Size(max = 500, message = "Açıklama en fazla 500 karakter olabilir")
    private String description;

    private Integer displayOrder = 0;

    private Boolean isActive = true;
}
