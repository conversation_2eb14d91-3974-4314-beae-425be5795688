package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMetricQueryDto {

    @NotBlank(message = "Sorgu adı boş olamaz")
    @Size(max = 100, message = "Sorgu adı en fazla 100 karakter olabilir")
    private String queryName;

    @Size(max = 500, message = "Sorgu açıklaması en fazla 500 karakter olabilir")
    private String queryDescription;

    @NotNull(message = "Veritabanı tipi boş olamaz")
    private DatabaseType databaseType;

    @NotBlank(message = "SQL sorgusu boş olamaz")
    private String sqlQuery;

    @Size(max = 50, message = "Metrik kategorisi en fazla 50 karakter olabilir")
    private String metricCategory;

    @Valid
    private List<CreateMetricDisplayFieldDto> displayFields;

    private Boolean isActive = true;
    private Integer executionOrder = 0;
}
