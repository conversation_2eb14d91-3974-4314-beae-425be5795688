package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.api.dto.MetricTestResultDto;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Metrik Test Service
 * Metrik sorgularının test edilmesi için business logic
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MetricTestService {

    private final MetricQueryRepository metricQueryRepository;
    private final ServerRepository serverRepository;

    /**
     * Tek bir metrik sorgusunu test eder
     */
    public MetricTestResultDto testSingleQuery(UUID serverId, UUID queryId) {
        log.debug("Tek metrik sorgusu test ediliyor - Server: {}, Query: {}", serverId, queryId);

        Optional<Server> serverOpt = serverRepository.findById(serverId);
        if (serverOpt.isEmpty()) {
            throw new RuntimeException("Server not found: " + serverId);
        }

        Optional<MetricQuery> queryOpt = metricQueryRepository.findById(queryId);
        if (queryOpt.isEmpty()) {
            throw new RuntimeException("Metric query not found: " + queryId);
        }

        Server server = serverOpt.get();
        MetricQuery query = queryOpt.get();

        return executeTestQuery(server, query);
    }

    /**
     * Belirli bir sunucu için tüm aktif metrik sorgularını test eder
     */
    public List<MetricTestResultDto> testAllQueriesForServer(UUID serverId) {
        log.debug("Sunucu için tüm metrik sorguları test ediliyor: {}", serverId);

        Optional<Server> serverOpt = serverRepository.findById(serverId);
        if (serverOpt.isEmpty()) {
            throw new RuntimeException("Server not found: " + serverId);
        }

        Server server = serverOpt.get();
        List<MetricQuery> queries = metricQueryRepository.findByDatabaseTypeAndIsActiveTrue(server.getDatabaseType());

        return queries.stream()
                .map(query -> executeTestQuery(server, query))
                .collect(Collectors.toList());
    }

    /**
     * Belirli bir veritabanı türü için metrik sorgularını test eder
     */
    public List<MetricTestResultDto> testQueriesByDatabaseType(DatabaseType databaseType) {
        log.debug("Veritabanı türü için metrik sorguları test ediliyor: {}", databaseType);

        List<MetricQuery> queries = metricQueryRepository.findByDatabaseTypeAndIsActiveTrue(databaseType);

        return queries.stream()
                .map(this::validateQueryOnly)
                .collect(Collectors.toList());
    }

    /**
     * Belirli bir kategori için metrik sorgularını test eder
     */
    public List<MetricTestResultDto> testQueriesByCategory(String metricCategory) {
        log.debug("Kategori için metrik sorguları test ediliyor: {}", metricCategory);

        List<MetricQuery> queries = metricQueryRepository.findByMetricCategoryAndIsActiveTrue(metricCategory);

        return queries.stream()
                .map(this::validateQueryOnly)
                .collect(Collectors.toList());
    }

    /**
     * Belirli bir metrik türü için metrik sorgularını test eder
     */
    public List<MetricTestResultDto> testQueriesByMetricType(MetricType metricType) {
        log.debug("Metrik türü için metrik sorguları test ediliyor: {}", metricType);

        List<MetricQuery> queries = metricQueryRepository.findByIsActiveTrue(); // MetricType artık display field seviyesinde

        return queries.stream()
                .map(this::validateQueryOnly)
                .collect(Collectors.toList());
    }

    /**
     * Metrik sorgusunun geçerliliğini kontrol eder
     */
    public MetricTestResultDto validateQuery(UUID queryId) {
        log.debug("Metrik sorgusu doğrulanıyor: {}", queryId);

        Optional<MetricQuery> queryOpt = metricQueryRepository.findById(queryId);
        if (queryOpt.isEmpty()) {
            throw new RuntimeException("Metric query not found: " + queryId);
        }

        MetricQuery query = queryOpt.get();
        return validateQueryOnly(query);
    }

    /**
     * Tüm aktif metrik sorgularını test eder
     */
    public List<MetricTestResultDto> testAllActiveQueries() {
        log.debug("Tüm aktif metrik sorguları test ediliyor");

        List<MetricQuery> queries = metricQueryRepository.findByIsActiveTrue();

        return queries.stream()
                .map(this::validateQueryOnly)
                .collect(Collectors.toList());
    }

    /**
     * Test sonuçlarının özetini getirir
     */
    public Map<String, Object> getTestResultsSummary() {
        log.debug("Test sonuçları özeti getiriliyor");

        long totalQueries = metricQueryRepository.count();
        long activeQueries = metricQueryRepository.countByIsActiveTrue();
        long inactiveQueries = totalQueries - activeQueries;

        Map<String, Long> byDatabaseType = new HashMap<>();
        for (DatabaseType dbType : DatabaseType.values()) {
            long count = metricQueryRepository.countByDatabaseTypeAndIsActiveTrue(dbType);
            if (count > 0) {
                byDatabaseType.put(dbType.name(), count);
            }
        }

        // MetricType artık display field seviyesinde - bu istatistik kaldırıldı
        Map<String, Long> byMetricType = new HashMap<>();
        byMetricType.put("MIXED", activeQueries); // Tüm sorgular mixed olarak sayılır

        Map<String, Object> summary = new HashMap<>();
        summary.put("totalQueries", totalQueries);
        summary.put("activeQueries", activeQueries);
        summary.put("inactiveQueries", inactiveQueries);
        summary.put("byDatabaseType", byDatabaseType);
        summary.put("byMetricType", byMetricType);
        summary.put("generatedAt", System.currentTimeMillis());

        return summary;
    }

    /**
     * Metrik sorgusunu gerçek sunucuda çalıştırır
     */
    private MetricTestResultDto executeTestQuery(Server server, MetricQuery query) {
        long startTime = System.currentTimeMillis();

        try {
            log.debug("Metrik sorgusu çalıştırılıyor: {} - {}", query.getQueryName(), server.getName());

            // Basit doğrulama - gerçek veritabanı bağlantısı olmadan
            if (query.getSqlQuery() == null || query.getSqlQuery().trim().isEmpty()) {
                return MetricTestResultDto.failure(
                        query.getId(),
                        query.getQueryName(),
                        query.getSqlQuery(),
                        System.currentTimeMillis() - startTime,
                        "SQL query is empty",
                        "EMPTY_QUERY"
                );
            }

            // Mock başarılı sonuç
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("test_result", "success");
            resultData.put("query_validated", true);

            MetricTestResultDto result = MetricTestResultDto.success(
                    query.getId(),
                    query.getQueryName(),
                    query.getSqlQuery(),
                    System.currentTimeMillis() - startTime,
                    1L,
                    resultData
            );

            result.setDatabaseType(query.getDatabaseType().name());
            result.setMetricType("MIXED"); // Display field'larda farklı tipler olabilir
            result.setMetricCategory(query.getMetricCategory());

            return result;

        } catch (Exception e) {
            log.error("Metrik sorgusu test edilirken hata oluştu: {}", e.getMessage());

            return MetricTestResultDto.failure(
                    query.getId(),
                    query.getQueryName(),
                    query.getSqlQuery(),
                    System.currentTimeMillis() - startTime,
                    e.getMessage(),
                    "EXECUTION_ERROR"
            );
        }
    }

    /**
     * Sadece sorgu doğrulaması yapar (çalıştırmaz)
     */
    private MetricTestResultDto validateQueryOnly(MetricQuery query) {
        boolean isValid = query.getSqlQuery() != null &&
                         !query.getSqlQuery().trim().isEmpty() &&
                         query.getQueryName() != null &&
                         !query.getQueryName().trim().isEmpty();

        String validationMessage = isValid ?
                "Query validation successful" :
                "Query validation failed - missing required fields";

        MetricTestResultDto result = MetricTestResultDto.validation(
                query.getId(),
                query.getQueryName(),
                isValid,
                validationMessage
        );

        result.setDatabaseType(query.getDatabaseType().name());
        result.setMetricType("MIXED"); // Display field'larda farklı tipler olabilir
        result.setMetricCategory(query.getMetricCategory());
        result.setSqlQuery(query.getSqlQuery());

        return result;
    }
}
