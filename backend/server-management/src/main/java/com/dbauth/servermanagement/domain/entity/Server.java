package com.dbauth.servermanagement.domain.entity;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Server Domain Entity - Pure Domain Object
 * Onion Architecture: Domain katmanında pure business entity
 * JPA annotation'ları infrastructure katmanında olacak
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Server {

    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private DatabaseType databaseType;
    private String username;
    private String password;
    private String description;

    // MSSQL Always On Availability Groups için replica tipi
    // Sadece SQL_SERVER için kullanılır, diğer veritabanları için null olabilir
    private ReplicaType replicaType;

    @Builder.Default
    private Boolean isActive = true;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Business Methods
    public void activate() {
        this.isActive = true;
    }

    public void deactivate() {
        this.isActive = false;
    }

    public void toggleStatus() {
        this.isActive = !this.isActive;
    }

    public boolean isConnectionValid() {
        return name != null && !name.trim().isEmpty() &&
               host != null && !host.trim().isEmpty() &&
               port != null && port > 0 &&
               databaseType != null;
    }

    public String getConnectionString() {
        // Dinamik port kullan, fallback olarak default port
        int actualPort = port != null ? port : databaseType.getDefaultPort();
        String actualDatabase = databaseType.getDefaultDatabase();

        return String.format("%s://%s:%d/%s",
            databaseType.getProtocol(),
            host,
            actualPort,
            actualDatabase);
    }

    public boolean isUsingDefaultPort() {
        return port != null && databaseType != null && port.equals(databaseType.getDefaultPort());
    }

    public boolean isUsingDefaultDatabase() {
        return true; // Her zaman default database kullanıyoruz
    }

    public int getActualPort() {
        return port != null ? port : (databaseType != null ? databaseType.getDefaultPort() : 0);
    }

    public String getActualDatabaseName() {
        return databaseType != null ? databaseType.getDefaultDatabase() : "";
    }

    public void updateTimestamps() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        updatedAt = now;
    }

    // MSSQL Always On Availability Groups için yardımcı metodlar

    /**
     * MSSQL Always On Availability Groups sunucusu mu kontrol eder
     */
    public boolean isMssqlAlwaysOn() {
        return databaseType == DatabaseType.SQL_SERVER && replicaType != null;
    }

    /**
     * Primary replica mı kontrol eder
     */
    public boolean isPrimaryReplica() {
        return replicaType != null && replicaType.isPrimary();
    }

    /**
     * Secondary replica mı kontrol eder
     */
    public boolean isSecondaryReplica() {
        return replicaType != null && replicaType.isSecondary();
    }

    /**
     * Read-only replica mı kontrol eder
     */
    public boolean isReadOnlyReplica() {
        return replicaType != null && replicaType.isReadOnly();
    }

    /**
     * Bu sunucuda yazma işlemi yapılabilir mi kontrol eder
     */
    public boolean canWriteToDatabase() {
        if (!isMssqlAlwaysOn()) {
            return true; // MSSQL Always On değilse her zaman yazabilir
        }
        return replicaType != null && replicaType.canWrite();
    }

    /**
     * Bu sunucuda okuma işlemi yapılabilir mi kontrol eder
     */
    public boolean canReadFromDatabase() {
        return true; // Tüm sunucular okuma yapabilir
    }

    /**
     * Replica type'ı ayarla (sadece MSSQL için)
     */
    public void setReplicaType(ReplicaType replicaType) {
        if (databaseType == DatabaseType.SQL_SERVER) {
            this.replicaType = replicaType;
        } else {
            this.replicaType = null; // MSSQL değilse replica type null olmalı
        }
    }
}
