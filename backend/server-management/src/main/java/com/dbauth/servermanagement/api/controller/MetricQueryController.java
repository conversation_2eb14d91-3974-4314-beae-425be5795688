package com.dbauth.servermanagement.api.controller;

import com.dbauth.servermanagement.api.dto.ComponentRecommendationDto;
import com.dbauth.servermanagement.application.dto.MetricDisplayFieldDto;
import com.dbauth.servermanagement.application.service.MetricQueryService;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/metric-queries")
@RequiredArgsConstructor
@Slf4j
public class MetricQueryController {

    private final MetricQueryService metricQueryService;

    @GetMapping
    public ResponseEntity<List<MetricQuery>> getAllMetricQueries() {
        log.info("Tüm metrik sorguları listeleniyor");
        List<MetricQuery> queries = metricQueryService.getAllMetricQueries();
        return ResponseEntity.ok(queries);
    }

    @GetMapping("/database-type/{databaseType}")
    public ResponseEntity<List<MetricQuery>> getMetricQueriesByDatabaseType(
            @PathVariable DatabaseType databaseType) {
        log.info("Veritabanı tipi {} için metrik sorguları listeleniyor", databaseType);
        List<MetricQuery> queries = metricQueryService.getMetricQueriesByDatabaseType(databaseType);
        return ResponseEntity.ok(queries);
    }

    @GetMapping("/database-type/{databaseType}/active")
    public ResponseEntity<List<MetricQuery>> getActiveMetricQueriesByDatabaseType(
            @PathVariable DatabaseType databaseType) {
        log.info("Veritabanı tipi {} için aktif metrik sorguları listeleniyor", databaseType);
        List<MetricQuery> queries = metricQueryService.getActiveMetricQueriesByDatabaseType(databaseType);
        return ResponseEntity.ok(queries);
    }

    @GetMapping("/category/{metricCategory}")
    public ResponseEntity<List<MetricQuery>> getMetricQueriesByCategory(@PathVariable String metricCategory) {
        log.info("Metrik kategorisi {} için sorgular listeleniyor", metricCategory);
        List<MetricQuery> queries = metricQueryService.getMetricQueriesByCategory(metricCategory);
        return ResponseEntity.ok(queries);
    }

    @GetMapping("/{id}")
    public ResponseEntity<MetricQuery> getMetricQueryById(@PathVariable UUID id) {
        log.info("ID {} ile metrik sorgusu getiriliyor", id);
        MetricQuery query = metricQueryService.getMetricQueryById(id);
        if (query == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(query);
    }

    @PostMapping
    public ResponseEntity<MetricQuery> createMetricQuery(@Valid @RequestBody MetricQuery metricQuery) {
        log.info("Yeni metrik sorgusu oluşturuluyor: {}", metricQuery.getQueryName());
        MetricQuery createdQuery = metricQueryService.createMetricQuery(metricQuery);
        return ResponseEntity.ok(createdQuery);
    }

    @PutMapping("/{id}")
    public ResponseEntity<MetricQuery> updateMetricQuery(
            @PathVariable UUID id,
            @Valid @RequestBody MetricQuery metricQuery) {
        log.info("Metrik sorgusu güncelleniyor: {}", id);
        try {
            MetricQuery updatedQuery = metricQueryService.updateMetricQuery(id, metricQuery);
            return ResponseEntity.ok(updatedQuery);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteMetricQuery(@PathVariable UUID id) {
        log.info("Metrik sorgusu siliniyor: {}", id);
        try {
            metricQueryService.deleteMetricQuery(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PatchMapping("/{id}/toggle-status")
    public ResponseEntity<MetricQuery> toggleMetricQueryStatus(@PathVariable UUID id) {
        log.info("Metrik sorgusu durumu değiştiriliyor: {}", id);
        try {
            MetricQuery updatedQuery = metricQueryService.toggleMetricQueryStatus(id);
            return ResponseEntity.ok(updatedQuery);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/database-type/{databaseType}/ordered")
    public ResponseEntity<List<MetricQuery>> getOrderedMetricQueriesByDatabaseType(
            @PathVariable DatabaseType databaseType) {
        log.info("Veritabanı tipi {} için sıralı metrik sorguları listeleniyor", databaseType);
        List<MetricQuery> queries = metricQueryService.getOrderedMetricQueriesByDatabaseType(databaseType);
        return ResponseEntity.ok(queries);
    }

    // ===== YENİ COMPONENT ENDPOINTS =====

    /**
     * Metrik tipi ve veri tipine göre bileşen önerilerini getirir
     */
    @GetMapping("/component-recommendations")
    public ResponseEntity<ComponentRecommendationDto> getComponentRecommendations(
            @RequestParam String dataType,
            @RequestParam MetricType metricType) {
        log.info("Veri tipi {} ve metrik tipi {} için bileşen önerileri getiriliyor", dataType, metricType);
        ComponentRecommendationDto recommendations = metricQueryService.getComponentRecommendations(dataType, metricType);
        return ResponseEntity.ok(recommendations);
    }

    /**
     * Tüm bileşen türlerini getirir
     */
    @GetMapping("/component-types")
    public ResponseEntity<List<ComponentType>> getAllComponentTypes() {
        log.info("Tüm bileşen türleri listeleniyor");
        List<ComponentType> componentTypes = metricQueryService.getAllComponentTypes();
        return ResponseEntity.ok(componentTypes);
    }

    /**
     * Veri tipine uyumlu bileşen türlerini getirir
     */
    @GetMapping("/component-types/compatible")
    public ResponseEntity<List<ComponentType>> getCompatibleComponentTypes(@RequestParam String dataType) {
        log.info("Veri tipi {} için uyumlu bileşen türleri getiriliyor", dataType);
        List<ComponentType> componentTypes = metricQueryService.getCompatibleComponentTypes(dataType);
        return ResponseEntity.ok(componentTypes);
    }

    /**
     * Metrik display field'ları getirir
     */
    @GetMapping("/{metricQueryId}/display-fields")
    public ResponseEntity<List<MetricDisplayFieldDto>> getMetricDisplayFields(@PathVariable UUID metricQueryId) {
        log.info("Metrik sorgusu {} için display field'lar getiriliyor", metricQueryId);
        List<MetricDisplayFieldDto> displayFields = metricQueryService.getMetricDisplayFields(metricQueryId);
        return ResponseEntity.ok(displayFields);
    }

    /**
     * Display field'ın bileşen konfigürasyonunu günceller
     */
    @PutMapping("/{metricQueryId}/display-fields/{columnName}/component")
    public ResponseEntity<MetricDisplayFieldDto> updateDisplayFieldComponent(
            @PathVariable UUID metricQueryId,
            @PathVariable String columnName,
            @RequestBody UpdateComponentRequest request) {
        log.info("Display field bileşeni güncelleniyor: {} - {} -> {}",
                metricQueryId, columnName, request.getComponentType());

        try {
            MetricDisplayFieldDto updatedField = metricQueryService.updateDisplayFieldComponent(
                    metricQueryId, columnName, request.getComponentType(), request.getComponentConfig());
            return ResponseEntity.ok(updatedField);
        } catch (IllegalArgumentException e) {
            log.warn("Bileşen güncelleme hatası: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (RuntimeException e) {
            log.error("Display field güncelleme hatası: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update Component Request DTO
     */
    public static class UpdateComponentRequest {
        private ComponentType componentType;
        private Map<String, Object> componentConfig;

        public ComponentType getComponentType() {
            return componentType;
        }

        public void setComponentType(ComponentType componentType) {
            this.componentType = componentType;
        }

        public Map<String, Object> getComponentConfig() {
            return componentConfig;
        }

        public void setComponentConfig(Map<String, Object> componentConfig) {
            this.componentConfig = componentConfig;
        }
    }

    // MetricType endpoint'leri kaldırıldı - artık display field seviyesinde
}
