package com.dbauth.servermanagement.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServerDetailDto {
    
    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private String databaseType;
    private String databaseName;
    private String username;
    private String description;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Metrik verileri
    private List<MetricDisplayData> metrics;
    private Map<String, Object> stats;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MetricDisplayData {
        private String metricName;
        private String metricType;
        private String dataType;
        private String defaultTimeRange;
        private Boolean supportsTimeRange;
        private Boolean success;
        private Long executionTimeMs;
        private Long recordedAt;
        private List<DisplayField> displayFields;
        private Map<String, Object> groupConfig;
        private Map<String, Object> tableConfig;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DisplayField {
            private String fieldName;
            private String displayName;
            private Object value;
            private String unit;
            private String type;
            private String category;
            private String description;
            private String formattedValue;
            private String visualType;
            private String dataType;
            private Map<String, Object> thresholds;
            private Map<String, Object> chartConfig;
        }
    }
}
