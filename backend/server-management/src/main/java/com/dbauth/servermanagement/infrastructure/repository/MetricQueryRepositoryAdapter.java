package com.dbauth.servermanagement.infrastructure.repository;

import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class MetricQueryRepositoryAdapter implements MetricQueryRepository {

    private final JpaMetricQueryRepository jpaMetricQueryRepository;

    @Override
    public List<MetricQuery> findAll() {
        return jpaMetricQueryRepository.findAll();
    }

    @Override
    public List<MetricQuery> findByDatabaseType(DatabaseType databaseType) {
        return jpaMetricQueryRepository.findByDatabaseType(databaseType);
    }

    @Override
    public List<MetricQuery> findByDatabaseTypeAndIsActive(DatabaseType databaseType, Boolean isActive) {
        return jpaMetricQueryRepository.findByDatabaseTypeAndIsActive(databaseType, isActive);
    }

    @Override
    public List<MetricQuery> findByDatabaseTypeAndIsActiveOrderByExecutionOrder(DatabaseType databaseType, Boolean isActive) {
        return jpaMetricQueryRepository.findByDatabaseTypeAndIsActiveOrderByExecutionOrder(databaseType, isActive);
    }

    @Override
    public List<MetricQuery> findByMetricCategory(String metricCategory) {
        return jpaMetricQueryRepository.findByMetricCategory(metricCategory);
    }

    // MetricType metodları kaldırıldı - artık display field seviyesinde

    @Override
    public Optional<MetricQuery> findById(UUID id) {
        return jpaMetricQueryRepository.findById(id);
    }

    @Override
    public MetricQuery save(MetricQuery metricQuery) {
        return jpaMetricQueryRepository.save(metricQuery);
    }

    @Override
    public void deleteById(UUID id) {
        jpaMetricQueryRepository.deleteById(id);
    }

    @Override
    public List<MetricQuery> findByDatabaseTypeOrderByExecutionOrder(DatabaseType databaseType) {
        return jpaMetricQueryRepository.findByDatabaseTypeOrderByExecutionOrder(databaseType);
    }

    @Override
    public List<MetricQuery> findByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType) {
        return jpaMetricQueryRepository.findByDatabaseTypeAndIsActiveTrue(databaseType);
    }

    @Override
    public List<MetricQuery> findByMetricCategoryAndIsActiveTrue(String metricCategory) {
        return jpaMetricQueryRepository.findByMetricCategoryAndIsActiveTrue(metricCategory);
    }

    // MetricType metodları kaldırıldı

    @Override
    public List<MetricQuery> findByIsActiveTrue() {
        return jpaMetricQueryRepository.findByIsActiveTrue();
    }

    // MetricType metodları kaldırıldı

    @Override
    public long count() {
        return jpaMetricQueryRepository.count();
    }

    @Override
    public long countByIsActiveTrue() {
        return jpaMetricQueryRepository.countByIsActiveTrue();
    }

    @Override
    public long countByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType) {
        return jpaMetricQueryRepository.countByDatabaseTypeAndIsActiveTrue(databaseType);
    }

    // MetricType metodları kaldırıldı
}
