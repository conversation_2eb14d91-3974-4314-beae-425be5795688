package com.dbauth.servermanagement.domain.entity;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "metric_queries")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricQuery {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @NotBlank(message = "Sorgu adı boş olamaz")
    @Size(max = 100, message = "Sorgu adı en fazla 100 karakter olabilir")
    @Column(name = "query_name", nullable = false, length = 100)
    private String queryName;

    @Size(max = 500, message = "Sorgu açıklaması en fazla 500 karakter olabilir")
    @Column(name = "query_description", length = 500)
    private String queryDescription;

    @NotNull(message = "Veritabanı tipi boş olamaz")
    @Enumerated(EnumType.STRING)
    @Column(name = "database_type", nullable = false)
    private DatabaseType databaseType;

    @NotBlank(message = "SQL sorgusu boş olamaz")
    @Column(name = "sql_query", nullable = false, columnDefinition = "TEXT")
    private String sqlQuery;

    @Size(max = 50, message = "Metrik kategorisi en fazla 50 karakter olabilir")
    @Column(name = "metric_category", length = 50)
    private String metricCategory; // CPU, RAM, DISK, CONNECTION, PERFORMANCE, etc.

    // Display Fields - Her kolon için ayrı metrik tipi
    @OneToMany(mappedBy = "metricQueryId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MetricDisplayField> displayFields;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "execution_order")
    @Builder.Default
    private Integer executionOrder = 0;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "display_mapping", columnDefinition = "jsonb")
    private Map<String, Object> displayMapping;

    @Column(name = "data_type", length = 50)
    @Builder.Default
    private String dataType = "snapshot"; // snapshot, aggregated, historical, comparative

    @Column(name = "default_time_range", length = 10)
    @Builder.Default
    private String defaultTimeRange = "1h"; // 5m, 15m, 1h, 6h, 24h, 7d

    @Column(name = "supports_time_range", nullable = false)
    @Builder.Default
    private Boolean supportsTimeRange = false;

    @Column(name = "numeric_field_name", length = 100)
    private String numericFieldName; // AVERAGE türü için hangi alanın ortalaması alınacak

    @Column(name = "time_aggregation_minutes")
    @Builder.Default
    private Integer timeAggregationMinutes = 60; // Varsayılan 1 saat

    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Business Methods

    /**
     * Display field'ların herhangi birinin zaman aralığı gerektirip gerektirmediğini kontrol eder
     */
    public boolean hasTimeRangeFields() {
        return displayFields != null && displayFields.stream()
                .anyMatch(MetricDisplayField::requiresTimeRange);
    }

    /**
     * Display field'ların herhangi birinin sayısal veri gerektirip gerektirmediğini kontrol eder
     */
    public boolean hasNumericFields() {
        return displayFields != null && displayFields.stream()
                .anyMatch(MetricDisplayField::requiresNumericData);
    }

    /**
     * Metrik sorgusu için validation
     */
    public boolean isValidConfiguration() {
        return displayFields != null && !displayFields.isEmpty();
    }

    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        if (this.updatedAt == null) {
            this.updatedAt = LocalDateTime.now();
        }

        // DisplayFields'ların metricQueryId'sini set et
        if (this.displayFields != null) {
            this.displayFields.forEach(field -> {
                field.setMetricQueryId(this.id);
            });
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
