package com.dbauth.servermanagement.api.controller;

import com.dbauth.servermanagement.api.dto.CreateServerDto;
import com.dbauth.servermanagement.api.dto.ServerDetailDto;
import com.dbauth.servermanagement.api.dto.ServerDto;
import com.dbauth.servermanagement.api.dto.UpdateServerDto;
import com.dbauth.servermanagement.api.mapper.ServerApiMapper;
import com.dbauth.servermanagement.application.service.ServerDetailService;
import com.dbauth.servermanagement.application.service.ServerService;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.infrastructure.service.DatabaseConnectionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Server REST Controller - API Layer
 * Onion Architecture: API katmanı, sadece API DTO'ları kullanır
 * Clean Code: Lombok ile temiz kod, mapper ile separation
 * Refactored: Application DTO mapping eklendi
 */
@RestController
@RequestMapping("/api/servers")
@RequiredArgsConstructor
@Slf4j
public class ServerController {

    private final ServerService serverService;
    private final ServerDetailService serverDetailService;
    private final ServerApiMapper apiMapper;

    @GetMapping
    public ResponseEntity<List<ServerDto>> getAllServers() {
        log.info("Tüm sunucular istendi");
        List<ServerDto> servers = apiMapper.toApiDtoList(serverService.getAllServers());
        return ResponseEntity.ok(servers);
    }

    @GetMapping("/active")
    public ResponseEntity<List<ServerDto>> getActiveServers() {
        log.info("Aktif sunucular istendi");
        List<ServerDto> servers = apiMapper.toApiDtoList(serverService.getActiveServers());
        return ResponseEntity.ok(servers);
    }

    @GetMapping("/database-type/{databaseType}")
    public ResponseEntity<List<ServerDto>> getServersByDatabaseType(@PathVariable DatabaseType databaseType) {
        log.info("Veritabanı tipi {} olan sunucular istendi", databaseType);
        List<ServerDto> servers = apiMapper.toApiDtoList(serverService.getServersByDatabaseType(databaseType));
        return ResponseEntity.ok(servers);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ServerDto> getServerById(@PathVariable UUID id) {
        log.info("Sunucu ID: {} istendi", id);
        ServerDto server = apiMapper.toApiDto(serverService.getServerById(id));
        return ResponseEntity.ok(server);
    }

    @GetMapping("/{id}/details")
    public ResponseEntity<ServerDetailDto> getServerDetailById(
            @PathVariable UUID id,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime) {
        log.info("Sunucu detayları ID: {} istendi (startTime: {}, endTime: {})", id, startTime, endTime);
        ServerDetailDto serverDetail = serverDetailService.getServerDetailById(id, startTime, endTime);
        return ResponseEntity.ok(serverDetail);
    }

    @GetMapping("/name/{name}")
    public ResponseEntity<ServerDto> getServerByName(@PathVariable String name) {
        log.info("Sunucu adı: {} istendi", name);
        ServerDto server = apiMapper.toApiDto(serverService.getServerByName(name));
        return ResponseEntity.ok(server);
    }

    @PostMapping
    public ResponseEntity<ServerDto> createServer(@Valid @RequestBody CreateServerDto createServerDto) {
        log.info("Yeni sunucu oluşturma isteği: {}", createServerDto.getName());
        ServerDto createdServer = apiMapper.toApiDto(
            serverService.createServer(apiMapper.toApplicationDto(createServerDto))
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(createdServer);
    }

    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@Valid @RequestBody CreateServerDto serverDto) {
        log.info("Sunucu bağlantı testi isteği alındı: {}:{}", serverDto.getHost(), serverDto.getPort());
        DatabaseConnectionService.ConnectionTestResult result =
            serverService.testConnection(apiMapper.toApplicationDto(serverDto));

        // Convert to legacy format for API compatibility
        Map<String, Object> response = new HashMap<>();
        response.put("success", result.isSuccess());
        response.put("message", result.getMessage());
        response.put("connectionTime", result.getResponseTimeMs() + " ms");

        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ServerDto> updateServer(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateServerDto updateServerDto) {
        log.info("Sunucu güncelleme isteği: {} (ID: {})", updateServerDto.getName(), id);

        // Set ID in update DTO
        var applicationUpdateDto = apiMapper.toApplicationDto(updateServerDto);
        applicationUpdateDto.setId(id);

        ServerDto updatedServer = apiMapper.toApiDto(
            serverService.updateServer(id, applicationUpdateDto)
        );
        return ResponseEntity.ok(updatedServer);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteServer(@PathVariable UUID id) {
        log.info("Sunucu silme isteği: {}", id);
        serverService.deleteServer(id);
        return ResponseEntity.noContent().build();
    }

    @PatchMapping("/{id}/toggle-status")
    public ResponseEntity<Void> toggleServerStatus(@PathVariable UUID id) {
        log.info("Sunucu durum değiştirme isteği: {}", id);
        serverService.toggleServerStatus(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/database-types")
    public ResponseEntity<DatabaseType[]> getDatabaseTypes() {
        log.info("Veritabanı tipleri istendi");
        return ResponseEntity.ok(DatabaseType.values());
    }
}
