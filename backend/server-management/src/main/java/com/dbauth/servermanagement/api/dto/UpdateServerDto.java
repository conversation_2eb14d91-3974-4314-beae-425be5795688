package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateServerDto {
    
    @NotBlank(message = "Sunucu adı boş olamaz")
    @Size(max = 100, message = "Sunucu adı en fazla 100 karakter olabilir")
    private String name;
    
    @NotBlank(message = "Host adresi boş olamaz")
    @Size(max = 255, message = "Host adresi en fazla 255 karakter olabilir")
    private String host;
    
    @NotNull(message = "Port numarası boş olamaz")
    @Min(value = 1, message = "Port numarası 1'den küçük olamaz")
    @Max(value = 65535, message = "Port numarası 65535'den büyük olamaz")
    private Integer port;
    
    @NotNull(message = "Veritabanı tipi boş olamaz")
    private DatabaseType databaseType;

    @Size(max = 100, message = "Kullanıcı adı en fazla 100 karakter olabilir")
    private String username;
    
    @Size(max = 255, message = "Şifre en fazla 255 karakter olabilir")
    private String password;
    
    @Size(max = 1000, message = "Açıklama en fazla 1000 karakter olabilir")
    private String description;

    // MSSQL Always On Availability Groups için replica tipi (opsiyonel)
    // Sadece SQL_SERVER için kullanılır
    private ReplicaType replicaType;

    private Boolean isActive;
}
