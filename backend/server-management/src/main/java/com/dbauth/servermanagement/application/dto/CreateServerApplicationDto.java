package com.dbauth.servermanagement.application.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Create Server Application DTO - Application Layer
 * Onion Architecture: Application katmanında server creation için DTO
 * Clean Code: Validation logic dahil
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class CreateServerApplicationDto {

    private String name;
    private String host;
    private Integer port;
    private DatabaseType databaseType;
    private String username;
    private String password;
    private String description;

    // MSSQL Always On Availability Groups için replica tipi (opsiyonel)
    private ReplicaType replicaType;

    @Builder.Default
    private Boolean isActive = true;

    // Validation methods
    public boolean isValid() {
        return isNameValid() && isHostValid() && isPortValid() && isDatabaseTypeValid();
    }

    public boolean isNameValid() {
        return name != null && !name.trim().isEmpty() && name.length() <= 100;
    }

    public boolean isHostValid() {
        return host != null && !host.trim().isEmpty() && host.length() <= 255;
    }

    public boolean isPortValid() {
        return port != null && port > 0 && port <= 65535;
    }

    public boolean isDatabaseTypeValid() {
        return databaseType != null;
    }

    // Business logic - Default'ları sadece gerektiğinde uygula
    public void applyDefaults() {
        // Port dinamik olmalı - sadece null ise default kullan
        if (port == null && databaseType != null) {
            port = databaseType.getDefaultPort();
            log.debug("Applied default port {} for database type {}", port, databaseType);
        }

        // Active status default
        if (isActive == null) {
            isActive = true;
        }
    }

    // Helper method to check if using default port
    public boolean isUsingDefaultPort() {
        return databaseType != null && port != null && port.equals(databaseType.getDefaultPort());
    }

    // Helper method to check if using default database
    public boolean isUsingDefaultDatabase() {
        return true; // Her zaman default database kullanıyoruz
    }
}
