package com.dbauth.servermanagement.infrastructure.repository;

import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import com.dbauth.servermanagement.infrastructure.entity.ServerEntity;
import com.dbauth.servermanagement.infrastructure.mapper.ServerEntityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Server Repository Implementation - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında entity mapping
 * Clean Code: ServerEntityMapper ile temiz mapping
 * Fixed: Domain-Infrastructure entity mapping eklendi
 */
@Component
@RequiredArgsConstructor
public class ServerRepositoryImpl implements ServerRepository {

    private final JpaServerRepository jpaServerRepository;
    private final ServerEntityMapper entityMapper;

    @Override
    public Server save(Server server) {
        ServerEntity entity = entityMapper.toEntity(server);
        ServerEntity savedEntity = jpaServerRepository.save(entity);
        return entityMapper.toDomain(savedEntity);
    }

    @Override
    public Optional<Server> findById(UUID id) {
        return jpaServerRepository.findById(id)
                .map(entityMapper::toDomain);
    }

    @Override
    public Optional<Server> findByName(String name) {
        return jpaServerRepository.findByName(name)
                .map(entityMapper::toDomain);
    }

    @Override
    public List<Server> findAll() {
        return jpaServerRepository.findAll().stream()
                .map(entityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Server> findByIsActive(Boolean isActive) {
        return jpaServerRepository.findByIsActive(isActive).stream()
                .map(entityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Server> findByDatabaseType(DatabaseType databaseType) {
        return jpaServerRepository.findByDatabaseType(databaseType).stream()
                .map(entityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Server> findByIsActiveAndDatabaseType(Boolean isActive, DatabaseType databaseType) {
        return jpaServerRepository.findByIsActiveAndDatabaseType(isActive, databaseType).stream()
                .map(entityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(UUID id) {
        jpaServerRepository.deleteById(id);
    }

    @Override
    public boolean existsByName(String name) {
        return jpaServerRepository.existsByName(name);
    }

    @Override
    public boolean existsByNameAndIdNot(String name, UUID id) {
        return jpaServerRepository.existsByNameAndIdNot(name, id);
    }
}
