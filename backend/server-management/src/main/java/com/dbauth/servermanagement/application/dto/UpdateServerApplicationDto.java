package com.dbauth.servermanagement.application.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Update Server Application DTO - Application Layer
 * Onion Architecture: Application katmanında server update için DTO
 * Clean Code: Partial update support
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateServerApplicationDto {

    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private DatabaseType databaseType;
    private String username;
    private String password;
    private String description;

    // MSSQL Always On Availability Groups için replica tipi (opsiyonel)
    private ReplicaType replicaType;

    private Boolean isActive;

    // Validation methods
    public boolean isIdValid() {
        return id != null;
    }

    public boolean isNameValid() {
        return name == null || (!name.trim().isEmpty() && name.length() <= 100);
    }

    public boolean isHostValid() {
        return host == null || (!host.trim().isEmpty() && host.length() <= 255);
    }

    public boolean isPortValid() {
        return port == null || (port > 0 && port <= 65535);
    }

    public boolean isValid() {
        return isIdValid() && isNameValid() && isHostValid() && isPortValid();
    }

    // Helper methods for partial updates
    public boolean hasName() {
        return name != null;
    }

    public boolean hasHost() {
        return host != null;
    }

    public boolean hasPort() {
        return port != null;
    }

    public boolean hasDatabaseType() {
        return databaseType != null;
    }

    public boolean hasUsername() {
        return username != null;
    }

    public boolean hasPassword() {
        return password != null;
    }

    public boolean hasDescription() {
        return description != null;
    }

    public boolean hasIsActive() {
        return isActive != null;
    }

    public boolean hasReplicaType() {
        return replicaType != null;
    }
}
