package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.MetricType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricDisplayFieldDto {
    
    private UUID id;
    private UUID metricQueryId;
    private String columnName;
    private String displayName;
    private String dataType; // string, int
    private MetricType metricType; // LATEST, AVERAGE, RANGE - her kolon için ayrı
    private String description;
    private Integer displayOrder;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
