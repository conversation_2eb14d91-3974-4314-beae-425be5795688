package com.dbauth.servermanagement.api.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricResultDto {
    
    private UUID id;
    private UUID serverId;
    private UUID metricQueryId;
    private String metricName;
    private String sqlQuery;
    private Map<String, Object> resultData;
    private Long rowCount;
    private Long executionTimeMs;
    private Boolean success;
    private String errorMessage;
    private String errorCode;
    private Long recordedAt; // Unix timestamp
    private LocalDateTime recordedAtFormatted; // Human readable format
    private String metricType; // Metrik türü bilgisi (artık String)
    private String metricCategory; // <PERSON><PERSON> kate<PERSON> (CPU, MEMORY, etc.)
    
    // Helper methods
    public String getExecutionTimeFormatted() {
        if (executionTimeMs == null) return "N/A";
        if (executionTimeMs < 1000) return executionTimeMs + "ms";
        return String.format("%.2fs", executionTimeMs / 1000.0);
    }
    
    public String getStatusIcon() {
        return success != null && success ? "✅" : "❌";
    }
    
    public String getStatusText() {
        return success != null && success ? "Başarılı" : "Başarısız";
    }
}
