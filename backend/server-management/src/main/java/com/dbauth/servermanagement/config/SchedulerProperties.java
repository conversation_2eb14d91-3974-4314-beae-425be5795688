package com.dbauth.servermanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Scheduler Configuration Properties
 * Scheduled task'lar için dinamik konfigürasyon değerleri
 */
@Data
@ConfigurationProperties(prefix = "scheduler")
public class SchedulerProperties {

    /**
     * Expired permission cleanup cron expression
     * Varsayılan: "0 0 3 * * ?" (Her gün saat 03:00)
     */
    private String expiredPermissionCleanupCron = "0 0 3 * * ?";

    /**
     * Gateway URL
     * Varsayılan: "http://localhost:8080"
     */
    private String gatewayUrl = "http://localhost:8080";

    /**
     * Scheduled task'lar aktif/pasif durumu
     * Varsayılan: true
     */
    private boolean enabled = true;

    /**
     * HTTP request timeout (milisaniye)
     * Varsayılan: 30000 (30 saniye)
     */
    private int httpTimeoutMs = 30000;

    /**
     * HTTP connection timeout (milisaniye)
     * Varsayılan: 10000 (10 saniye)
     */
    private int httpConnectionTimeoutMs = 10000;

    /**
     * HTTP read timeout (milisaniye)
     * Varsayılan: 30000 (30 saniye)
     */
    private int httpReadTimeoutMs = 30000;
}
