package com.dbauth.servermanagement.domain.repository;

import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ServerRepository {
    
    Server save(Server server);
    
    Optional<Server> findById(UUID id);
    
    Optional<Server> findByName(String name);
    
    List<Server> findAll();
    
    List<Server> findByIsActive(Boolean isActive);
    
    List<Server> findByDatabaseType(DatabaseType databaseType);
    
    List<Server> findByIsActiveAndDatabaseType(Boolean isActive, DatabaseType databaseType);
    
    void deleteById(UUID id);
    
    boolean existsByName(String name);
    
    boolean existsByNameAndIdNot(String name, UUID id);
}
