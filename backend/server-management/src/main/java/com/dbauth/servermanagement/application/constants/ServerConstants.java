package com.dbauth.servermanagement.application.constants;

/**
 * Server Management Constants - Application Layer
 * Clean Code: Magic number'ları constant'lara çevrildi
 * Single Responsibility: Sadece constant tanımları
 */
public final class ServerConstants {

    // Time constants (milliseconds)
    public static final long ONE_HOUR_MS = 60 * 60 * 1000L;
    public static final long ONE_DAY_MS = 24 * ONE_HOUR_MS;
    public static final long ONE_WEEK_MS = 7 * ONE_DAY_MS;
    public static final long ONE_MONTH_MS = 30 * ONE_DAY_MS;

    // Connection constants
    public static final int DEFAULT_CONNECTION_TIMEOUT_SECONDS = 30;
    public static final int DEFAULT_QUERY_TIMEOUT_SECONDS = 30;
    public static final int MAX_CONNECTION_RETRIES = 3;

    // Validation constants
    public static final int MAX_SERVER_NAME_LENGTH = 100;
    public static final int MAX_HOST_LENGTH = 255;
    public static final int MAX_PORT_NUMBER = 65535;
    public static final int MIN_PORT_NUMBER = 1;

    // Metric constants
    public static final int DEFAULT_METRIC_BATCH_SIZE = 100;
    public static final int MAX_METRIC_HISTORY_DAYS = 90;

    // Format constants
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "HH:mm:ss";

    // Error messages
    public static final String ERROR_SERVER_NOT_FOUND = "Server not found";
    public static final String ERROR_SERVER_NAME_EXISTS = "Server name already exists";
    public static final String ERROR_INVALID_CONNECTION = "Invalid connection parameters";
    public static final String ERROR_CONNECTION_FAILED = "Connection test failed";

    // Success messages
    public static final String SUCCESS_SERVER_CREATED = "Server created successfully";
    public static final String SUCCESS_SERVER_UPDATED = "Server updated successfully";
    public static final String SUCCESS_SERVER_DELETED = "Server deleted successfully";
    public static final String SUCCESS_CONNECTION_TEST = "Connection test successful";

    private ServerConstants() {
        // Utility class - prevent instantiation
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
