package com.dbauth.servermanagement.infrastructure.repository;

import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface JpaMetricQueryRepository extends JpaRepository<MetricQuery, UUID> {
    
    List<MetricQuery> findByDatabaseType(DatabaseType databaseType);
    
    List<MetricQuery> findByDatabaseTypeAndIsActive(DatabaseType databaseType, Boolean isActive);

    List<MetricQuery> findByDatabaseTypeAndIsActiveOrderByExecutionOrder(DatabaseType databaseType, Boolean isActive);

    List<MetricQuery> findByMetricCategory(String metricCategory);

    // MetricType metodları kaldırıldı - artık display field seviyesinde

    List<MetricQuery> findByDatabaseTypeOrderByExecutionOrder(DatabaseType databaseType);

    // Eksik metodlar
    List<MetricQuery> findByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType);

    List<MetricQuery> findByMetricCategoryAndIsActiveTrue(String metricCategory);

    List<MetricQuery> findByIsActiveTrue();

    long countByIsActiveTrue();

    long countByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType);
}
