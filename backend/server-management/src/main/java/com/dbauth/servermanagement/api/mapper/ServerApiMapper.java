package com.dbauth.servermanagement.api.mapper;


import com.dbauth.servermanagement.api.dto.CreateServerDto;
import com.dbauth.servermanagement.api.dto.ServerDto;
import com.dbauth.servermanagement.api.dto.UpdateServerDto;
import com.dbauth.servermanagement.application.dto.CreateServerApplicationDto;
import com.dbauth.servermanagement.application.dto.ServerApplicationDto;
import com.dbauth.servermanagement.application.dto.UpdateServerApplicationDto;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Server API Mapper - API Layer
 * Onion Architecture: API ve Application DTO'ları arasında mapping
 * Clean Code: Lombok ile temiz kod, API-Application separation
 */
@Component
@NoArgsConstructor
public class ServerApiMapper {

    /**
     * Application DTO'yu API DTO'ya dönüştürür
     */
    public ServerDto toApiDto(ServerApplicationDto applicationDto) {
        if (applicationDto == null) {
            return null;
        }

        return ServerDto.builder()
                .id(applicationDto.getId())
                .name(applicationDto.getName())
                .host(applicationDto.getHost())
                .port(applicationDto.getPort())
                .databaseType(applicationDto.getDatabaseType())
                .username(applicationDto.getUsername())
                .password(applicationDto.getPassword()) // Include encrypted password
                .description(applicationDto.getDescription())
                .replicaType(applicationDto.getReplicaType())
                .isActive(applicationDto.getIsActive())
                .createdAt(applicationDto.getCreatedAt())
                .updatedAt(applicationDto.getUpdatedAt())
                .build();
    }

    /**
     * Application DTO listesini API DTO listesine dönüştürür
     */
    public List<ServerDto> toApiDtoList(List<ServerApplicationDto> applicationDtos) {
        if (applicationDtos == null) {
            return null;
        }

        return applicationDtos.stream()
                .map(this::toApiDto)
                .collect(Collectors.toList());
    }

    /**
     * API Create DTO'yu Application Create DTO'ya dönüştürür
     */
    public CreateServerApplicationDto toApplicationDto(CreateServerDto apiDto) {
        if (apiDto == null) {
            return null;
        }

        return CreateServerApplicationDto.builder()
                .name(apiDto.getName())
                .host(apiDto.getHost())
                .port(apiDto.getPort())
                .databaseType(apiDto.getDatabaseType())
                .username(apiDto.getUsername())
                .password(apiDto.getPassword())
                .description(apiDto.getDescription())
                .replicaType(apiDto.getReplicaType())
                .isActive(apiDto.getIsActive())
                .build();
    }

    /**
     * API Update DTO'yu Application Update DTO'ya dönüştürür
     */
    public UpdateServerApplicationDto toApplicationDto(UpdateServerDto apiDto) {
        if (apiDto == null) {
            return null;
        }

        return UpdateServerApplicationDto.builder()
                .name(apiDto.getName())
                .host(apiDto.getHost())
                .port(apiDto.getPort())
                .databaseType(apiDto.getDatabaseType())
                .username(apiDto.getUsername())
                .password(apiDto.getPassword())
                .description(apiDto.getDescription())
                .replicaType(apiDto.getReplicaType())
                .isActive(apiDto.getIsActive())
                .build();
    }
}
