package com.dbauth.servermanagement.application.dto;

import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.ReplicaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Server Application DTO - Application Layer
 * Onion Architecture: Application katmanında internal DTO
 * API katmanından bağımsız, sadece business logic için
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServerApplicationDto {

    private UUID id;
    private String name;
    private String host;
    private Integer port;
    private DatabaseType databaseType;
    private String username;
    private String password;
    private String description;

    // MSSQL Always On Availability Groups için replica tipi
    private ReplicaType replicaType;

    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Business helper methods
    public String getDisplayName() {
        return String.format("%s (%s:%d)", name, host, port);
    }

    public String getConnectionInfo() {
        return String.format("%s - %s", databaseType.getDisplayName(), getDisplayName());
    }

    public boolean isConnectionComplete() {
        return name != null && !name.trim().isEmpty() &&
               host != null && !host.trim().isEmpty() &&
               port != null && port > 0 &&
               databaseType != null;
    }
}
