package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.api.dto.MetricResultDto;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricResult;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import com.dbauth.servermanagement.domain.repository.MetricResultRepository;
import com.dbauth.servermanagement.domain.repository.ServerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class MetricResultService {

    private final MetricResultRepository metricResultRepository;
    private final MetricQueryRepository metricQueryRepository;
    private final ServerRepository serverRepository;

    public List<MetricResultDto> getMetricsByServerId(UUID serverId, Pageable pageable) {
        List<MetricResult> metrics = metricResultRepository.findByServerIdOrderByRecordedAtDesc(serverId, pageable);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getLatestMetricsByServerId(UUID serverId) {
        List<MetricResult> metrics = metricResultRepository.findLatestSuccessfulMetricsByServerId(serverId);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getSuccessfulMetricsByServerId(UUID serverId, Pageable pageable) {
        List<MetricResult> metrics = metricResultRepository.findSuccessfulByServerIdOrderByRecordedAtDesc(serverId, pageable);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getFailedMetricsByServerId(UUID serverId, Pageable pageable) {
        List<MetricResult> metrics = metricResultRepository.findFailedByServerIdOrderByRecordedAtDesc(serverId, pageable);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getMetricsByServerIdAndMetricName(UUID serverId, String metricName, Pageable pageable) {
        List<MetricResult> metrics = metricResultRepository.findByServerIdAndMetricNameOrderByRecordedAtDesc(serverId, metricName, pageable);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getMetricsByServerIdAndTimeRange(UUID serverId, Long startTime, Long endTime) {
        List<MetricResult> metrics = metricResultRepository.findByServerIdAndRecordedAtBetween(serverId, startTime, endTime);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public List<MetricResultDto> getRecentMetrics(Long sinceTime, Pageable pageable) {
        List<MetricResult> metrics = metricResultRepository.findRecentMetrics(sinceTime, pageable);
        return metrics.stream().map(this::convertToDto).toList();
    }

    public Optional<MetricResultDto> getMetricById(UUID id) {
        return metricResultRepository.findById(id).map(this::convertToDto);
    }

    public Map<String, Object> getMetricStats(UUID serverId) {
        long last24Hours = System.currentTimeMillis() - (24 * 60 * 60 * 1000L);
        long lastHour = System.currentTimeMillis() - (60 * 60 * 1000L);

        Map<String, Object> stats = new HashMap<>();
        
        // Son 24 saat istatistikleri
        Long successful24h = metricResultRepository.getSuccessfulMetricCount(serverId, last24Hours);
        Long failed24h = metricResultRepository.getFailedMetricCount(serverId, last24Hours);
        Double avgExecution24h = metricResultRepository.getAverageExecutionTime(serverId, last24Hours);
        
        // Son 1 saat istatistikleri
        Long successful1h = metricResultRepository.getSuccessfulMetricCount(serverId, lastHour);
        Long failed1h = metricResultRepository.getFailedMetricCount(serverId, lastHour);
        Double avgExecution1h = metricResultRepository.getAverageExecutionTime(serverId, lastHour);

        stats.put("last24Hours", Map.of(
            "successful", successful24h != null ? successful24h : 0,
            "failed", failed24h != null ? failed24h : 0,
            "total", (successful24h != null ? successful24h : 0) + (failed24h != null ? failed24h : 0),
            "successRate", calculateSuccessRate(successful24h, failed24h),
            "avgExecutionTimeMs", avgExecution24h != null ? avgExecution24h.longValue() : 0
        ));

        stats.put("lastHour", Map.of(
            "successful", successful1h != null ? successful1h : 0,
            "failed", failed1h != null ? failed1h : 0,
            "total", (successful1h != null ? successful1h : 0) + (failed1h != null ? failed1h : 0),
            "successRate", calculateSuccessRate(successful1h, failed1h),
            "avgExecutionTimeMs", avgExecution1h != null ? avgExecution1h.longValue() : 0
        ));

        // Test için gerekli alanları ekle
        long totalMetrics = (successful24h != null ? successful24h : 0) + (failed24h != null ? failed24h : 0);
        stats.put("totalMetrics", totalMetrics);
        stats.put("successfulMetrics", successful24h != null ? successful24h : 0);
        stats.put("failedMetrics", failed24h != null ? failed24h : 0);

        return stats;
    }

    private double calculateSuccessRate(Long successful, Long failed) {
        long successCount = successful != null ? successful : 0;
        long failCount = failed != null ? failed : 0;
        long total = successCount + failCount;
        
        if (total == 0) return 0.0;
        return (double) successCount / total * 100.0;
    }

    @Transactional
    public int cleanupOldMetrics(Long cutoffTime) {
        return metricResultRepository.deleteOldMetrics(cutoffTime);
    }

    /**
     * Metrik türüne göre sonuçları getirir
     * LATEST: En son değer
     * AVERAGE: Zaman aralığındaki ortalama
     * RANGE: Zaman aralığındaki tüm değerler
     */
    public List<MetricResultDto> getMetricsByTypeAndTimeRange(UUID serverId, MetricType metricType,
                                                             Long startTime, Long endTime) {
        log.debug("Sunucu {} için {} türünde metrikler getiriliyor (zaman aralığı: {} - {})",
                 serverId, metricType, startTime, endTime);

        // Metrik türüne göre farklı işlemler
        return switch (metricType) {
            case LATEST -> getLatestMetricsByType(serverId, metricType);
            case AVERAGE -> getAverageMetricsByTimeRange(serverId, metricType, startTime, endTime);
            case RANGE -> getRangeMetricsByTimeRange(serverId, metricType, startTime, endTime);
        };
    }

    /**
     * LATEST türü metrikler - En son değerler
     */
    private List<MetricResultDto> getLatestMetricsByType(UUID serverId, MetricType metricType) {
        // Bu sunucu için metrik sorgularını bul (metrik tipi artık display field seviyesinde)
        List<MetricQuery> queries = metricQueryRepository.findByDatabaseTypeAndIsActiveTrue(
            serverRepository.findById(serverId).get().getDatabaseType());

        List<MetricResultDto> results = new ArrayList<>();
        for (MetricQuery query : queries) {
            // Her sorgu için en son başarılı sonucu getir
            List<MetricResult> latestResults = metricResultRepository
                .findByServerIdAndMetricQueryIdAndSuccessOrderByRecordedAtDesc(serverId, query.getId(), true);

            if (!latestResults.isEmpty()) {
                results.add(convertToDto(latestResults.get(0)));
            }
        }

        return results;
    }

    private MetricResultDto convertToDto(MetricResult metric) {
        LocalDateTime recordedAtFormatted = LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(metric.getRecordedAt()),
            ZoneId.systemDefault()
        );

        // MetricQuery bilgisini al
        MetricQuery metricQuery = null;
        if (metric.getMetricQueryId() != null) {
            metricQuery = metricQueryRepository.findById(metric.getMetricQueryId()).orElse(null);
        }

        return MetricResultDto.builder()
            .id(metric.getId())
            .serverId(metric.getServerId())
            .metricQueryId(metric.getMetricQueryId())
            .metricName(metric.getMetricName())
            .sqlQuery(metric.getSqlQuery())
            .resultData(metric.getResultData())
            .rowCount(metric.getRowCount())
            .executionTimeMs(metric.getExecutionTimeMs())
            .success(metric.getSuccess())
            .errorMessage(metric.getErrorMessage())
            .errorCode(metric.getErrorCode())
            .recordedAt(metric.getRecordedAt())
            .recordedAtFormatted(recordedAtFormatted)
            .metricType("MIXED") // Display field'larda farklı tipler olabilir
            .metricCategory(metricQuery != null ? metricQuery.getMetricCategory() : null)
            .build();
    }

    /**
     * AVERAGE türü metrikler - Zaman aralığındaki ortalama
     */
    private List<MetricResultDto> getAverageMetricsByTimeRange(UUID serverId, MetricType metricType,
                                                              Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("AVERAGE türü metrikler için zaman aralığı gereklidir");
        }

        // Bu sunucu için metrik sorgularını bul (metrik tipi artık display field seviyesinde)
        List<MetricQuery> queries = metricQueryRepository.findByDatabaseTypeAndIsActiveTrue(
            serverRepository.findById(serverId).get().getDatabaseType());

        List<MetricResultDto> results = new ArrayList<>();
        for (MetricQuery query : queries) {
            if (query.getNumericFieldName() == null) {
                log.warn("AVERAGE türü sorgu {} için numeric field tanımlanmamış", query.getQueryName());
                continue;
            }

            // Zaman aralığındaki tüm başarılı sonuçları getir
            List<MetricResult> timeRangeResults = metricResultRepository
                .findByServerIdAndMetricQueryIdAndRecordedAtBetweenAndSuccess(
                    serverId, query.getId(), startTime, endTime, true);

            if (!timeRangeResults.isEmpty()) {
                // Ortalama hesapla
                MetricResultDto averageResult = calculateAverageResult(query, timeRangeResults);
                if (averageResult != null) {
                    results.add(averageResult);
                }
            }
        }

        return results;
    }

    /**
     * RANGE türü metrikler - Zaman aralığındaki tüm değerler
     */
    private List<MetricResultDto> getRangeMetricsByTimeRange(UUID serverId, MetricType metricType,
                                                            Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("RANGE türü metrikler için zaman aralığı gereklidir");
        }

        // Bu sunucu için metrik sorgularını bul (metrik tipi artık display field seviyesinde)
        List<MetricQuery> queries = metricQueryRepository.findByDatabaseTypeAndIsActiveTrue(
            serverRepository.findById(serverId).get().getDatabaseType());

        List<MetricResultDto> results = new ArrayList<>();
        for (MetricQuery query : queries) {
            // Zaman aralığındaki tüm başarılı sonuçları getir
            List<MetricResult> timeRangeResults = metricResultRepository
                .findByServerIdAndMetricQueryIdAndRecordedAtBetweenAndSuccess(
                    serverId, query.getId(), startTime, endTime, true);

            // Tüm sonuçları listeye ekle
            results.addAll(timeRangeResults.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList()));
        }

        return results;
    }

    /**
     * Ortalama hesaplama
     */
    private MetricResultDto calculateAverageResult(MetricQuery query, List<MetricResult> results) {
        String numericField = query.getNumericFieldName();

        List<Double> numericValues = new ArrayList<>();
        for (MetricResult result : results) {
            if (result.getResultData() != null && result.getResultData().containsKey(numericField)) {
                Object value = result.getResultData().get(numericField);
                if (value instanceof Number) {
                    numericValues.add(((Number) value).doubleValue());
                }
            }
        }

        if (numericValues.isEmpty()) {
            log.warn("Ortalama hesaplaması için sayısal veri bulunamadı: {}", query.getQueryName());
            return null;
        }

        // Ortalama hesapla
        double average = numericValues.stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

        // Ortalama sonucu için yeni MetricResultDto oluştur
        Map<String, Object> averageData = new HashMap<>();
        averageData.put(numericField + "_average", average);
        averageData.put("sample_count", numericValues.size());
        averageData.put("time_range_start", results.get(0).getRecordedAt());
        averageData.put("time_range_end", results.get(results.size() - 1).getRecordedAt());

        return MetricResultDto.builder()
            .id(UUID.randomUUID()) // Geçici ID
            .serverId(results.get(0).getServerId())
            .metricQueryId(query.getId())
            .metricName(query.getQueryName() + "_AVERAGE")
            .resultData(averageData)
            .rowCount((long) numericValues.size())
            .executionTimeMs(0L)
            .success(true)
            .recordedAt(System.currentTimeMillis())
            .recordedAtFormatted(LocalDateTime.now())
            .build();
    }
}
