package com.dbauth.servermanagement.domain.enums;

import lombok.Getter;

/**
 * Replica Type Enum - Domain Layer
 * MSSQL Always On Availability Groups için replica tiplerini tanımlar
 * PRIMARY: Ana replica (read/write)
 * SECONDARY: İkincil replica (read-only, failover için hazır)
 * READ_ONLY: Sadece okuma amaçlı replica
 */
@Getter
public enum ReplicaType {
    PRIMARY("Primary", "Ana replica - okuma/yazma"),
    SECONDARY("Secondary", "İkincil replica - sadece okuma, failover hazır"),
    READ_ONLY("Read Only", "Sadece okuma amaçlı replica");

    private final String displayName;
    private final String description;

    ReplicaType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * Primary replica mı kontrol eder
     */
    public boolean isPrimary() {
        return this == PRIMARY;
    }

    /**
     * Secondary replica mı kontrol eder
     */
    public boolean isSecondary() {
        return this == SECONDARY;
    }

    /**
     * Read-only replica mı kontrol eder
     */
    public boolean isReadOnly() {
        return this == READ_ONLY;
    }

    /**
     * Yazma işlemi yapılabilir mi kontrol eder
     */
    public boolean canWrite() {
        return this == PRIMARY;
    }

    /**
     * Okuma işlemi yapılabilir mi kontrol eder
     */
    public boolean canRead() {
        return true; // Tüm replica tipleri okuma yapabilir
    }
}
