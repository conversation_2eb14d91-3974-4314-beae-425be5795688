package com.dbauth.servermanagement.domain.repository;

import com.dbauth.servermanagement.domain.entity.MetricResult;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface MetricResultRepository extends JpaRepository<MetricResult, UUID> {

    // Server'a göre en son metrikler
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId ORDER BY mr.recordedAt DESC")
    List<MetricResult> findByServerIdOrderByRecordedAtDesc(@Param("serverId") UUID serverId, Pageable pageable);

    // Server'a göre belirli zaman aralığındaki metrikler
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.recordedAt BETWEEN :startTime AND :endTime ORDER BY mr.recordedAt DESC")
    List<MetricResult> findByServerIdAndRecordedAtBetween(
        @Param("serverId") UUID serverId, 
        @Param("startTime") Long startTime, 
        @Param("endTime") Long endTime
    );

    // Belirli metrik query'sine göre sonuçlar
    @Query("SELECT mr FROM MetricResult mr WHERE mr.metricQueryId = :metricQueryId ORDER BY mr.recordedAt DESC")
    List<MetricResult> findByMetricQueryIdOrderByRecordedAtDesc(@Param("metricQueryId") UUID metricQueryId, Pageable pageable);

    // Server ve metrik query'sine göre en son sonuç
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.metricQueryId = :metricQueryId ORDER BY mr.recordedAt DESC LIMIT 1")
    Optional<MetricResult> findLatestByServerIdAndMetricQueryId(@Param("serverId") UUID serverId, @Param("metricQueryId") UUID metricQueryId);

    // Başarılı metrikler
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.success = true ORDER BY mr.recordedAt DESC")
    List<MetricResult> findSuccessfulByServerIdOrderByRecordedAtDesc(@Param("serverId") UUID serverId, Pageable pageable);

    // Başarısız metrikler
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.success = false ORDER BY mr.recordedAt DESC")
    List<MetricResult> findFailedByServerIdOrderByRecordedAtDesc(@Param("serverId") UUID serverId, Pageable pageable);

    // Belirli metrik adına göre sonuçlar
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.metricName = :metricName ORDER BY mr.recordedAt DESC")
    List<MetricResult> findByServerIdAndMetricNameOrderByRecordedAtDesc(
        @Param("serverId") UUID serverId, 
        @Param("metricName") String metricName, 
        Pageable pageable
    );

    // Tüm sunucular için en son metrikler
    @Query("SELECT mr FROM MetricResult mr WHERE mr.recordedAt > :sinceTime ORDER BY mr.recordedAt DESC")
    List<MetricResult> findRecentMetrics(@Param("sinceTime") Long sinceTime, Pageable pageable);

    // Server'ın en son başarılı metrik sonuçları (her metrik tipinden birer tane)
    @Query(value = """
        SELECT DISTINCT ON (mr.metric_name) mr.*
        FROM metric_results mr
        WHERE mr.server_id = :serverId AND mr.success = true
        ORDER BY mr.metric_name, mr.recorded_at DESC
        """, nativeQuery = true)
    List<MetricResult> findLatestSuccessfulMetricsByServerId(@Param("serverId") UUID serverId);

    // Performans istatistikleri
    @Query("SELECT AVG(mr.executionTimeMs) FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.success = true AND mr.recordedAt > :sinceTime")
    Double getAverageExecutionTime(@Param("serverId") UUID serverId, @Param("sinceTime") Long sinceTime);

    @Query("SELECT COUNT(mr) FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.success = true AND mr.recordedAt > :sinceTime")
    Long getSuccessfulMetricCount(@Param("serverId") UUID serverId, @Param("sinceTime") Long sinceTime);

    @Query("SELECT COUNT(mr) FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.success = false AND mr.recordedAt > :sinceTime")
    Long getFailedMetricCount(@Param("serverId") UUID serverId, @Param("sinceTime") Long sinceTime);

    // Eski kayıtları temizleme
    @Modifying
    @Query("DELETE FROM MetricResult mr WHERE mr.recordedAt < :cutoffTime")
    int deleteOldMetrics(@Param("cutoffTime") Long cutoffTime);

    // JSON sorguları (PostgreSQL JSONB)
    @Query(value = "SELECT * FROM metric_results WHERE result_data->>'cpu_usage' IS NOT NULL AND server_id = :serverId ORDER BY recorded_at DESC LIMIT :limit", nativeQuery = true)
    List<MetricResult> findCpuMetricsByServerId(@Param("serverId") UUID serverId, @Param("limit") int limit);

    @Query(value = "SELECT * FROM metric_results WHERE result_data->>'memory_usage' IS NOT NULL AND server_id = :serverId ORDER BY recorded_at DESC LIMIT :limit", nativeQuery = true)
    List<MetricResult> findMemoryMetricsByServerId(@Param("serverId") UUID serverId, @Param("limit") int limit);

    @Query(value = "SELECT * FROM metric_results WHERE result_data->>'active_connections' IS NOT NULL AND server_id = :serverId ORDER BY recorded_at DESC LIMIT :limit", nativeQuery = true)
    List<MetricResult> findConnectionMetricsByServerId(@Param("serverId") UUID serverId, @Param("limit") int limit);

    // Yeni metrik türü desteği için query'ler

    // Server, metrik query ve success durumuna göre sonuçlar
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.metricQueryId = :metricQueryId AND mr.success = :success ORDER BY mr.recordedAt DESC")
    List<MetricResult> findByServerIdAndMetricQueryIdAndSuccessOrderByRecordedAtDesc(
        @Param("serverId") UUID serverId,
        @Param("metricQueryId") UUID metricQueryId,
        @Param("success") Boolean success
    );

    // Zaman aralığı ve success durumuna göre sonuçlar
    @Query("SELECT mr FROM MetricResult mr WHERE mr.serverId = :serverId AND mr.metricQueryId = :metricQueryId AND mr.recordedAt BETWEEN :startTime AND :endTime AND mr.success = :success ORDER BY mr.recordedAt ASC")
    List<MetricResult> findByServerIdAndMetricQueryIdAndRecordedAtBetweenAndSuccess(
        @Param("serverId") UUID serverId,
        @Param("metricQueryId") UUID metricQueryId,
        @Param("startTime") Long startTime,
        @Param("endTime") Long endTime,
        @Param("success") Boolean success
    );
}
