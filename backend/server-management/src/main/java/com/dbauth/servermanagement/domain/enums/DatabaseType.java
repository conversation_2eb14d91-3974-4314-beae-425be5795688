package com.dbauth.servermanagement.domain.enums;

import lombok.Getter;

/**
 * Database Type Enum - Domain Layer
 * Onion Architecture: Domain katmanında business enum
 * Clean Code: Business logic ve default values eklendi
 * Note: Default port'lar sadece fallback, gerçek port'lar dinamik olarak server entity'sinden alınır
 */
@Getter
public enum DatabaseType {
    POSTGRESQL("PostgreSQL", "jdbc:postgresql", 5432, "postgres"),
    MYSQL("MySQL", "jdbc:mysql", 3306, "mysql"),
    ORACLE("Oracle", "jdbc:oracle:thin", 1521, "XE"),
    SQL_SERVER("SQL Server", "jdbc:sqlserver", 1433, "master"),
    MONGODB("MongoDB", "mongodb", 27017, "admin"),
    REDIS("Redis", "redis", 6379, "0");

    private final String displayName;
    private final String protocol;
    private final int defaultPort;
    private final String defaultDatabase;

    DatabaseType(String displayName, String protocol, int defaultPort, String defaultDatabase) {
        this.displayName = displayName;
        this.protocol = protocol;
        this.defaultPort = defaultPort;
        this.defaultDatabase = defaultDatabase;
    }

    public boolean isRelational() {
        return this == POSTGRESQL || this == MYSQL || this == ORACLE || this == SQL_SERVER;
    }

    public boolean isNoSQL() {
        return this == MONGODB || this == REDIS;
    }
}
