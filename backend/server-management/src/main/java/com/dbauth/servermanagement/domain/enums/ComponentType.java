package com.dbauth.servermanagement.domain.enums;

import lombok.Getter;

/**
 * Component Type Enum - Domain Layer
 * UI bileşen türlerini tanımlar
 */
@Getter
public enum ComponentType {
    
    // ===== ANLIK VERİLER (Zaman aralığı gerektirmez) =====
    
    /**
     * Sayı Kartı - Büyük sayı gösterimi
     * Kullanım: An<PERSON><PERSON><PERSON> değ<PERSON>ler, sayaçlar
     * Örnek: CPU: 85%, RAM: 4.2GB
     */
    NUMBER_CARD("Number Card", "Sayı Kartı", false, true, false),
    
    /**
     * Durum Kartı - Renkli durum gösterimi
     * Kullanım: Aktif/Pasif, Sağlıklı/Hatalı durumlar
     * Örnek: Sunucu Durumu: Aktif (Yeşil)
     */
    STATUS_CARD("Status Card", "Durum Kartı", false, false, true),
    
    /**
     * İlerleme Çubuğu - Yüzde gösterimi
     * Kullanım: Disk kullanımı, bellek kullanımı
     * Örnek: Disk: [████████░░] 80%
     */
    PROGRESS_BAR("Progress Bar", "İlerleme Çubuğu", false, true, false),
    
    /**
     * Rozet - Küçük sayı rozeti
     * Kullanım: Bildirim sayısı, hata sayısı
     * Örnek: Hatalar: (5)
     */
    BADGE("Badge", "Rozet", false, true, false),
    
    /**
     * İkon + Sayı - İkonlu gösterim
     * Kullanım: Kategorize edilmiş sayılar
     * Örnek: 👥 42 kullanıcı
     */
    ICON_NUMBER("Icon Number", "İkon + Sayı", false, true, false),
    
    // ===== ZAMAN ARALIKLI VERİLER (Zaman aralığı gerektirir) =====
    
    /**
     * Trend Kartı - Sayı + mini grafik
     * Kullanım: Anlık değer + trend gösterimi
     * Örnek: CPU: 72% ▁▃▅▇▅▃▁
     */
    TREND_CARD("Trend Card", "Trend Kartı", true, true, false),
    
    /**
     * Çizgi Grafiği - Tam boyut çizgi grafik
     * Kullanım: Zaman serisi verileri
     * Örnek: CPU kullanımının 24 saatlik grafiği
     */
    LINE_CHART("Line Chart", "Çizgi Grafiği", true, true, false),
    
    /**
     * Bar Grafiği - Sütun grafik
     * Kullanım: Kategorik veriler, karşılaştırmalar
     * Örnek: Veritabanı boyutları, bağlantı türleri
     */
    BAR_CHART("Bar Chart", "Bar Grafiği", true, true, true),
    
    /**
     * Alan Grafiği - Doldurulmuş alan
     * Kullanım: Kümülatif veriler, toplam gösterimler
     * Örnek: Toplam network trafiği
     */
    AREA_CHART("Area Chart", "Alan Grafiği", true, true, false),
    
    /**
     * Tablo - Detaylı veri tablosu
     * Kullanım: Çoklu kolon, detaylı veriler
     * Örnek: Aktif bağlantılar listesi
     */
    TABLE("Table", "Tablo", true, true, true),
    
    /**
     * İstatistik Kartı - Min/Max/Ortalama
     * Kullanım: Özet istatistikler
     * Örnek: Son 1 saat: Min: 45%, Max: 89%, Ort: 72%
     */
    STATISTICS_CARD("Statistics Card", "İstatistik Kartı", true, true, false),
    
    /**
     * Karşılaştırma Kartı - Önceki dönemle karşılaştırma
     * Kullanım: Dönemsel karşılaştırmalar
     * Örnek: Bu hafta vs geçen hafta
     */
    COMPARISON_CARD("Comparison Card", "Karşılaştırma Kartı", true, true, false);

    private final String code;
    private final String displayName;
    private final boolean requiresTimeRange;
    private final boolean supportsNumericData;
    private final boolean supportsStringData;

    ComponentType(String code, String displayName, boolean requiresTimeRange, 
                 boolean supportsNumericData, boolean supportsStringData) {
        this.code = code;
        this.displayName = displayName;
        this.requiresTimeRange = requiresTimeRange;
        this.supportsNumericData = supportsNumericData;
        this.supportsStringData = supportsStringData;
    }

    /**
     * Bileşenin zaman aralığı gerektirip gerektirmediğini kontrol eder
     */
    public boolean needsTimeRange() {
        return requiresTimeRange;
    }

    /**
     * Bileşenin sayısal veri destekleyip desteklemediğini kontrol eder
     */
    public boolean supportsNumeric() {
        return supportsNumericData;
    }

    /**
     * Bileşenin string veri destekleyip desteklemediğini kontrol eder
     */
    public boolean supportsString() {
        return supportsStringData;
    }

    /**
     * Veri tipine göre uyumlu bileşenleri getirir
     */
    public static ComponentType[] getCompatibleComponents(String dataType) {
        boolean isNumeric = "int".equalsIgnoreCase(dataType) || "INT".equalsIgnoreCase(dataType);
        
        return java.util.Arrays.stream(values())
                .filter(component -> isNumeric ? component.supportsNumeric() : component.supportsString())
                .toArray(ComponentType[]::new);
    }

    /**
     * Metrik tipine göre önerilen bileşenleri getirir
     */
    public static ComponentType[] getRecommendedForMetricType(MetricType metricType, String dataType) {
        boolean isNumeric = "int".equalsIgnoreCase(dataType) || "INT".equalsIgnoreCase(dataType);
        
        return switch (metricType) {
            case LATEST -> isNumeric ? 
                new ComponentType[]{NUMBER_CARD, PROGRESS_BAR, BADGE, ICON_NUMBER} :
                new ComponentType[]{STATUS_CARD, BADGE};
                
            case AVERAGE -> isNumeric ?
                new ComponentType[]{TREND_CARD, STATISTICS_CARD, NUMBER_CARD} :
                new ComponentType[]{TABLE, STATUS_CARD};
                
            case RANGE -> isNumeric ?
                new ComponentType[]{LINE_CHART, BAR_CHART, AREA_CHART, TABLE} :
                new ComponentType[]{TABLE, BAR_CHART};
        };
    }

    /**
     * String değerden ComponentType'a dönüştürme
     */
    public static ComponentType fromCode(String code) {
        for (ComponentType type : values()) {
            if (type.code.equalsIgnoreCase(code) || type.name().equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Geçersiz bileşen türü: " + code);
    }

    /**
     * Bileşen için varsayılan konfigürasyon
     */
    public String getDefaultConfig() {
        return switch (this) {
            case NUMBER_CARD -> """
                {
                    "color": "blue",
                    "unit": "",
                    "showTrend": true,
                    "size": "medium"
                }
                """;
            case STATUS_CARD -> """
                {
                    "colorMapping": {
                        "active": "green",
                        "inactive": "red",
                        "pending": "yellow"
                    }
                }
                """;
            case PROGRESS_BAR -> """
                {
                    "color": "blue",
                    "showPercentage": true,
                    "animated": true
                }
                """;
            case LINE_CHART -> """
                {
                    "color": "blue",
                    "smooth": true,
                    "showPoints": true,
                    "defaultTimeRange": "1h"
                }
                """;
            case BAR_CHART -> """
                {
                    "color": "purple",
                    "orientation": "vertical",
                    "defaultTimeRange": "1h"
                }
                """;
            case TABLE -> """
                {
                    "pageSize": 10,
                    "sortable": true,
                    "filterable": true
                }
                """;
            default -> "{}";
        };
    }
}
