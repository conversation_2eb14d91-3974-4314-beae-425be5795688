package com.dbauth.servermanagement.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.UUID;

/**
 * Metrik Test Sonucu DTO
 * Metrik sorgularının test sonuçlarını taşır
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricTestResultDto {

    private UUID queryId;
    private String metricName;
    private String sqlQuery;
    private boolean success;
    private boolean valid;
    private String validationMessage;
    private Long executionTimeMs;
    private Long rowCount;
    private String errorMessage;
    private String errorCode;
    private Map<String, Object> resultData;
    private String databaseType;
    private String metricType;
    private String metricCategory;
    private Long testedAt;

    // Helper methods
    public boolean isSuccessful() {
        return success;
    }

    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    public boolean isValid() {
        return valid;
    }

    public String getFormattedExecutionTime() {
        if (executionTimeMs == null) {
            return "N/A";
        }
        if (executionTimeMs < 1000) {
            return executionTimeMs + "ms";
        } else {
            return String.format("%.2fs", executionTimeMs / 1000.0);
        }
    }

    public String getResultSummary() {
        if (success) {
            return String.format("✅ Success - %d rows in %s", 
                    rowCount != null ? rowCount : 0, 
                    getFormattedExecutionTime());
        } else {
            return String.format("❌ Failed - %s", 
                    errorMessage != null ? errorMessage : "Unknown error");
        }
    }

    public static MetricTestResultDto success(UUID queryId, String metricName, String sqlQuery, 
                                            Long executionTimeMs, Long rowCount, 
                                            Map<String, Object> resultData) {
        return MetricTestResultDto.builder()
                .queryId(queryId)
                .metricName(metricName)
                .sqlQuery(sqlQuery)
                .success(true)
                .valid(true)
                .executionTimeMs(executionTimeMs)
                .rowCount(rowCount)
                .resultData(resultData)
                .testedAt(System.currentTimeMillis())
                .build();
    }

    public static MetricTestResultDto failure(UUID queryId, String metricName, String sqlQuery, 
                                            Long executionTimeMs, String errorMessage, String errorCode) {
        return MetricTestResultDto.builder()
                .queryId(queryId)
                .metricName(metricName)
                .sqlQuery(sqlQuery)
                .success(false)
                .valid(false)
                .executionTimeMs(executionTimeMs)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .testedAt(System.currentTimeMillis())
                .build();
    }

    public static MetricTestResultDto validation(UUID queryId, String metricName, boolean valid, 
                                               String validationMessage) {
        return MetricTestResultDto.builder()
                .queryId(queryId)
                .metricName(metricName)
                .valid(valid)
                .validationMessage(validationMessage)
                .testedAt(System.currentTimeMillis())
                .build();
    }
}
