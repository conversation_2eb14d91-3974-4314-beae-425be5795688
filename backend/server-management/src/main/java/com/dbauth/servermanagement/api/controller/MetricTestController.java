package com.dbauth.servermanagement.api.controller;

import com.dbauth.servermanagement.api.dto.MetricTestResultDto;
import com.dbauth.servermanagement.application.service.MetricTestService;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * Metrik Test Controller
 * Metrik sorgularının test edilmesi için endpoint'ler sağlar
 */
@RestController
@RequestMapping("/api/metric-test")
@RequiredArgsConstructor
@Slf4j
public class MetricTestController {

    private final MetricTestService metricTestService;

    /**
     * <PERSON><PERSON><PERSON> bir sunucu için tek bir metrik sorgusunu test eder
     */
    @PostMapping("/server/{serverId}/query/{queryId}")
    public ResponseEntity<MetricTestResultDto> testSingleMetricQuery(
            @PathVariable UUID serverId,
            @PathVariable UUID queryId) {

        log.info("Sunucu {} için metrik sorgusu {} test ediliyor", serverId, queryId);

        try {
            MetricTestResultDto result = metricTestService.testSingleQuery(serverId, queryId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Metrik sorgusu test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Belirli bir sunucu için tüm aktif metrik sorgularını test eder
     */
    @PostMapping("/server/{serverId}/test-all")
    public ResponseEntity<List<MetricTestResultDto>> testAllMetricQueriesForServer(
            @PathVariable UUID serverId) {

        log.info("Sunucu {} için tüm metrik sorguları test ediliyor", serverId);

        try {
            List<MetricTestResultDto> results = metricTestService.testAllQueriesForServer(serverId);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("Sunucu metrik sorguları test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Belirli bir veritabanı türü için tüm metrik sorgularını test eder
     */
    @PostMapping("/database-type/{databaseType}/test-queries")
    public ResponseEntity<List<MetricTestResultDto>> testMetricQueriesByDatabaseType(
            @PathVariable String databaseType) {

        log.info("Veritabanı türü {} için metrik sorguları test ediliyor", databaseType);

        try {
            DatabaseType dbType = DatabaseType.valueOf(databaseType.toUpperCase());
            List<MetricTestResultDto> results = metricTestService.testQueriesByDatabaseType(dbType);
            return ResponseEntity.ok(results);
        } catch (IllegalArgumentException e) {
            log.error("Geçersiz veritabanı türü: {}", databaseType);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Veritabanı türü metrik sorguları test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Belirli bir kategori için tüm metrik sorgularını test eder
     */
    @PostMapping("/category/{metricCategory}/test-queries")
    public ResponseEntity<List<MetricTestResultDto>> testMetricQueriesByCategory(
            @PathVariable String metricCategory) {

        log.info("Kategori {} için metrik sorguları test ediliyor", metricCategory);

        try {
            List<MetricTestResultDto> results = metricTestService.testQueriesByCategory(metricCategory);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("Kategori metrik sorguları test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Belirli bir metrik türü için tüm metrik sorgularını test eder
     */
    @PostMapping("/metric-type/{metricType}/test-queries")
    public ResponseEntity<List<MetricTestResultDto>> testMetricQueriesByMetricType(
            @PathVariable String metricType) {

        log.info("Metrik türü {} için metrik sorguları test ediliyor", metricType);

        try {
            MetricType type = MetricType.valueOf(metricType.toUpperCase());
            List<MetricTestResultDto> results = metricTestService.testQueriesByMetricType(type);
            return ResponseEntity.ok(results);
        } catch (IllegalArgumentException e) {
            log.error("Geçersiz metrik türü: {}", metricType);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Metrik türü sorguları test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Belirli bir metrik sorgusunun geçerliliğini kontrol eder
     */
    @PostMapping("/query/{queryId}/validate")
    public ResponseEntity<MetricTestResultDto> validateMetricQuery(
            @PathVariable UUID queryId) {

        log.info("Metrik sorgusu {} doğrulanıyor", queryId);

        try {
            MetricTestResultDto result = metricTestService.validateQuery(queryId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Metrik sorgusu doğrulanırken hata oluştu: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Tüm aktif metrik sorgularını test eder
     */
    @PostMapping("/test-all-active")
    public ResponseEntity<List<MetricTestResultDto>> testAllActiveQueries() {

        log.info("Tüm aktif metrik sorguları test ediliyor");

        try {
            List<MetricTestResultDto> results = metricTestService.testAllActiveQueries();
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("Tüm aktif metrik sorguları test edilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test sonuçlarının özetini getirir
     */
    @GetMapping("/results/summary")
    public ResponseEntity<Object> getTestResultsSummary() {

        log.info("Test sonuçları özeti getiriliyor");

        try {
            Object summary = metricTestService.getTestResultsSummary();
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            log.error("Test sonuçları özeti getirilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
}
