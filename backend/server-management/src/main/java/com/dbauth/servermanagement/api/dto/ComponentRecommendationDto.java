package com.dbauth.servermanagement.api.dto;

import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Component Recommendation DTO
 * Metrik tipi ve veri tipine göre bileşen önerilerini taşır
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentRecommendationDto {

    private String dataType;
    private MetricType metricType;
    private List<ComponentRecommendation> recommended;
    private List<ComponentRecommendation> compatible;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComponentRecommendation {
        private ComponentType componentType;
        private String displayName;
        private String description;
        private boolean requiresTimeRange;
        private boolean supportsNumeric;
        private boolean supportsString;
        private String defaultConfig;
        private String previewExample;
    }

    /**
     * Metrik tipi ve veri tipine göre önerileri oluşturur
     */
    public static ComponentRecommendationDto create(String dataType, MetricType metricType) {
        ComponentType[] recommendedTypes = ComponentType.getRecommendedForMetricType(metricType, dataType);
        ComponentType[] compatibleTypes = ComponentType.getCompatibleComponents(dataType);

        List<ComponentRecommendation> recommended = java.util.Arrays.stream(recommendedTypes)
                .map(ComponentRecommendationDto::createRecommendation)
                .toList();

        List<ComponentRecommendation> compatible = java.util.Arrays.stream(compatibleTypes)
                .map(ComponentRecommendationDto::createRecommendation)
                .toList();

        return ComponentRecommendationDto.builder()
                .dataType(dataType)
                .metricType(metricType)
                .recommended(recommended)
                .compatible(compatible)
                .build();
    }

    private static ComponentRecommendation createRecommendation(ComponentType componentType) {
        return ComponentRecommendation.builder()
                .componentType(componentType)
                .displayName(componentType.getDisplayName())
                .description(getComponentDescription(componentType))
                .requiresTimeRange(componentType.needsTimeRange())
                .supportsNumeric(componentType.supportsNumeric())
                .supportsString(componentType.supportsString())
                .defaultConfig(componentType.getDefaultConfig())
                .previewExample(getPreviewExample(componentType))
                .build();
    }

    private static String getComponentDescription(ComponentType componentType) {
        return switch (componentType) {
            case NUMBER_CARD -> "Büyük sayı gösterimi, anlık değerler için ideal";
            case STATUS_CARD -> "Renkli durum gösterimi, aktif/pasif durumlar için";
            case PROGRESS_BAR -> "Yüzde gösterimi, disk/bellek kullanımı için";
            case BADGE -> "Küçük sayı rozeti, bildirim sayıları için";
            case ICON_NUMBER -> "İkonlu gösterim, kategorize edilmiş sayılar için";
            case TREND_CARD -> "Sayı + mini grafik, trend gösterimi ile";
            case LINE_CHART -> "Çizgi grafik, zaman serisi verileri için";
            case BAR_CHART -> "Sütun grafik, kategorik veriler için";
            case AREA_CHART -> "Alan grafik, kümülatif veriler için";
            case TABLE -> "Detaylı tablo, çoklu kolon verileri için";
            case STATISTICS_CARD -> "İstatistik kartı, min/max/ortalama gösterimi";
            case COMPARISON_CARD -> "Karşılaştırma kartı, dönemsel karşılaştırmalar için";
        };
    }

    private static String getPreviewExample(ComponentType componentType) {
        return switch (componentType) {
            case NUMBER_CARD -> "85% ↗️ +5%";
            case STATUS_CARD -> "🟢 Aktif";
            case PROGRESS_BAR -> "[████████░░] 80%";
            case BADGE -> "(42)";
            case ICON_NUMBER -> "👥 42 kullanıcı";
            case TREND_CARD -> "72% ▁▃▅▇▅▃▁";
            case LINE_CHART -> "📈 Çizgi grafik";
            case BAR_CHART -> "📊 Bar grafik";
            case AREA_CHART -> "📉 Alan grafik";
            case TABLE -> "📋 Detaylı tablo";
            case STATISTICS_CARD -> "Min: 45% | Max: 89% | Ort: 72%";
            case COMPARISON_CARD -> "Bu hafta: +15% ↗️";
        };
    }
}
