package com.dbauth.servermanagement.domain.repository;

import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.enums.DatabaseType;


import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface MetricQueryRepository {
    
    List<MetricQuery> findAll();
    
    List<MetricQuery> findByDatabaseType(DatabaseType databaseType);
    
    List<MetricQuery> findByDatabaseTypeAndIsActive(DatabaseType databaseType, Boolean isActive);

    List<MetricQuery> findByDatabaseTypeAndIsActiveOrderByExecutionOrder(DatabaseType databaseType, Boolean isActive);

    List<MetricQuery> findByMetricCategory(String metricCategory);

    // MetricType artık display field seviyesinde

    Optional<MetricQuery> findById(UUID id);

    MetricQuery save(MetricQuery metricQuery);

    void deleteById(UUID id);

    List<MetricQuery> findByDatabaseTypeOrderByExecutionOrder(DatabaseType databaseType);

    // Eksik metodlar
    List<MetricQuery> findByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType);

    List<MetricQuery> findByMetricCategoryAndIsActiveTrue(String metricCategory);

    List<MetricQuery> findByIsActiveTrue();

    long count();

    long countByIsActiveTrue();

    long countByDatabaseTypeAndIsActiveTrue(DatabaseType databaseType);
}
