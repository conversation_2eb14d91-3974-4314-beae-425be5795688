package com.dbauth.servermanagement.application.service;

import com.dbauth.servermanagement.api.dto.ComponentRecommendationDto;
import com.dbauth.servermanagement.application.dto.MetricDisplayFieldDto;
import com.dbauth.servermanagement.domain.entity.MetricDisplayField;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.repository.MetricQueryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class MetricQueryService {

    private final MetricQueryRepository metricQueryRepository;

    public List<MetricQuery> getAllMetricQueries() {
        log.debug("Tüm metrik sorguları getiriliyor");
        return metricQueryRepository.findAll();
    }

    public List<MetricQuery> getMetricQueriesByDatabaseType(DatabaseType databaseType) {
        log.debug("Veritabanı tipi {} için metrik sorguları getiriliyor", databaseType);
        return metricQueryRepository.findByDatabaseType(databaseType);
    }

    public List<MetricQuery> getActiveMetricQueriesByDatabaseType(DatabaseType databaseType) {
        log.debug("Veritabanı tipi {} için aktif metrik sorguları getiriliyor", databaseType);
        return metricQueryRepository.findByDatabaseTypeAndIsActive(databaseType, true);
    }

    public List<MetricQuery> getMetricQueriesByCategory(String metricCategory) {
        log.debug("Metrik kategorisi {} için sorgular getiriliyor", metricCategory);
        return metricQueryRepository.findByMetricCategory(metricCategory);
    }

    // MetricType artık display field seviyesinde - bu metod kaldırıldı

    public MetricQuery getMetricQueryById(UUID id) {
        log.debug("ID {} ile metrik sorgusu getiriliyor", id);
        return metricQueryRepository.findById(id).orElse(null);
    }

    @Transactional
    public MetricQuery createMetricQuery(MetricQuery metricQuery) {
        log.info("Yeni metrik sorgusu oluşturuluyor: {}", metricQuery.getQueryName());

        // Metrik konfigürasyon validation
        if (!metricQuery.isValidConfiguration()) {
            throw new IllegalArgumentException("Geçersiz metrik konfigürasyonu: Display fields eksik");
        }

        // Önce MetricQuery'yi kaydet
        MetricQuery savedQuery = metricQueryRepository.save(metricQuery);

        // DisplayFields'ların metricQueryId'sini set et
        if (savedQuery.getDisplayFields() != null) {
            savedQuery.getDisplayFields().forEach(field -> {
                field.setMetricQueryId(savedQuery.getId());
            });
        }

        return savedQuery;
    }

    @Transactional
    public MetricQuery updateMetricQuery(UUID id, MetricQuery updatedQuery) {
        log.info("Metrik sorgusu güncelleniyor: {}", id);

        MetricQuery existingQuery = metricQueryRepository.findById(id).orElse(null);
        if (existingQuery == null) {
            throw new RuntimeException("Metrik sorgusu bulunamadı: " + id);
        }

        // Sadece null olmayan alanları güncelle (partial update)
        if (updatedQuery.getQueryName() != null) {
            existingQuery.setQueryName(updatedQuery.getQueryName());
        }
        if (updatedQuery.getQueryDescription() != null) {
            existingQuery.setQueryDescription(updatedQuery.getQueryDescription());
        }
        if (updatedQuery.getSqlQuery() != null) {
            existingQuery.setSqlQuery(updatedQuery.getSqlQuery());
        }
        // MetricType artık display field seviyesinde
        if (updatedQuery.getIsActive() != null) {
            existingQuery.setIsActive(updatedQuery.getIsActive());
        }
        if (updatedQuery.getExecutionOrder() != null) {
            existingQuery.setExecutionOrder(updatedQuery.getExecutionOrder());
        }
        if (updatedQuery.getDataType() != null) {
            existingQuery.setDataType(updatedQuery.getDataType());
        }
        if (updatedQuery.getDefaultTimeRange() != null) {
            existingQuery.setDefaultTimeRange(updatedQuery.getDefaultTimeRange());
        }
        if (updatedQuery.getDisplayMapping() != null) {
            existingQuery.setDisplayMapping(updatedQuery.getDisplayMapping());
        }
        if (updatedQuery.getSupportsTimeRange() != null) {
            existingQuery.setSupportsTimeRange(updatedQuery.getSupportsTimeRange());
        }

        existingQuery.setUpdatedAt(LocalDateTime.now());

        log.info("Metrik sorgusu güncellendi: {} - Display mapping: {}",
                existingQuery.getQueryName(),
                existingQuery.getDisplayMapping() != null ? "Var" : "Yok");

        return metricQueryRepository.save(existingQuery);
    }

    @Transactional
    public void deleteMetricQuery(UUID id) {
        log.info("Metrik sorgusu siliniyor: {}", id);

        // Önce kayıt var mı kontrol et
        MetricQuery existingQuery = metricQueryRepository.findById(id).orElse(null);
        if (existingQuery == null) {
            throw new RuntimeException("Metrik sorgusu bulunamadı: " + id);
        }

        metricQueryRepository.deleteById(id);
    }

    @Transactional
    public MetricQuery toggleMetricQueryStatus(UUID id) {
        log.info("Metrik sorgusu durumu değiştiriliyor: {}", id);
        
        MetricQuery query = metricQueryRepository.findById(id).orElse(null);
        if (query == null) {
            throw new RuntimeException("Metrik sorgusu bulunamadı: " + id);
        }

        query.setIsActive(!query.getIsActive());
        return metricQueryRepository.save(query);
    }

    public List<MetricQuery> getOrderedMetricQueriesByDatabaseType(DatabaseType databaseType) {
        log.debug("Veritabanı tipi {} için sıralı metrik sorguları getiriliyor", databaseType);
        return metricQueryRepository.findByDatabaseTypeOrderByExecutionOrder(databaseType);
    }

    /**
     * Metrik tipi ve veri tipine göre bileşen önerilerini getirir
     */
    public ComponentRecommendationDto getComponentRecommendations(String dataType, MetricType metricType) {
        log.debug("Veri tipi {} ve metrik tipi {} için bileşen önerileri getiriliyor", dataType, metricType);
        return ComponentRecommendationDto.create(dataType, metricType);
    }

    /**
     * Tüm bileşen türlerini getirir
     */
    public List<ComponentType> getAllComponentTypes() {
        log.debug("Tüm bileşen türleri getiriliyor");
        return List.of(ComponentType.values());
    }

    /**
     * Veri tipine uyumlu bileşen türlerini getirir
     */
    public List<ComponentType> getCompatibleComponentTypes(String dataType) {
        log.debug("Veri tipi {} için uyumlu bileşen türleri getiriliyor", dataType);
        return List.of(ComponentType.getCompatibleComponents(dataType));
    }

    /**
     * Metrik display field'ları DTO olarak getirir
     */
    public List<MetricDisplayFieldDto> getMetricDisplayFields(UUID metricQueryId) {
        log.debug("Metrik sorgusu {} için display field'lar getiriliyor", metricQueryId);

        MetricQuery metricQuery = metricQueryRepository.findById(metricQueryId)
                .orElseThrow(() -> new RuntimeException("Metrik sorgusu bulunamadı: " + metricQueryId));

        if (metricQuery.getDisplayFields() == null) {
            return List.of();
        }

        return metricQuery.getDisplayFields().stream()
                .map(MetricDisplayFieldDto::fromEntity)
                .toList();
    }

    /**
     * Display field'ın bileşen konfigürasyonunu günceller
     */
    @Transactional
    public MetricDisplayFieldDto updateDisplayFieldComponent(UUID metricQueryId, String columnName,
                                                           ComponentType componentType,
                                                           java.util.Map<String, Object> componentConfig) {
        log.info("Display field bileşeni güncelleniyor: {} - {} -> {}",
                metricQueryId, columnName, componentType);

        MetricQuery metricQuery = metricQueryRepository.findById(metricQueryId)
                .orElseThrow(() -> new RuntimeException("Metrik sorgusu bulunamadı: " + metricQueryId));

        MetricDisplayField displayField = metricQuery.getDisplayFields().stream()
                .filter(field -> field.getColumnName().equals(columnName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Display field bulunamadı: " + columnName));

        // Uyumluluk kontrolü
        boolean isNumeric = "int".equalsIgnoreCase(displayField.getDataType()) ||
                           "INT".equalsIgnoreCase(displayField.getDataType());
        boolean isCompatible = isNumeric ? componentType.supportsNumeric() : componentType.supportsString();

        if (!isCompatible) {
            throw new IllegalArgumentException(
                String.format("Bileşen tipi %s veri tipi %s ile uyumlu değil",
                             componentType, displayField.getDataType()));
        }

        displayField.setComponentType(componentType);
        displayField.setComponentConfig(componentConfig != null ? componentConfig : new java.util.HashMap<>());
        displayField.setUpdatedAt(LocalDateTime.now());

        metricQueryRepository.save(metricQuery);

        return MetricDisplayFieldDto.fromEntity(displayField);
    }

    // MetricType artık display field seviyesinde - bu metodlar kaldırıldı
}
