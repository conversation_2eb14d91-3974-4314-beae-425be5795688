package com.dbauth.servermanagement.infrastructure.repository;

import com.dbauth.servermanagement.infrastructure.entity.ServerEntity;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA Server Repository - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında JPA entity kullanır
 * Clean Code: ServerEntity kullanır, Server domain entity değil
 * Fixed: Domain entity yerine Infrastructure entity kullanımı
 */
@Repository
public interface JpaServerRepository extends JpaRepository<ServerEntity, UUID> {

    Optional<ServerEntity> findByName(String name);

    List<ServerEntity> findByIsActive(Boolean isActive);

    List<ServerEntity> findByDatabaseType(DatabaseType databaseType);

    List<ServerEntity> findByIsActiveAndDatabaseType(Boolean isActive, DatabaseType databaseType);

    boolean existsByName(String name);

    boolean existsByNameAndIdNot(String name, UUID id);
}
