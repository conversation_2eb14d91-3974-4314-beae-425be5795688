package com.dbauth.servermanagement.infrastructure.service;

import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.shared.security.PasswordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;

/**
 * Database Connection Service - Infrastructure Layer
 * Onion Architecture: Infrastructure katmanında external system connection
 * Clean Code: Single Responsibility - sadece database connection
 */
@Service("serverDatabaseConnectionService")
@RequiredArgsConstructor
@Slf4j
public class DatabaseConnectionService {

    private static final int DEFAULT_CONNECTION_TIMEOUT = 30; // seconds
    private static final int DEFAULT_QUERY_TIMEOUT = 30; // seconds

    private final PasswordUtil passwordUtil;

    /**
     * Server connection'ını test eder
     */
    public ConnectionTestResult testConnection(Server server) {
        if (server == null) {
            return ConnectionTestResult.failure("Server bilgisi boş");
        }

        if (!server.isConnectionValid()) {
            return ConnectionTestResult.failure("Server connection bilgileri eksik");
        }

        Instant startTime = Instant.now();
        
        try {
            String connectionUrl = buildConnectionUrl(server);
            log.debug("Testing connection to: {}", connectionUrl);

            // Handle password decryption - check if it's already encrypted
            String decryptedPassword = server.getPassword();
            if (decryptedPassword != null && !decryptedPassword.isEmpty()) {
                // Try to decrypt - if it fails, assume it's plain text (for new servers)
                try {
                    // Check if password looks like it's encrypted (Base64 encoded)
                    if (isEncryptedPassword(decryptedPassword)) {
                        decryptedPassword = passwordUtil.decrypt(decryptedPassword);
                        log.debug("Successfully decrypted password for server: {}", server.getName());
                    } else {
                        log.debug("Using plain text password for server: {} (likely new server)", server.getName());
                    }
                } catch (Exception e) {
                    log.warn("Failed to decrypt password for server: {} - {}, using as plain text",
                           server.getName(), e.getMessage());
                    // If decryption fails, use the password as-is (plain text)
                }
            }

            try (Connection connection = DriverManager.getConnection(
                    connectionUrl,
                    server.getUsername(),
                    decryptedPassword)) {
                
                // Test connection validity
                if (connection.isValid(DEFAULT_CONNECTION_TIMEOUT)) {
                    Duration duration = Duration.between(startTime, Instant.now());
                    log.info("Connection test successful for server: {} ({}ms)", 
                            server.getName(), duration.toMillis());
                    
                    return ConnectionTestResult.success(
                        String.format("Bağlantı başarılı (%dms)", duration.toMillis()),
                        duration.toMillis()
                    );
                } else {
                    return ConnectionTestResult.failure("Connection validation failed");
                }
            }
            
        } catch (SQLException e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.warn("Connection test failed for server: {} - {}", server.getName(), e.getMessage());
            
            return ConnectionTestResult.failure(
                String.format("Bağlantı hatası: %s", e.getMessage()),
                duration.toMillis()
            );
        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("Unexpected error during connection test for server: {}", server.getName(), e);
            
            return ConnectionTestResult.failure(
                String.format("Beklenmeyen hata: %s", e.getMessage()),
                duration.toMillis()
            );
        }
    }

    /**
     * Server için connection URL oluşturur
     * Port bilgisi dinamik olarak server entity'sinden alınır
     * Default port sadece fallback olarak kullanılır
     */
    private String buildConnectionUrl(Server server) {
        DatabaseType dbType = server.getDatabaseType();
        String host = server.getHost();

        // Server'ın kendi metodlarını kullan - dinamik port ve database
        int port = server.getActualPort();
        String databaseName = server.getActualDatabaseName();

        log.debug("Building connection URL for server: {} with dynamic port: {} (using default: {}), database: {} (using default: {})",
                 server.getName(), port, server.isUsingDefaultPort(), databaseName, server.isUsingDefaultDatabase());

        return switch (dbType) {
            case POSTGRESQL -> String.format("jdbc:postgresql://%s:%d/%s", host, port, databaseName);
            case MYSQL -> String.format("jdbc:mysql://%s:%d/%s", host, port, databaseName);
            case ORACLE -> String.format("**************************", host, port, databaseName);
            case SQL_SERVER -> String.format("********************************************************************************",
                host, port, databaseName);
            case MONGODB -> String.format("mongodb://%s:%d/%s", host, port, databaseName);
            case REDIS -> String.format("redis://%s:%d/%s", host, port, databaseName);
        };
    }

    /**
     * Connection Test Result
     */
    public static class ConnectionTestResult {
        private final boolean success;
        private final String message;
        private final long responseTimeMs;

        private ConnectionTestResult(boolean success, String message, long responseTimeMs) {
            this.success = success;
            this.message = message;
            this.responseTimeMs = responseTimeMs;
        }

        public static ConnectionTestResult success(String message, long responseTimeMs) {
            return new ConnectionTestResult(true, message, responseTimeMs);
        }

        public static ConnectionTestResult failure(String message) {
            return new ConnectionTestResult(false, message, 0);
        }

        public static ConnectionTestResult failure(String message, long responseTimeMs) {
            return new ConnectionTestResult(false, message, responseTimeMs);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public long getResponseTimeMs() { return responseTimeMs; }
    }

    /**
     * Check if password appears to be encrypted (Base64 encoded)
     */
    private boolean isEncryptedPassword(String password) {
        if (password == null || password.isEmpty()) {
            return false;
        }

        // Simple check: if it's valid Base64 and ends with padding, it's likely encrypted
        try {
            java.util.Base64.getDecoder().decode(password);
            // AES encrypted passwords always end with = or == (Base64 padding)
            return password.endsWith("=") || password.endsWith("==");
        } catch (IllegalArgumentException e) {
            // Not valid Base64, so it's plain text
            return false;
        }
    }
}
