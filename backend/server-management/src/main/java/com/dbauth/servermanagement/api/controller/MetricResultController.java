package com.dbauth.servermanagement.api.controller;

import com.dbauth.servermanagement.api.dto.MetricResultDto;
import com.dbauth.servermanagement.application.service.MetricResultService;
import com.dbauth.servermanagement.domain.enums.MetricType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/metric-results")
@RequiredArgsConstructor
@Slf4j
public class MetricResultController {

    private final MetricResultService metricResultService;

    @GetMapping("/server/{serverId}")
    public ResponseEntity<List<MetricResultDto>> getMetricsByServerId(
            @PathVariable UUID serverId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        
        log.info("Sunucu {} için metrik sonuçları istendi (sayfa: {}, boyut: {})", serverId, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        List<MetricResultDto> metrics = metricResultService.getMetricsByServerId(serverId, pageable);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/latest")
    public ResponseEntity<List<MetricResultDto>> getLatestMetricsByServerId(@PathVariable UUID serverId) {
        log.info("Sunucu {} için en son metrik sonuçları istendi", serverId);
        
        List<MetricResultDto> metrics = metricResultService.getLatestMetricsByServerId(serverId);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/successful")
    public ResponseEntity<List<MetricResultDto>> getSuccessfulMetricsByServerId(
            @PathVariable UUID serverId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("Sunucu {} için başarılı metrik sonuçları istendi", serverId);
        
        Pageable pageable = PageRequest.of(page, size);
        List<MetricResultDto> metrics = metricResultService.getSuccessfulMetricsByServerId(serverId, pageable);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/failed")
    public ResponseEntity<List<MetricResultDto>> getFailedMetricsByServerId(
            @PathVariable UUID serverId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("Sunucu {} için başarısız metrik sonuçları istendi", serverId);
        
        Pageable pageable = PageRequest.of(page, size);
        List<MetricResultDto> metrics = metricResultService.getFailedMetricsByServerId(serverId, pageable);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/metric/{metricName}")
    public ResponseEntity<List<MetricResultDto>> getMetricsByServerIdAndMetricName(
            @PathVariable UUID serverId,
            @PathVariable String metricName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("Sunucu {} için {} metrik sonuçları istendi", serverId, metricName);
        
        Pageable pageable = PageRequest.of(page, size);
        List<MetricResultDto> metrics = metricResultService.getMetricsByServerIdAndMetricName(serverId, metricName, pageable);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/time-range")
    public ResponseEntity<List<MetricResultDto>> getMetricsByTimeRange(
            @PathVariable UUID serverId,
            @RequestParam Long startTime,
            @RequestParam Long endTime) {
        
        log.info("Sunucu {} için {} - {} arası metrik sonuçları istendi", serverId, startTime, endTime);
        
        List<MetricResultDto> metrics = metricResultService.getMetricsByServerIdAndTimeRange(serverId, startTime, endTime);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/server/{serverId}/stats")
    public ResponseEntity<Map<String, Object>> getMetricStats(@PathVariable UUID serverId) {
        log.info("Sunucu {} için metrik istatistikleri istendi", serverId);
        
        Map<String, Object> stats = metricResultService.getMetricStats(serverId);
        
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/recent")
    public ResponseEntity<List<MetricResultDto>> getRecentMetrics(
            @RequestParam(defaultValue = "3600000") Long sinceMs, // Son 1 saat
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size) {
        
        log.info("Son {} ms içindeki metrik sonuçları istendi", sinceMs);
        
        long sinceTime = System.currentTimeMillis() - sinceMs;
        Pageable pageable = PageRequest.of(page, size);
        List<MetricResultDto> metrics = metricResultService.getRecentMetrics(sinceTime, pageable);
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/{id}")
    public ResponseEntity<MetricResultDto> getMetricById(@PathVariable UUID id) {
        log.info("ID {} ile metrik sonucu istendi", id);
        
        return metricResultService.getMetricById(id)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupOldMetrics(
            @RequestParam(defaultValue = "2592000000") Long olderThanMs) { // 30 gün
        
        log.info("{} ms'den eski metrik kayıtları temizleniyor", olderThanMs);
        
        long cutoffTime = System.currentTimeMillis() - olderThanMs;
        int deletedCount = metricResultService.cleanupOldMetrics(cutoffTime);
        
        Map<String, Object> result = Map.of(
            "deletedCount", deletedCount,
            "cutoffTime", cutoffTime,
            "message", deletedCount + " eski metrik kaydı temizlendi"
        );
        
        return ResponseEntity.ok(result);
    }

    @GetMapping("/server/{serverId}/by-type")
    public ResponseEntity<List<MetricResultDto>> getMetricsByType(
            @PathVariable UUID serverId,
            @RequestParam MetricType metricType,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime) {

        log.info("Sunucu {} için {} türünde metrikler istendi (zaman aralığı: {} - {})",
                serverId, metricType, startTime, endTime);

        // Metrik türüne göre zaman aralığı kontrolü
        if (metricType.needsTimeRange() && (startTime == null || endTime == null)) {
            return ResponseEntity.badRequest().build();
        }

        List<MetricResultDto> metrics = metricResultService.getMetricsByTypeAndTimeRange(
            serverId, metricType, startTime, endTime);

        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/metric-types")
    public ResponseEntity<MetricType[]> getMetricTypes() {
        log.info("Metrik türleri istendi");
        return ResponseEntity.ok(MetricType.values());
    }
}
