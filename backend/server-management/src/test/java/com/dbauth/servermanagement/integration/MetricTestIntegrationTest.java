package com.dbauth.servermanagement.integration;

import com.dbauth.servermanagement.config.TestSecurityConfig;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricDisplayField;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ContextConfiguration(classes = {TestSecurityConfig.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class MetricTestIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static JdbcTemplate staticJdbcTemplate;

    private Server testServer;
    private MetricQuery testMetricQuery;
    private UUID testServerId;
    private UUID testMetricQueryId;

    @BeforeEach
    void setUp() {
        staticJdbcTemplate = jdbcTemplate;
        
        // Create test server
        testServer = Server.builder()
                .name("Test PostgreSQL Server")
                .host("localhost")
                .port(5432)
                .username("test_user")
                .password("test_password")
                .databaseType(DatabaseType.POSTGRESQL)
                .isActive(true)
                .build();

        // Create test metric query
        MetricDisplayField testDisplayField = MetricDisplayField.builder()
                .columnName("connection_count")
                .displayName("Connection Count")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();

        testMetricQuery = MetricQuery.builder()
                .queryName("Test Connection Count")
                .queryDescription("Test query for connection count")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as connection_count FROM pg_stat_activity")
                .metricCategory("CONNECTION")
                .isActive(true)
                .executionOrder(1)
                .supportsTimeRange(false)
                .displayFields(List.of(testDisplayField))
                .build();
    }

    @Test
    void shouldTestSingleMetricQuery() throws Exception {
        // Given - Create a server and metric query first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        MvcResult queryResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String queryResponseContent = queryResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(queryResponseContent, MetricQuery.class);
        testMetricQueryId = createdQuery.getId();

        // When & Then - Test single metric query
        mockMvc.perform(post("/api/metric-test/server/{serverId}/query/{queryId}", 
                        testServerId, testMetricQueryId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.metricName").value("Test Connection Count"))
                .andExpect(jsonPath("$.executionTimeMs").exists());
    }

    @Test
    void shouldTestAllMetricQueriesForServer() throws Exception {
        // Given - Create a server and multiple metric queries
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create multiple metric queries
        MetricQuery query1 = MetricQuery.builder()
                .queryName("Test Query 1")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1 as test_value")
                .metricCategory("TEST")
                .isActive(true)
                .displayFields(List.of(createSimpleDisplayField("test_value", "Test Value")))
                .build();

        MetricQuery query2 = MetricQuery.builder()
                .queryName("Test Query 2")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT current_timestamp as current_time")
                .metricCategory("TEST")
                .isActive(true)
                .displayFields(List.of(createSimpleDisplayField("current_time", "Current Time")))
                .build();

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(query1)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(query2)))
                .andExpect(status().isOk());

        // When & Then - Test all metric queries for server
        mockMvc.perform(post("/api/metric-test/server/{serverId}/test-all", testServerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].metricName").exists())
                .andExpect(jsonPath("$[0].success").exists())
                .andExpect(jsonPath("$[1].metricName").exists())
                .andExpect(jsonPath("$[1].success").exists());
    }

    @Test
    void shouldTestMetricQueriesByDatabaseType() throws Exception {
        // Given - Create metric queries for different database types
        MetricQuery postgresQuery = MetricQuery.builder()
                .queryName("PostgreSQL Test Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT version() as postgres_version")
                .metricCategory("SYSTEM")
                .isActive(true)
                .build();

        MetricQuery mysqlQuery = MetricQuery.builder()
                .queryName("MySQL Test Query")
                .databaseType(DatabaseType.MYSQL)
                .sqlQuery("SELECT VERSION() as mysql_version")
                .metricCategory("SYSTEM")
                .isActive(true)
                .build();

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlQuery)))
                .andExpect(status().isOk());

        // When & Then - Test PostgreSQL queries
        mockMvc.perform(post("/api/metric-test/database-type/POSTGRESQL/test-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricName").value("PostgreSQL Test Query"));

        // When & Then - Test MySQL queries
        mockMvc.perform(post("/api/metric-test/database-type/MYSQL/test-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricName").value("MySQL Test Query"));
    }

    @Test
    void shouldTestMetricQueriesByCategory() throws Exception {
        // Given - Create metric queries for different categories
        MetricQuery connectionQuery = MetricQuery.builder()
                .queryName("Connection Test Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'")
                .metricCategory("CONNECTION")
                .isActive(true)
                .build();

        MetricQuery performanceQuery = MetricQuery.builder()
                .queryName("Performance Test Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as query_count FROM pg_stat_statements")
                .metricCategory("PERFORMANCE")
                .isActive(true)
                .build();

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(connectionQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(performanceQuery)))
                .andExpect(status().isOk());

        // When & Then - Test CONNECTION category queries
        mockMvc.perform(post("/api/metric-test/category/CONNECTION/test-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricName").value("Connection Test Query"));

        // When & Then - Test PERFORMANCE category queries
        mockMvc.perform(post("/api/metric-test/category/PERFORMANCE/test-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricName").value("Performance Test Query"));
    }

    @Test
    void shouldValidateMetricQuery() throws Exception {
        // Given - Create a metric query
        MvcResult queryResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String queryResponseContent = queryResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(queryResponseContent, MetricQuery.class);
        testMetricQueryId = createdQuery.getId();

        // When & Then - Validate metric query
        mockMvc.perform(post("/api/metric-test/query/{queryId}/validate", testMetricQueryId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").exists())
                .andExpect(jsonPath("$.metricName").value("Test Connection Count"))
                .andExpect(jsonPath("$.validationMessage").exists());
    }

    @Test
    void shouldHandleInvalidServerId() throws Exception {
        // When & Then - Test with non-existent server ID
        UUID nonExistentServerId = UUID.randomUUID();
        
        mockMvc.perform(post("/api/metric-test/server/{serverId}/test-all", nonExistentServerId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldHandleInvalidQueryId() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // When & Then - Test with non-existent query ID
        UUID nonExistentQueryId = UUID.randomUUID();
        
        mockMvc.perform(post("/api/metric-test/server/{serverId}/query/{queryId}", 
                        testServerId, nonExistentQueryId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldHandleInvalidDatabaseType() throws Exception {
        // When & Then - Test with invalid database type
        mockMvc.perform(post("/api/metric-test/database-type/INVALID_DB/test-queries"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldTestMetricQueriesByMetricType() throws Exception {
        // Given - Create metric queries with different types
        MetricQuery latestQuery = MetricQuery.builder()
                .queryName("Latest Type Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1 as latest_value")
                .metricCategory("TEST")
                .isActive(true)
                .build();

        MetricQuery averageQuery = MetricQuery.builder()
                .queryName("Average Type Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT AVG(value) as avg_value FROM test_table")
                .metricCategory("TEST")
                .numericFieldName("avg_value")
                .supportsTimeRange(true)
                .isActive(true)
                .build();

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(latestQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(averageQuery)))
                .andExpect(status().isOk());

        // MetricType endpoint'leri artık yok - test kaldırıldı
    }

    @AfterAll
    static void cleanupDatabase() {
        if (staticJdbcTemplate != null) {
            try {
                // Clean up test data
                staticJdbcTemplate.execute("DELETE FROM metric_queries");
                staticJdbcTemplate.execute("DELETE FROM servers");

                System.out.println("✅ Metric Test database cleaned up successfully");
            } catch (Exception e) {
                System.err.println("❌ Error cleaning up metric test database: " + e.getMessage());
            }
        }
    }

    // Helper method to create a simple display field
    private MetricDisplayField createSimpleDisplayField(String columnName, String displayName) {
        return MetricDisplayField.builder()
                .columnName(columnName)
                .displayName(displayName)
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();
    }
}
