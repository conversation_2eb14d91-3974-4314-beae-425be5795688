package com.dbauth.servermanagement.integration;

import com.dbauth.servermanagement.config.TestSecurityConfig;
import com.dbauth.servermanagement.domain.entity.MetricQuery;
import com.dbauth.servermanagement.domain.entity.MetricDisplayField;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.dbauth.servermanagement.domain.enums.MetricType;
import com.dbauth.servermanagement.domain.enums.ComponentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Metric Query Integration Test
 * Tests the complete flow of adding, managing, and executing metric queries
 * 
 * Test Scenarios:
 * - Create new metric queries for different database types
 * - Retrieve metric queries by database type
 * - Test metric query execution
 * - Update and manage metric queries
 * - Validation and error handling
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ContextConfiguration(classes = TestSecurityConfig.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class MetricQueryIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static JdbcTemplate staticJdbcTemplate;

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        MetricQueryIntegrationTest.staticJdbcTemplate = jdbcTemplate;
    }

    private MetricQuery postgresqlMetricQuery;
    private MetricQuery mysqlMetricQuery;
    private MetricQuery updateMetricQuery;

    @BeforeEach
    void setUp() {
        // PostgreSQL metric query for testing
        Map<String, Object> displayMapping = new HashMap<>();
        displayMapping.put("chartType", "line");
        displayMapping.put("unit", "connections");
        displayMapping.put("color", "#3b82f6");

        // Create display fields for PostgreSQL query
        MetricDisplayField postgresDisplayField = MetricDisplayField.builder()
                .columnName("active_connections")
                .displayName("Active Connections")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();

        postgresqlMetricQuery = MetricQuery.builder()
                .queryName("Active Connections Test")
                .queryDescription("Test metric to count active database connections")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'")
                .metricCategory("CONNECTION")
                .isActive(true)
                .executionOrder(1)
                .displayMapping(displayMapping)
                .dataType("snapshot")
                .defaultTimeRange("1h")
                .supportsTimeRange(false)
                .displayFields(List.of(postgresDisplayField))
                .build();

        // MySQL metric query for testing
        MetricDisplayField mysqlDisplayField = MetricDisplayField.builder()
                .columnName("process_count")
                .displayName("Process Count")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();

        mysqlMetricQuery = MetricQuery.builder()
                .queryName("MySQL Process List Count")
                .queryDescription("Count of active MySQL processes")
                .databaseType(DatabaseType.MYSQL)
                .sqlQuery("SELECT COUNT(*) as process_count FROM INFORMATION_SCHEMA.PROCESSLIST WHERE COMMAND != 'Sleep'")
                .metricCategory("PERFORMANCE")
                .isActive(true)
                .executionOrder(2)
                .dataType("snapshot")
                .defaultTimeRange("5m")
                .supportsTimeRange(false)
                .displayFields(List.of(mysqlDisplayField))
                .build();

        // Update metric query for testing updates
        MetricDisplayField updateDisplayField1 = MetricDisplayField.builder()
                .columnName("total_connections")
                .displayName("Total Connections")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();

        MetricDisplayField updateDisplayField2 = MetricDisplayField.builder()
                .columnName("active_connections")
                .displayName("Active Connections")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(2)
                .build();

        updateMetricQuery = MetricQuery.builder()
                .queryName("Updated Active Connections")
                .queryDescription("Updated test metric for active connections")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as total_connections, count(*) FILTER (WHERE state = 'active') as active_connections FROM pg_stat_activity")
                .metricCategory("CONNECTION")
                .isActive(false)
                .executionOrder(5)
                .dataType("aggregated")
                .defaultTimeRange("15m")
                .supportsTimeRange(true)
                .displayFields(List.of(updateDisplayField1, updateDisplayField2))
                .build();
    }

    @Test
    void shouldCreatePostgreSQLMetricQuery() throws Exception {
        // When & Then
        MvcResult result = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.queryName").value("Active Connections Test"))
                .andExpect(jsonPath("$.databaseType").value("POSTGRESQL"))
                .andExpect(jsonPath("$.metricCategory").value("CONNECTION"))
                .andExpect(jsonPath("$.isActive").value(true))
                .andExpect(jsonPath("$.executionOrder").value(1))
                .andExpect(jsonPath("$.dataType").value("snapshot"))
                .andExpect(jsonPath("$.defaultTimeRange").value("1h"))
                .andExpect(jsonPath("$.supportsTimeRange").value(false))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(responseContent, MetricQuery.class);
        
        assertThat(createdQuery.getId()).isNotNull();
        assertThat(createdQuery.getCreatedAt()).isNotNull();
        assertThat(createdQuery.getDisplayMapping()).isNotNull();
        assertThat(createdQuery.getDisplayMapping().get("chartType")).isEqualTo("line");
    }

    @Test
    void shouldCreateMySQLMetricQuery() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlMetricQuery)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.queryName").value("MySQL Process List Count"))
                .andExpect(jsonPath("$.databaseType").value("MYSQL"))
                .andExpect(jsonPath("$.metricCategory").value("PERFORMANCE"))
                .andExpect(jsonPath("$.sqlQuery").value("SELECT COUNT(*) as process_count FROM INFORMATION_SCHEMA.PROCESSLIST WHERE COMMAND != 'Sleep'"))
                .andExpect(jsonPath("$.isActive").value(true));
    }

    @Test
    void shouldGetAllMetricQueries() throws Exception {
        // Given - Create multiple metric queries
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlMetricQuery)))
                .andExpect(status().isOk());

        // When & Then
        mockMvc.perform(get("/api/metric-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].queryName").exists())
                .andExpect(jsonPath("$[1].queryName").exists());
    }

    @Test
    void shouldGetMetricQueriesByDatabaseType() throws Exception {
        // Given - Create queries for different database types
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlMetricQuery)))
                .andExpect(status().isOk());

        // When & Then - Get PostgreSQL queries only
        mockMvc.perform(get("/api/metric-queries/database-type/POSTGRESQL"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].databaseType").value("POSTGRESQL"))
                .andExpect(jsonPath("$[0].queryName").value("Active Connections Test"));

        // When & Then - Get MySQL queries only
        mockMvc.perform(get("/api/metric-queries/database-type/MYSQL"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].databaseType").value("MYSQL"))
                .andExpect(jsonPath("$[0].queryName").value("MySQL Process List Count"));
    }

    @Test
    void shouldGetActiveMetricQueriesByDatabaseType() throws Exception {
        // Given - Create active and inactive queries
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk());

        // Create inactive query
        MetricQuery inactiveQuery = MetricQuery.builder()
                .queryName("Inactive PostgreSQL Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1")
                .metricCategory("TEST")
                .isActive(false)
                .displayFields(List.of(createSimpleDisplayField("1", "Test Value")))
                .build();

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inactiveQuery)))
                .andExpect(status().isOk());

        // When & Then - Get only active PostgreSQL queries
        mockMvc.perform(get("/api/metric-queries/database-type/POSTGRESQL/active"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].isActive").value(true))
                .andExpect(jsonPath("$[0].queryName").value("Active Connections Test"));
    }

    @Test
    void shouldGetMetricQueryById() throws Exception {
        // Given - Create a metric query first
        MvcResult createResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(createResponseContent, MetricQuery.class);

        // When & Then
        mockMvc.perform(get("/api/metric-queries/{id}", createdQuery.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdQuery.getId().toString()))
                .andExpect(jsonPath("$.queryName").value("Active Connections Test"))
                .andExpect(jsonPath("$.databaseType").value("POSTGRESQL"))
                .andExpect(jsonPath("$.metricCategory").value("CONNECTION"));
    }

    @Test
    void shouldUpdateMetricQuery() throws Exception {
        // Given - Create a metric query first
        MvcResult createResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(createResponseContent, MetricQuery.class);

        // When & Then - Update the metric query
        mockMvc.perform(put("/api/metric-queries/{id}", createdQuery.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateMetricQuery)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.queryName").value("Updated Active Connections"))
                .andExpect(jsonPath("$.queryDescription").value("Updated test metric for active connections"))
                .andExpect(jsonPath("$.isActive").value(false))
                .andExpect(jsonPath("$.executionOrder").value(5))
                .andExpect(jsonPath("$.dataType").value("aggregated"))
                .andExpect(jsonPath("$.defaultTimeRange").value("15m"))
                .andExpect(jsonPath("$.supportsTimeRange").value(true));
    }

    @Test
    void shouldToggleMetricQueryStatus() throws Exception {
        // Given - Create a metric query first
        MvcResult createResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(createResponseContent, MetricQuery.class);

        // Verify initial state
        assertThat(createdQuery.getIsActive()).isTrue();

        // When - Toggle status
        MvcResult toggleResult = mockMvc.perform(patch("/api/metric-queries/{id}/toggle-status", createdQuery.getId()))
                .andExpect(status().isOk())
                .andReturn();

        // Then - Verify status changed
        String toggleResponseContent = toggleResult.getResponse().getContentAsString();
        MetricQuery toggledQuery = objectMapper.readValue(toggleResponseContent, MetricQuery.class);
        assertThat(toggledQuery.getIsActive()).isFalse();

        // Toggle again to verify it goes back to active
        mockMvc.perform(patch("/api/metric-queries/{id}/toggle-status", createdQuery.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isActive").value(true));
    }

    @Test
    void shouldDeleteMetricQuery() throws Exception {
        // Given - Create a metric query first
        MvcResult createResult = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(createResponseContent, MetricQuery.class);

        // When - Delete the metric query
        mockMvc.perform(delete("/api/metric-queries/{id}", createdQuery.getId()))
                .andExpect(status().isOk());

        // Then - Verify deletion by trying to get the deleted query
        mockMvc.perform(get("/api/metric-queries/{id}", createdQuery.getId()))
                .andExpect(status().isNotFound());

        // Also verify it's not in the list
        mockMvc.perform(get("/api/metric-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void shouldGetMetricQueriesByType() throws Exception {
        // Given - Create queries with different metric types
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresqlMetricQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlMetricQuery)))
                .andExpect(status().isOk());

        // When & Then - Get CONNECTION category queries
        mockMvc.perform(get("/api/metric-queries/category/CONNECTION"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricCategory").value("CONNECTION"))
                .andExpect(jsonPath("$[0].queryName").value("Active Connections Test"));

        // When & Then - Get PERFORMANCE category queries
        mockMvc.perform(get("/api/metric-queries/category/PERFORMANCE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricCategory").value("PERFORMANCE"))
                .andExpect(jsonPath("$[0].queryName").value("MySQL Process List Count"));
    }

    @Test
    void shouldGetOrderedMetricQueriesByDatabaseType() throws Exception {
        // Given - Create queries with different execution orders
        MetricQuery firstQuery = MetricQuery.builder()
                .queryName("First Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1")
                
                .metricCategory("TEST")
                .executionOrder(1)
                .isActive(true)
                .build();

        MetricQuery thirdQuery = MetricQuery.builder()
                .queryName("Third Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 3")
                
                .metricCategory("TEST")
                .executionOrder(3)
                .isActive(true)
                .build();

        MetricQuery secondQuery = MetricQuery.builder()
                .queryName("Second Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 2")
                
                .metricCategory("TEST")
                .executionOrder(2)
                .isActive(true)
                .build();

        // Create in random order
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(thirdQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firstQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(secondQuery)))
                .andExpect(status().isOk());

        // When & Then - Get ordered queries
        mockMvc.perform(get("/api/metric-queries/database-type/POSTGRESQL/ordered"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].queryName").value("First Query"))
                .andExpect(jsonPath("$[0].executionOrder").value(1))
                .andExpect(jsonPath("$[1].queryName").value("Second Query"))
                .andExpect(jsonPath("$[1].executionOrder").value(2))
                .andExpect(jsonPath("$[2].queryName").value("Third Query"))
                .andExpect(jsonPath("$[2].executionOrder").value(3));
    }

    @Test
    void shouldHandleValidationErrors() throws Exception {
        // Given - Invalid metric query data
        MetricQuery invalidQuery = MetricQuery.builder()
                .queryName("") // Empty name should fail validation
                .databaseType(null) // Null database type should fail
                .sqlQuery("") // Empty SQL query should fail
                .build();

        // When & Then
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidQuery)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldHandleNonExistentMetricQueryRetrieval() throws Exception {
        // Given - Random UUID that doesn't exist
        UUID nonExistentId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(get("/api/metric-queries/{id}", nonExistentId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldHandleNonExistentMetricQueryUpdate() throws Exception {
        // Given - Random UUID that doesn't exist
        UUID nonExistentId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(put("/api/metric-queries/{id}", nonExistentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateMetricQuery)))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldHandleNonExistentMetricQueryDeletion() throws Exception {
        // Given - Random UUID that doesn't exist
        UUID nonExistentId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(delete("/api/metric-queries/{id}", nonExistentId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldCreateMetricQueryWithMinimalData() throws Exception {
        // Given - Metric query with only required fields
        MetricDisplayField minimalDisplayField = MetricDisplayField.builder()
                .columnName("test_value")
                .displayName("Test Value")
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();

        MetricQuery minimalQuery = MetricQuery.builder()
                .queryName("Minimal Test Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1 as test_value")
                .metricCategory("TEST")
                .displayFields(List.of(minimalDisplayField))
                .build();

        // When & Then
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(minimalQuery)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.queryName").value("Minimal Test Query"))
                .andExpect(jsonPath("$.databaseType").value("POSTGRESQL"))
                .andExpect(jsonPath("$.metricCategory").value("TEST"))
                .andExpect(jsonPath("$.isActive").value(true)) // Should default to true
                .andExpect(jsonPath("$.executionOrder").value(0)) // Should default to 0
                .andExpect(jsonPath("$.dataType").value("snapshot")) // Should default to snapshot
                .andExpect(jsonPath("$.defaultTimeRange").value("1h")) // Should default to 1h
                .andExpect(jsonPath("$.supportsTimeRange").value(false)); // Should default to false
    }

    @Test
    void shouldCreateMetricQueryWithComplexDisplayMapping() throws Exception {
        // Given - Metric query with complex display mapping
        Map<String, Object> complexDisplayMapping = new HashMap<>();
        complexDisplayMapping.put("chartType", "bar");
        complexDisplayMapping.put("unit", "MB");
        complexDisplayMapping.put("color", "#ef4444");
        complexDisplayMapping.put("threshold", Map.of(
                "warning", 80,
                "critical", 95
        ));
        complexDisplayMapping.put("aggregation", "avg");

        MetricQuery complexQuery = MetricQuery.builder()
                .queryName("Complex Display Mapping Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT pg_size_pretty(pg_database_size(current_database())) as db_size")
                
                .metricCategory("STORAGE")
                .displayMapping(complexDisplayMapping)
                .isActive(true)
                .build();

        // When & Then
        MvcResult result = mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(complexQuery)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.queryName").value("Complex Display Mapping Query"))
                .andExpect(jsonPath("$.displayMapping.chartType").value("bar"))
                .andExpect(jsonPath("$.displayMapping.unit").value("MB"))
                .andExpect(jsonPath("$.displayMapping.color").value("#ef4444"))
                .andExpect(jsonPath("$.displayMapping.threshold.warning").value(80))
                .andExpect(jsonPath("$.displayMapping.threshold.critical").value(95))
                .andExpect(jsonPath("$.displayMapping.aggregation").value("avg"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        MetricQuery createdQuery = objectMapper.readValue(responseContent, MetricQuery.class);

        assertThat(createdQuery.getDisplayMapping()).isNotNull();
        assertThat(createdQuery.getDisplayMapping().get("chartType")).isEqualTo("bar");
        assertThat(createdQuery.getDisplayMapping().get("threshold")).isInstanceOf(Map.class);
    }

    @Test
    void shouldCreateMetricQueriesForAllDatabaseTypes() throws Exception {
        // Test creating metric queries for all supported database types
        DatabaseType[] databaseTypes = {
                DatabaseType.POSTGRESQL,
                DatabaseType.MYSQL,
                DatabaseType.ORACLE,
                DatabaseType.SQL_SERVER,
                DatabaseType.MONGODB,
                DatabaseType.REDIS
        };

        for (DatabaseType dbType : databaseTypes) {
            MetricQuery query = MetricQuery.builder()
                    .queryName("Test Query for " + dbType.name())
                    .databaseType(dbType)
                    .sqlQuery("SELECT 1 as test_value")
                    
                    .metricCategory("TEST")
                    .isActive(true)
                    .build();

            mockMvc.perform(post("/api/metric-queries")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(query)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.databaseType").value(dbType.name()))
                    .andExpect(jsonPath("$.queryName").value("Test Query for " + dbType.name()));
        }

        // Verify all queries were created
        mockMvc.perform(get("/api/metric-queries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(databaseTypes.length));
    }

    // MetricType endpoint'leri kaldırıldı - test kaldırıldı

    @Test
    void shouldGetMetricQueriesByDatabaseTypeAndMetricType() throws Exception {
        // Given - Create queries for different database types and metric types
        MetricQuery postgresLatest = MetricQuery.builder()
                .queryName("PostgreSQL Latest Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as connections FROM pg_stat_activity")
                
                .metricCategory("CONNECTION")
                .isActive(true)
                .build();

        MetricQuery postgresAverage = MetricQuery.builder()
                .queryName("PostgreSQL Average Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT AVG(connections) as avg_connections FROM connection_stats")
                
                .metricCategory("CONNECTION")
                .numericFieldName("avg_connections")
                .supportsTimeRange(true)
                .isActive(true)
                .build();

        MetricQuery mysqlLatest = MetricQuery.builder()
                .queryName("MySQL Latest Query")
                .databaseType(DatabaseType.MYSQL)
                .sqlQuery("SELECT COUNT(*) as process_count FROM INFORMATION_SCHEMA.PROCESSLIST")
                
                .metricCategory("CONNECTION")
                .isActive(true)
                .build();

        // Create the queries
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresLatest)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(postgresAverage)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlLatest)))
                .andExpect(status().isOk());

        // MetricType endpoint'leri kaldırıldı - test kaldırıldı
    }

    @Test
    void shouldGetActiveMetricQueriesByDatabaseTypeAndMetricType() throws Exception {
        // Given - Create active and inactive queries
        MetricQuery activeQuery = MetricQuery.builder()
                .queryName("Active PostgreSQL Latest Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as connections FROM pg_stat_activity")
                
                .metricCategory("CONNECTION")
                .isActive(true)
                .build();

        MetricQuery inactiveQuery = MetricQuery.builder()
                .queryName("Inactive PostgreSQL Latest Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT 1 as test")
                
                .metricCategory("CONNECTION")
                .isActive(false)
                .build();

        // Create the queries
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(activeQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inactiveQuery)))
                .andExpect(status().isOk());

        // MetricType endpoint'leri kaldırıldı - test kaldırıldı
    }

    // MetricType endpoint'leri kaldırıldı - testler kaldırıldı

    @Test
    void shouldGetMetricQueriesByCategory() throws Exception {
        // Given - Create queries with different categories
        MetricQuery connectionQuery = MetricQuery.builder()
                .queryName("Connection Category Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT count(*) as connections FROM pg_stat_activity")
                
                .metricCategory("CONNECTION")
                .isActive(true)
                .build();

        MetricQuery performanceQuery = MetricQuery.builder()
                .queryName("Performance Category Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT * FROM pg_stat_statements")
                
                .metricCategory("PERFORMANCE")
                .supportsTimeRange(true)
                .isActive(true)
                .build();

        MetricQuery storageQuery = MetricQuery.builder()
                .queryName("Storage Category Query")
                .databaseType(DatabaseType.POSTGRESQL)
                .sqlQuery("SELECT pg_size_pretty(pg_database_size(current_database()))")
                
                .metricCategory("STORAGE")
                .isActive(true)
                .build();

        // Create the queries
        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(connectionQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(performanceQuery)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/metric-queries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(storageQuery)))
                .andExpect(status().isOk());

        // When & Then - Get CONNECTION category queries
        mockMvc.perform(get("/api/metric-queries/category/CONNECTION"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricCategory").value("CONNECTION"))
                .andExpect(jsonPath("$[0].queryName").value("Connection Category Query"));

        // When & Then - Get PERFORMANCE category queries
        mockMvc.perform(get("/api/metric-queries/category/PERFORMANCE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricCategory").value("PERFORMANCE"))
                .andExpect(jsonPath("$[0].queryName").value("Performance Category Query"));

        // When & Then - Get STORAGE category queries
        mockMvc.perform(get("/api/metric-queries/category/STORAGE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].metricCategory").value("STORAGE"))
                .andExpect(jsonPath("$[0].queryName").value("Storage Category Query"));

        // When & Then - Get non-existent category (should return empty)
        mockMvc.perform(get("/api/metric-queries/category/NON_EXISTENT"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @AfterAll
    static void cleanupDatabase() {
        if (staticJdbcTemplate != null) {
            try {
                // Clean up test data
                staticJdbcTemplate.execute("DELETE FROM metric_queries");
                staticJdbcTemplate.execute("DELETE FROM metric_results");

                System.out.println("✅ Metric Query test database cleaned up successfully");
            } catch (Exception e) {
                System.err.println("❌ Error cleaning up metric query test database: " + e.getMessage());
            }
        }
    }

    // Helper method to create a simple display field
    private MetricDisplayField createSimpleDisplayField(String columnName, String displayName) {
        return MetricDisplayField.builder()
                .columnName(columnName)
                .displayName(displayName)
                .dataType("int")
                .componentType(ComponentType.NUMBER_CARD)
                .metricType(MetricType.LATEST)
                .isActive(true)
                .displayOrder(1)
                .build();
    }
}
