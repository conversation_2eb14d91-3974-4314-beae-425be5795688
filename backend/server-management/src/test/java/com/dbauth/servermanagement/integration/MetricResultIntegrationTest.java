package com.dbauth.servermanagement.integration;

import com.dbauth.servermanagement.config.TestSecurityConfig;
import com.dbauth.servermanagement.domain.entity.Server;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ContextConfiguration(classes = {TestSecurityConfig.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class MetricResultIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static JdbcTemplate staticJdbcTemplate;

    private Server testServer;
    private UUID testServerId;

    @BeforeEach
    void setUp() {
        staticJdbcTemplate = jdbcTemplate;
        
        // Create test server
        testServer = Server.builder()
                .name("Test PostgreSQL Server")
                .host("localhost")
                .port(5432)
                .username("test_user")
                .password("test_password")
                .databaseType(DatabaseType.POSTGRESQL)
                .isActive(true)
                .build();
    }

    @Test
    void shouldGetMetricsByServerId() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}", testServerId)
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3));
    }

    @Test
    void shouldGetLatestMetricsByServerId() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/latest", testServerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetSuccessfulMetricsByServerId() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/successful", testServerId)
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetFailedMetricsByServerId() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results with failures
        createTestMetricResultsWithFailures(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/failed", testServerId)
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetMetricsByServerIdAndMetricName() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/metric/{metricName}",
                        testServerId, "Active Connections")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetMetricsByTimeRange() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        long endTime = System.currentTimeMillis();
        long startTime = endTime - 3600000; // 1 hour ago

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/time-range", testServerId)
                        .param("startTime", String.valueOf(startTime))
                        .param("endTime", String.valueOf(endTime)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetMetricStats() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then
        mockMvc.perform(get("/api/metric-results/server/{serverId}/stats", testServerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalMetrics").exists())
                .andExpect(jsonPath("$.successfulMetrics").exists())
                .andExpect(jsonPath("$.failedMetrics").exists());
    }

    @Test
    void shouldGetRecentMetrics() throws Exception {
        // Given - Create test metric results
        createTestMetricResults(UUID.randomUUID());

        // When & Then
        mockMvc.perform(get("/api/metric-results/recent")
                        .param("sinceMs", "3600000") // 1 hour
                        .param("page", "0")
                        .param("size", "50"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void shouldGetMetricsByType() throws Exception {
        // Given - Create a server first
        MvcResult serverResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testServer)))
                .andExpect(status().isCreated())
                .andReturn();

        String serverResponseContent = serverResult.getResponse().getContentAsString();
        Server createdServer = objectMapper.readValue(serverResponseContent, Server.class);
        testServerId = createdServer.getId();

        // Create test metric results
        createTestMetricResults(testServerId);

        // When & Then - Test LATEST type (no time range needed)
        mockMvc.perform(get("/api/metric-results/server/{serverId}/by-type", testServerId)
                        .param("metricType", "LATEST"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // When & Then - Test AVERAGE type (time range required)
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 3600000;
        
        mockMvc.perform(get("/api/metric-results/server/{serverId}/by-type", testServerId)
                        .param("metricType", "AVERAGE")
                        .param("startTime", String.valueOf(startTime))
                        .param("endTime", String.valueOf(endTime)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // When & Then - Test AVERAGE type without time range (should fail)
        mockMvc.perform(get("/api/metric-results/server/{serverId}/by-type", testServerId)
                        .param("metricType", "AVERAGE"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldCleanupOldMetrics() throws Exception {
        // Given - Create old metric results
        createTestMetricResults(UUID.randomUUID());

        // When & Then
        mockMvc.perform(delete("/api/metric-results/cleanup")
                        .param("olderThanMs", "1000")) // Very short time to ensure cleanup
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deletedCount").exists())
                .andExpect(jsonPath("$.message").exists());
    }

    private void createTestMetricResults(UUID serverId) {
        long currentTime = System.currentTimeMillis();

        try {
            // Insert test metric results directly into database
            String sql = """
                INSERT INTO metric_results (id, server_id, metric_name, sql_query, result_data,
                                          row_count, execution_time_ms, success, recorded_at)
                VALUES (?, ?, ?, ?, ?::jsonb, ?, ?, ?, ?)
                """;

            Map<String, Object> resultData1 = Map.of("active_connections", 5);
            Map<String, Object> resultData2 = Map.of("total_connections", 10);
            Map<String, Object> resultData3 = Map.of("cpu_usage", 75.5);

            jdbcTemplate.update(sql, UUID.randomUUID(), serverId, "Active Connections",
                    "SELECT count(*) as active_connections FROM pg_stat_activity",
                    objectMapper.writeValueAsString(resultData1), 1L, 150L, true, currentTime);

            jdbcTemplate.update(sql, UUID.randomUUID(), serverId, "Total Connections",
                    "SELECT count(*) as total_connections FROM pg_stat_activity",
                    objectMapper.writeValueAsString(resultData2), 1L, 200L, true, currentTime - 1000);

            jdbcTemplate.update(sql, UUID.randomUUID(), serverId, "CPU Usage",
                    "SELECT cpu_usage FROM system_stats",
                    objectMapper.writeValueAsString(resultData3), 1L, 100L, true, currentTime - 2000);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test metric results", e);
        }
    }

    private void createTestMetricResultsWithFailures(UUID serverId) {
        long currentTime = System.currentTimeMillis();

        try {
            String sql = """
                INSERT INTO metric_results (id, server_id, metric_name, sql_query, result_data,
                                          row_count, execution_time_ms, success, error_message, recorded_at)
                VALUES (?, ?, ?, ?, ?::jsonb, ?, ?, ?, ?, ?)
                """;

            Map<String, Object> resultData = Map.of("error", "Connection failed");

            jdbcTemplate.update(sql, UUID.randomUUID(), serverId, "Failed Query",
                    "SELECT * FROM non_existent_table",
                    objectMapper.writeValueAsString(resultData), 0L, 50L, false,
                    "Table 'non_existent_table' doesn't exist", currentTime);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test metric results with failures", e);
        }
    }

    @AfterAll
    static void cleanupDatabase() {
        if (staticJdbcTemplate != null) {
            try {
                // Clean up test data
                staticJdbcTemplate.execute("DELETE FROM metric_results");
                staticJdbcTemplate.execute("DELETE FROM servers");

                System.out.println("✅ Metric Result test database cleaned up successfully");
            } catch (Exception e) {
                System.err.println("❌ Error cleaning up metric result test database: " + e.getMessage());
            }
        }
    }
}
