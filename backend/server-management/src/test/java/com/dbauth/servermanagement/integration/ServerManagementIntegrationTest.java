package com.dbauth.servermanagement.integration;

import com.dbauth.servermanagement.api.dto.CreateServerDto;
import com.dbauth.servermanagement.api.dto.ServerDto;
import com.dbauth.servermanagement.api.dto.UpdateServerDto;
import com.dbauth.servermanagement.config.TestSecurityConfig;
import com.dbauth.servermanagement.domain.enums.DatabaseType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.junit.jupiter.api.AfterAll;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Server Management Integration Test
 * Onion Architecture: Full integration test with PostgreSQL
 * Clean Code: Comprehensive API testing with real database
 * Sexi Robot: Integration test yazıldı! 🤖✨
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ContextConfiguration(classes = TestSecurityConfig.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class ServerManagementIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static JdbcTemplate staticJdbcTemplate;

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        ServerManagementIntegrationTest.staticJdbcTemplate = jdbcTemplate;
    }

    private CreateServerDto createServerDto;
    private UpdateServerDto updateServerDto;

    @BeforeEach
    void setUp() {
        createServerDto = CreateServerDto.builder()
                .name("Test PostgreSQL Server")
                .host("localhost")
                .port(5432)
                .databaseType(DatabaseType.POSTGRESQL)
                .username("postgres")
                .password("admin123")
                .description("Test server for integration testing")
                .isActive(true)
                .build();

        updateServerDto = UpdateServerDto.builder()
                .name("Updated Test Server")
                .host("localhost")
                .port(5433)
                .databaseType(DatabaseType.POSTGRESQL)
                .username("postgres")
                .password("admin123")
                .description("Updated test server")
                .isActive(false)
                .build();
    }

    @Test
    void shouldCreateServer() throws Exception {
        // When & Then
        MvcResult result = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Test PostgreSQL Server"))
                .andExpect(jsonPath("$.host").value("localhost"))
                .andExpect(jsonPath("$.port").value(5432))
                .andExpect(jsonPath("$.databaseType").value("POSTGRESQL"))
                .andExpect(jsonPath("$.isActive").value(true))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(responseContent, ServerDto.class);
        
        assertThat(createdServer.getId()).isNotNull();
        assertThat(createdServer.getCreatedAt()).isNotNull();
        assertThat(createdServer.getUpdatedAt()).isNotNull();
    }

    @Test
    void shouldGetAllServers() throws Exception {
        // Given - Create a server first
        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated());

        // When & Then
        mockMvc.perform(get("/api/servers"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("Test PostgreSQL Server"));
    }

    @Test
    void shouldGetActiveServers() throws Exception {
        // Given - Create active and inactive servers
        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated());

        CreateServerDto inactiveServer = CreateServerDto.builder()
                .name("Inactive Server")
                .host("localhost")
                .port(5433)
                .databaseType(DatabaseType.POSTGRESQL)
                .username("postgres")
                .password("admin123")
                .isActive(false)
                .build();

        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inactiveServer)))
                .andExpect(status().isCreated());

        // When & Then
        mockMvc.perform(get("/api/servers/active"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("Test PostgreSQL Server"))
                .andExpect(jsonPath("$[0].isActive").value(true));
    }

    @Test
    void shouldTestConnection() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/servers/test-connection")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.connectionTime").exists());
    }

    @Test
    void shouldUpdateServer() throws Exception {
        // Given - Create a server first
        MvcResult createResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(createResponseContent, ServerDto.class);

        // When & Then
        mockMvc.perform(put("/api/servers/{id}", createdServer.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateServerDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Updated Test Server"))
                .andExpect(jsonPath("$.port").value(5433))
                .andExpect(jsonPath("$.isActive").value(false));
    }

    @Test
    void shouldDeleteServer() throws Exception {
        // Given - Create a server first
        MvcResult createResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(createResponseContent, ServerDto.class);

        // When - Delete the server
        mockMvc.perform(delete("/api/servers/{id}", createdServer.getId()))
                .andExpect(status().isNoContent());

        // Then - Verify deletion by checking the server list doesn't contain the deleted server
        mockMvc.perform(get("/api/servers"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[?(@.id == '" + createdServer.getId() + "')]").doesNotExist());
    }

    @Test
    void shouldGetServersByDatabaseType() throws Exception {
        // Given - Create servers with different database types
        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated());

        CreateServerDto mysqlServer = CreateServerDto.builder()
                .name("MySQL Server")
                .host("localhost")
                .port(3306)
                .databaseType(DatabaseType.MYSQL)
                .username("root")
                .password("admin123")
                .isActive(true)
                .build();

        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mysqlServer)))
                .andExpect(status().isCreated());

        // When & Then
        mockMvc.perform(get("/api/servers/database-type/POSTGRESQL"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].databaseType").value("POSTGRESQL"));
    }

    @Test
    void shouldGetServerById() throws Exception {
        // Given - Create a server first
        MvcResult createResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(createResponseContent, ServerDto.class);

        // When & Then
        mockMvc.perform(get("/api/servers/{id}", createdServer.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdServer.getId().toString()))
                .andExpect(jsonPath("$.name").value("Test PostgreSQL Server"))
                .andExpect(jsonPath("$.host").value("localhost"))
                .andExpect(jsonPath("$.port").value(5432));
    }

    @Test
    void shouldGetServerByName() throws Exception {
        // Given - Create a server first
        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated());

        // When & Then
        mockMvc.perform(get("/api/servers/name/{name}", "Test PostgreSQL Server"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Test PostgreSQL Server"))
                .andExpect(jsonPath("$.host").value("localhost"))
                .andExpect(jsonPath("$.port").value(5432));
    }

    @Test
    void shouldGetServerDetails() throws Exception {
        // Given - Create a server first
        MvcResult createResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(createResponseContent, ServerDto.class);

        // When & Then
        mockMvc.perform(get("/api/servers/{id}/details", createdServer.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdServer.getId().toString()))
                .andExpect(jsonPath("$.name").value("Test PostgreSQL Server"))
                .andExpect(jsonPath("$.metrics").exists())
                .andExpect(jsonPath("$.stats").exists());
    }

    @Test
    void shouldToggleServerStatus() throws Exception {
        // Given - Create a server first
        MvcResult createResult = mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createServerDto)))
                .andExpect(status().isCreated())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ServerDto createdServer = objectMapper.readValue(createResponseContent, ServerDto.class);

        // When - Toggle status
        mockMvc.perform(patch("/api/servers/{id}/toggle-status", createdServer.getId()))
                .andExpect(status().isOk());

        // Then - Verify status changed
        mockMvc.perform(get("/api/servers/{id}", createdServer.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isActive").value(false)); // Should be toggled to false
    }

    @Test
    void shouldGetDatabaseTypes() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/servers/database-types"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(6)) // POSTGRESQL, MYSQL, ORACLE, SQL_SERVER, MONGODB, REDIS
                .andExpect(jsonPath("$[?(@=='POSTGRESQL')]").exists())
                .andExpect(jsonPath("$[?(@=='MYSQL')]").exists());
    }

    @Test
    void shouldHandleValidationErrors() throws Exception {
        // Given - Invalid server data
        CreateServerDto invalidServer = CreateServerDto.builder()
                .name("") // Empty name should fail validation
                .host("")
                .port(null)
                .databaseType(null)
                .build();

        // When & Then
        mockMvc.perform(post("/api/servers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidServer)))
                .andExpect(status().isBadRequest());
    }

    @AfterAll
    static void cleanupDatabase() {
        if (staticJdbcTemplate != null) {
            try {
                // Clean up test data
                staticJdbcTemplate.execute("DELETE FROM servers");
                staticJdbcTemplate.execute("DELETE FROM metric_results");

                // Reset sequences if needed
                staticJdbcTemplate.execute("ALTER SEQUENCE IF EXISTS servers_id_seq RESTART WITH 1");

                System.out.println("✅ Server Management test database cleaned up successfully");
            } catch (Exception e) {
                System.err.println("❌ Error cleaning up server management test database: " + e.getMessage());
            }
        }
    }
}
