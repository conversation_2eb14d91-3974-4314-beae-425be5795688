package com.dbauth.servermanagement.config;

import com.dbauth.shared.security.JwtUtil;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Test Security Configuration for Server Management
 * Disables all security for integration tests and provides mock JWT utilities
 */
@TestConfiguration
@EnableWebSecurity
public class TestSecurityConfig {

    @Bean
    @Primary
    public SecurityFilter<PERSON>hain testSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth.anyRequest().permitAll())
            .headers(headers -> headers.frameOptions().disable());

        return http.build();
    }

    @Bean
    @Primary
    public JwtUtil mockJwtUtil() {
        JwtUtil mockJwtUtil = Mockito.mock(JwtUtil.class);
        when(mockJwtUtil.validateToken(anyString())).thenReturn(true);
        when(mockJwtUtil.extractEmail(anyString())).thenReturn("<EMAIL>");
        when(mockJwtUtil.extractUsername(anyString())).thenReturn("<EMAIL>");
        return mockJwtUtil;
    }
}
