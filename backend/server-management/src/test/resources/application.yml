spring:
  application:
    name: server-management-test

  datasource:
    url: *********************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  flyway:
    enabled: false

logging:
  level:
    com.dbauth.servermanagement: DEBUG
    org.springframework.scheduling: DEBUG
