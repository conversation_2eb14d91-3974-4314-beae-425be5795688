# Test Environment Configuration for Server Management

server:
  port: 8082

spring:
  application:
    name: server-management-test
  
  # Test Database (Local PostgreSQL)
  datasource:
    url: *********************************************
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: password
    
  # JPA Configuration for Test
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create-drop  # Recreate tables for each test
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true

# Gateway Configuration for Test
gateway:
  url: http://localhost:8080
  security:
    # JWT secret for test environment
    jwt-secret: test-jwt-secret-key-for-testing-environment-must-be-at-least-512-bits-long-to-satisfy-hs512-algorithm-requirements-exactly-64-chars
    jwt-expiration: 86400000

# Server Management Configuration for Test
server-management:
  # Application info
  application-name: server-management-test
  application-version: 1.0.0-test
  
  # Metric collection configuration
  metric-collection:
    enabled: false  # Disable metric collection in tests
    interval: 300000  # 5 minutes

# Test Logging
logging:
  level:
    com.dbauth.servermanagement: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"

# Swagger Configuration for Test
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# Actuator for Test
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: always

# Test specific properties
app:
  environment: test
  debug-mode: true

# Security Configuration for Test
security:
  # JWT authentication devre dışı
  jwt:
    enabled: false

# WebSocket Configuration for Test
websocket:
  enabled: false  # Disable WebSocket in tests
