package com.dbauth.shared.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * CORS Configuration Properties
 * Merkezi CORS konfigürasyonu için dinamik değerler
 */
@Data
@ConfigurationProperties(prefix = "cors")
public class CorsProperties {

    /**
     * İzin verilen origin'ler
     * Varsayılan: localhost development URL'leri
     */
    private List<String> allowedOrigins = Arrays.asList(
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8080",
        "http://localhost:8081",
        "http://localhost:8082"
    );

    /**
     * İzin verilen HTTP metodları
     * Varsayılan: GET, POST, PUT, DELETE, OPTIONS
     */
    private List<String> allowedMethods = Arrays.asList(
        "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"
    );

    /**
     * İzin verilen header'lar
     * Varsayılan: Yaygın kullanılan header'lar
     */
    private List<String> allowedHeaders = Arrays.asList(
        "Origin",
        "Content-Type",
        "Accept",
        "Authorization",
        "X-Requested-With",
        "X-Auth-Token",
        "Cache-Control",
        "Pragma"
    );

    /**
     * Exposed header'lar
     * Varsayılan: Authorization ve Content-Type
     */
    private List<String> exposedHeaders = Arrays.asList(
        "Authorization",
        "Content-Type",
        "X-Total-Count"
    );

    /**
     * Credentials desteği
     * Varsayılan: true
     */
    private boolean allowCredentials = true;

    /**
     * Preflight cache süresi (saniye)
     * Varsayılan: 3600 (1 saat)
     */
    private long maxAge = 3600;

    /**
     * CORS aktif/pasif durumu
     * Varsayılan: true
     */
    private boolean enabled = true;

    /**
     * Debug logging aktif/pasif
     * Varsayılan: false
     */
    private boolean debugLogging = false;

    /**
     * Path pattern'ları
     * Varsayılan: Tüm API endpoint'leri
     */
    private List<String> pathPatterns = Arrays.asList(
        "/api/**",
        "/actuator/**"
    );
}
