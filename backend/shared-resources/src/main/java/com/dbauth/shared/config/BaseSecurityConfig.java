package com.dbauth.shared.config;

import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Base Security Configuration
 * Tüm modüller için ortak security ayarları
 * Her modül bu base'i extend ederek kendi özel ayarlarını ekleyebilir
 */
public abstract class BaseSecurityConfig {

    protected final CorsConfigurationSource corsConfigurationSource;
    protected final UserDetailsService userDetailsService;

    protected BaseSecurityConfig(CorsConfigurationSource corsConfigurationSource,
                               UserDetailsService userDetailsService) {
        this.corsConfigurationSource = corsConfigurationSource;
        this.userDetailsService = userDetailsService;
    }

    /**
     * Ortak PasswordEncoder bean
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * Ortak AuthenticationProvider bean
     */
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    /**
     * Ortak AuthenticationManager bean
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * Base HTTP Security yapılandırması
     * Her modül bu metodu override ederek kendi authorization rules'larını ekleyebilir
     */
    protected void configureBaseHttpSecurity(HttpSecurity http) throws Exception {
        http
            // CORS yapılandırmasını etkinleştir
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            
            // CSRF'yi devre dışı bırak (REST API için)
            .csrf(AbstractHttpConfigurer::disable)
            
            // Session management - Stateless (JWT kullanıyoruz)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // Authentication provider'ı ekle
            .authenticationProvider(authenticationProvider());
    }

    /**
     * Her modül kendi authorization rules'larını implement etmeli
     */
    protected abstract void configureAuthorization(HttpSecurity http) throws Exception;
}
