package com.dbauth.shared.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.List;

/**
 * Merkezi CORS yapılandırması - Tüm mikroservisler için tek nokta yönetimi
 * application.yml üzerinden yapılandırılabilir
 * WebSocket ve REST API'ler için ortak kullanım
 */
@Configuration
@EnableConfigurationProperties(CorsProperties.class)
@RequiredArgsConstructor
@Slf4j
public class CorsConfiguration {

    private final CorsProperties corsProperties;

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        if (!corsProperties.isEnabled()) {
            log.info("CORS devre dışı bırakıldı");
            return null;
        }

        if (corsProperties.isDebugLogging()) {
            log.info("CORS yapılandırması başlatılıyor...");
        }

        org.springframework.web.cors.CorsConfiguration configuration =
            new org.springframework.web.cors.CorsConfiguration();

        // Origin ayarları - dinamik
        if (corsProperties.getAllowedOrigins() != null && !corsProperties.getAllowedOrigins().isEmpty()) {
            configuration.setAllowedOrigins(corsProperties.getAllowedOrigins());
            if (corsProperties.isDebugLogging()) {
                log.info("İzin verilen origin'ler: {}", corsProperties.getAllowedOrigins());
            }
        }

        // Dinamik konfigürasyon
        configuration.setAllowedMethods(corsProperties.getAllowedMethods());
        configuration.setAllowedHeaders(corsProperties.getAllowedHeaders());
        configuration.setExposedHeaders(corsProperties.getExposedHeaders());
        configuration.setAllowCredentials(corsProperties.isAllowCredentials());
        configuration.setMaxAge(corsProperties.getMaxAge());

        if (corsProperties.isDebugLogging()) {
            log.info("CORS ayarları - Methods: {}, Headers: {}, Credentials: {}, MaxAge: {}",
                corsProperties.getAllowedMethods(), corsProperties.getAllowedHeaders(),
                corsProperties.isAllowCredentials(), corsProperties.getMaxAge());
        }

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

        // Path pattern'ları dinamik olarak kaydet
        for (String pathPattern : corsProperties.getPathPatterns()) {
            source.registerCorsConfiguration(pathPattern, configuration);
        }

        log.info("CORS yapılandırması tamamlandı - {} path pattern kayıtlı",
            corsProperties.getPathPatterns().size());
        return source;
    }

    /**
     * WebSocket için CORS yapılandırması
     * WebSocketMessageBrokerConfigurer'da kullanılmak üzere
     */
    public String[] getAllowedOriginPatternsArray() {
        if (corsProperties.getAllowedOrigins() != null && !corsProperties.getAllowedOrigins().isEmpty()) {
            return corsProperties.getAllowedOrigins().toArray(new String[0]);
        }
        return new String[]{"*"};
    }

    /**
     * CORS properties'e erişim için getter
     */
    public CorsProperties getCorsProperties() {
        return corsProperties;
    }
}
