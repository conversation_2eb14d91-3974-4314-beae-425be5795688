package com.dbauth.shared.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Shared RestTemplate Configuration
 * Tüm modüller için ortak RestTemplate ayarları
 * HTTP client timeout ve diğer ayarlar
 */
@Configuration
public class SharedRestTemplateConfig {

    @Value("${http.client.connection-timeout-ms:5000}")
    private int connectionTimeoutMs;

    @Value("${http.client.read-timeout-ms:10000}")
    private int readTimeoutMs;

    /**
     * Shared RestTemplate bean
     * Tüm modüller bu RestTemplate'i kullanabilir
     */
    @Bean("sharedRestTemplate")
    @Primary
    public RestTemplate sharedRestTemplate(RestTemplateBuilder builder) {
        return builder
            .setConnectTimeout(Duration.ofMillis(connectionTimeoutMs))
            .setReadTimeout(Duration.ofMillis(readTimeoutMs))
            .build();
    }

    /**
     * JWT ile authenticated RestTemplate
     * JWT token'ı otomatik olarak header'a ekler
     */
    @Bean("jwtRestTemplate")
    public RestTemplate jwtRestTemplate(RestTemplateBuilder builder) {
        return builder
            .setConnectTimeout(Duration.ofMillis(connectionTimeoutMs))
            .setReadTimeout(Duration.ofMillis(readTimeoutMs))
            .additionalInterceptors((request, body, execution) -> {
                // JWT token interceptor burada eklenebilir
                // Şimdilik basic RestTemplate döndürüyoruz
                return execution.execute(request, body);
            })
            .build();
    }
}
