package com.dbauth.shared.security;

import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Password Utility for encrypting and decrypting database passwords
 * Uses AES encryption for secure password storage
 */
@Component
public class PasswordUtil {
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES";
    
    // Bu key production'da environment variable'dan alınmalı
    private static final String SECRET_KEY = "MySecretKey12345"; // 16 bytes for AES-128
    
    private final SecretKey secretKey;
    
    public PasswordUtil() {
        // Secret key'i oluştur
        this.secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
    }
    
    /**
     * <PERSON><PERSON><PERSON><PERSON> encrypt eder
     */
    public String encrypt(String plainPassword) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(plainPassword.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
            
        } catch (Exception e) {
            throw new RuntimeException("Error encrypting password", e);
        }
    }
    
    /**
     * Şifreyi decrypt eder
     */
    public String decrypt(String encryptedPassword) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedPassword);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            
            return new String(decryptedBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new RuntimeException("Error decrypting password", e);
        }
    }
    
    /**
     * Test için şifreleme/çözme işlemini kontrol eder
     */
    public boolean testEncryption(String originalPassword) {
        try {
            String encrypted = encrypt(originalPassword);
            String decrypted = decrypt(encrypted);
            return originalPassword.equals(decrypted);
        } catch (Exception e) {
            return false;
        }
    }
}
