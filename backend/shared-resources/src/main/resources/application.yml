# Shared Resources - Common Configuration
# Bu dosya tüm modüller tarafından kullanılır

# CORS Configuration - Dinamik konfigürasyon
cors:
  # İzin verilen origin'ler
  allowed-origins:
    - "http://localhost:3000"
    - "http://localhost:3001"
    - "http://localhost:8080"
    - "http://localhost:8081"
    - "http://localhost:8082"
  # İzin verilen HTTP metodları
  allowed-methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "HEAD"
    - "PATCH"
  # İzin verilen header'lar
  allowed-headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Requested-With"
    - "X-Auth-Token"
    - "Cache-Control"
    - "Pragma"
  # Exposed header'lar
  exposed-headers:
    - "Authorization"
    - "Content-Type"
    - "X-Total-Count"
  # Credentials desteği
  allow-credentials: true
  # Preflight cache süresi (saniye)
  max-age: 3600
  # CORS aktif/pasif durumu
  enabled: true
  # Debug logging aktif/pasif
  debug-logging: false
  # Path pattern'ları
  path-patterns:
    - "/api/**"
    - "/actuator/**"

# Common Database Configuration
spring:
  datasource:
    url: ${DATABASE_URL:****************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000

  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

# Common Logging Configuration
logging:
  level:
    com.dbauth: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG
    org.springframework.web.cors: DEBUG

# Common Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
  endpoint:
    health:
      show-details: when-authorized

# Server yapılandırması (her modül kendi portunu override edecek)
server:
  port: 8080
