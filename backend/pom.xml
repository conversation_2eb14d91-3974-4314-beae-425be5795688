<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dbauth</groupId>
    <artifactId>db-auth-v2</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>DB Auth V2 - Parent</name>
    <description>Multi-module Spring Boot application with Onion Architecture</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Spring Boot Version -->
        <spring-boot.version>3.2.0</spring-boot.version>

        <!-- Documentation -->
        <springdoc-openapi.version>2.2.0</springdoc-openapi.version>

        <!-- Database Drivers -->
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <oracle-jdbc.version>********</oracle-jdbc.version>
        <mssql-jdbc.version>12.4.2.jre11</mssql-jdbc.version>

        <!-- Security -->
        <jjwt.version>0.12.3</jjwt.version>

        <!-- Testing -->
        <junit.version>5.10.0</junit.version>

        <!-- Logging -->
        <logback.version>1.4.11</logback.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
    </dependencies>

    <modules>
        <module>shared-resources</module>
        <module>user-management</module>
        <module>server-management</module>
        <module>gateway</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Internal Modules -->
            <dependency>
                <groupId>com.dbauth</groupId>
                <artifactId>shared-resources</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dbauth</groupId>
                <artifactId>user-management</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dbauth</groupId>
                <artifactId>server-management</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dbauth</groupId>
                <artifactId>db-agent</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dbauth</groupId>
                <artifactId>logging</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- JWT Dependencies -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- Documentation -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>

            <!-- Database Drivers -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>

            <!-- Test Dependencies -->
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
