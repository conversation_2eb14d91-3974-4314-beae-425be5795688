2025-06-16 23:17:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-16 23:17:56 [main] INFO  c.d.s.ServerManagementApplication - Starting ServerManagementApplication using Java 17.0.12 with PID 13627 (/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar started by ishak<PERSON> in /Users/<USER>/App/db-auth-v2)
2025-06-16 23:17:56 [main] INFO  c.d.s.ServerManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-16 23:17:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 23:17:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142 ms. Found 3 JPA repository interfaces.
2025-06-16 23:18:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8082 (http)
2025-06-16 23:18:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8082"]
2025-06-16 23:18:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 23:18:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-16 23:18:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 23:18:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3775 ms
2025-06-16 23:18:01 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 23:18:01 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-16 23:18:01 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-16 23:18:02 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16 23:18:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 23:18:02 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6d3163a6
2025-06-16 23:18:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 23:18:02 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-16 23:18:04 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16 23:18:04 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:18:05 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'serverController' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/api/controller/ServerController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'serverService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/ServerService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'metricCollectionService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/MetricCollectionService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'metricQueryRepositoryAdapter' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/infrastructure/repository/MetricQueryRepositoryAdapter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
2025-06-16 23:18:05 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:18:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 23:18:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 23:18:05 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-16 23:18:05 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-16 23:18:05 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'serverController' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/api/controller/ServerController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'serverService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/ServerService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'metricCollectionService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/MetricCollectionService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'metricQueryRepositoryAdapter' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/infrastructure/repository/MetricQueryRepositoryAdapter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:802)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:241)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1193)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.dbauth.servermanagement.ServerManagementApplication.main(ServerManagementApplication.java:27)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:91)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:53)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:58)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'serverService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/ServerService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'metricCollectionService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/MetricCollectionService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'metricQueryRepositoryAdapter' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/infrastructure/repository/MetricQueryRepositoryAdapter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:802)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:241)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1193)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 26 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'metricCollectionService' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/application/service/MetricCollectionService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'metricQueryRepositoryAdapter' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/infrastructure/repository/MetricQueryRepositoryAdapter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:802)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:241)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1193)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 40 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'metricQueryRepositoryAdapter' defined in URL [jar:nested:/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar/!BOOT-INF/classes/!/com/dbauth/servermanagement/infrastructure/repository/MetricQueryRepositoryAdapter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:802)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:241)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1193)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 54 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jpaMetricQueryRepository' defined in com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 68 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); Reason: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract java.util.List com.dbauth.servermanagement.infrastructure.repository.JpaMetricQueryRepository.findByDatabaseTypeAndMetricTypeAndIsActiveTrue(com.dbauth.servermanagement.domain.enums.DatabaseType,com.dbauth.servermanagement.domain.enums.MetricType); No property 'metricType' found for type 'MetricQuery'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:107)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:124)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:258)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111)
	... 91 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'metricType' found for type 'MetricQuery'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:90)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:443)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:419)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:372)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:354)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:332)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:259)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:992)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:260)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:389)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:992)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:390)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:103)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:100)
	... 95 common frames omitted
2025-06-16 23:19:43 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-16 23:19:43 [main] INFO  c.d.s.ServerManagementApplication - Starting ServerManagementApplication using Java 17.0.12 with PID 14043 (/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar started by ishakdas in /Users/<USER>/App/db-auth-v2)
2025-06-16 23:19:43 [main] INFO  c.d.s.ServerManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-16 23:19:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 23:19:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124 ms. Found 3 JPA repository interfaces.
2025-06-16 23:19:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8082 (http)
2025-06-16 23:19:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8082"]
2025-06-16 23:19:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 23:19:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-16 23:19:46 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 23:19:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3414 ms
2025-06-16 23:19:47 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 23:19:47 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-16 23:19:47 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-16 23:19:47 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16 23:19:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 23:19:48 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6d3163a6
2025-06-16 23:19:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 23:19:48 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-16 23:19:49 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16 23:19:50 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:19:51 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16 23:19:52 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 23:19:52 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 27aeeb6d-c83f-42ae-883f-04a312d59db6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-16 23:19:52 [main] INFO  c.d.s.config.SecurityConfig - Configuring Server Management Security...
2025-06-16 23:19:52 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-06-16 23:19:52 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-06-16 23:19:52 [main] DEBUG o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-06-16 23:19:52 [main] INFO  c.d.s.config.SecurityConfig - Server Management Security configuration completed
2025-06-16 23:19:52 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4471f216, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4528a8bf, org.springframework.security.web.context.SecurityContextHolderFilter@1802badc, org.springframework.security.web.header.HeaderWriterFilter@474a63d9, org.springframework.security.web.authentication.logout.LogoutFilter@2f3a3fec, com.dbauth.servermanagement.config.SecurityConfig$SimpleJwtAuthenticationFilter@5bcec67e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2cbb9702, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10fef6da, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1927c2c6, org.springframework.security.web.session.SessionManagementFilter@3cd3cf6b, org.springframework.security.web.access.ExceptionTranslationFilter@293ccbdd, org.springframework.security.web.access.intercept.AuthorizationFilter@6a4d72bd]
2025-06-16 23:19:53 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-06-16 23:19:53 [main] INFO  c.d.shared.config.CorsConfiguration - CORS yapılandırması tamamlandı - 2 path pattern kayıtlı
2025-06-16 23:19:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8082"]
2025-06-16 23:19:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8082 (http) with context path ''
2025-06-16 23:19:53 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-16 23:19:53 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@19544898]]
2025-06-16 23:19:53 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-16 23:19:53 [main] INFO  c.d.s.ServerManagementApplication - Started ServerManagementApplication in 11.24 seconds (process running for 12.038)
2025-06-16 23:19:53 [MessageBroker-1] INFO  c.d.s.a.s.MetricCollectionService - 🚀 Tüm sunucular için metrik toplama işlemi başlatıldı (interval: 300000ms)
2025-06-16 23:19:53 [MessageBroker-1] WARN  c.d.s.a.s.MetricCollectionService - ⚠️ Aktif sunucu bulunamadı, metrik toplama işlemi atlanıyor
2025-06-16 23:20:53 [MessageBroker-3] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 3, completed tasks = 1]
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@19544898]]
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-06-16 23:21:29 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'brokerChannelExecutor'
2025-06-16 23:21:29 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'messageBrokerTaskScheduler'
2025-06-16 23:21:29 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientOutboundChannelExecutor'
2025-06-16 23:21:29 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientInboundChannelExecutor'
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 23:21:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-16 23:22:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-16 23:22:21 [main] INFO  c.d.s.ServerManagementApplication - Starting ServerManagementApplication using Java 17.0.12 with PID 14983 (/Users/<USER>/App/db-auth-v2/backend/server-management/target/server-management-1.0.0-exec.jar started by ishakdas in /Users/<USER>/App/db-auth-v2)
2025-06-16 23:22:21 [main] INFO  c.d.s.ServerManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-16 23:22:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 23:22:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 139 ms. Found 3 JPA repository interfaces.
2025-06-16 23:22:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8082 (http)
2025-06-16 23:22:25 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8082"]
2025-06-16 23:22:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 23:22:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-16 23:22:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 23:22:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4703 ms
2025-06-16 23:22:26 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 23:22:26 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-16 23:22:26 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-16 23:22:27 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16 23:22:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-16 23:22:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@75d97e18
2025-06-16 23:22:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-16 23:22:28 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-16 23:22:29 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16 23:22:29 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:22:31 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16 23:22:32 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 23:22:32 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 15fcc564-cf19-4c0a-be13-50e890c5d337

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-16 23:22:32 [main] INFO  c.d.s.config.SecurityConfig - Configuring Server Management Security...
2025-06-16 23:22:32 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-06-16 23:22:32 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-06-16 23:22:32 [main] DEBUG o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-06-16 23:22:33 [main] INFO  c.d.s.config.SecurityConfig - Server Management Security configuration completed
2025-06-16 23:22:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4528a8bf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1927c2c6, org.springframework.security.web.context.SecurityContextHolderFilter@3a089620, org.springframework.security.web.header.HeaderWriterFilter@1b8d0c21, org.springframework.security.web.authentication.logout.LogoutFilter@1d7d2345, com.dbauth.servermanagement.config.SecurityConfig$SimpleJwtAuthenticationFilter@2dafae61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@10fef6da, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1da77a43, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@79a15001, org.springframework.security.web.session.SessionManagementFilter@6f3f2340, org.springframework.security.web.access.ExceptionTranslationFilter@12f85dc8, org.springframework.security.web.access.intercept.AuthorizationFilter@3e9c59ed]
2025-06-16 23:22:33 [main] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-06-16 23:22:33 [main] INFO  c.d.shared.config.CorsConfiguration - CORS yapılandırması tamamlandı - 2 path pattern kayıtlı
2025-06-16 23:22:33 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8082"]
2025-06-16 23:22:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8082 (http) with context path ''
2025-06-16 23:22:33 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-16 23:22:33 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d63d553]]
2025-06-16 23:22:33 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-16 23:22:33 [main] INFO  c.d.s.ServerManagementApplication - Started ServerManagementApplication in 13.583 seconds (process running for 14.481)
2025-06-16 23:22:33 [MessageBroker-1] INFO  c.d.s.a.s.MetricCollectionService - 🚀 Tüm sunucular için metrik toplama işlemi başlatıldı (interval: 300000ms)
2025-06-16 23:22:33 [MessageBroker-1] WARN  c.d.s.a.s.MetricCollectionService - ⚠️ Aktif sunucu bulunamadı, metrik toplama işlemi atlanıyor
2025-06-16 23:23:33 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 3, completed tasks = 1]
2025-06-16 23:25:16 [http-nio-8082-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 23:25:16 [http-nio-8082-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 23:25:16 [http-nio-8082-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-06-16 23:25:16 [http-nio-8082-exec-1] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/metrics' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-06-16 23:25:16 [http-nio-8082-exec-1] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:25:16 [http-nio-8082-exec-1] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:25:16 [http-nio-8082-exec-2] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:25:16 [http-nio-8082-exec-2] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:25:26 [http-nio-8082-exec-3] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:25:26 [http-nio-8082-exec-3] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:25:50 [http-nio-8082-exec-4] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:25:50 [http-nio-8082-exec-4] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:25:50 [http-nio-8082-exec-5] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:25:50 [http-nio-8082-exec-5] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:26:31 [http-nio-8082-exec-6] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:26:31 [http-nio-8082-exec-6] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:26:31 [http-nio-8082-exec-7] INFO  c.d.s.a.controller.MetricController - Tüm metrikler istendi
2025-06-16 23:26:31 [http-nio-8082-exec-7] INFO  c.d.s.a.controller.MetricController - Toplam 0 metrik döndürüldü
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d63d553]]
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-06-16 23:27:20 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'brokerChannelExecutor'
2025-06-16 23:27:20 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'messageBrokerTaskScheduler'
2025-06-16 23:27:20 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientOutboundChannelExecutor'
2025-06-16 23:27:20 [SpringApplicationShutdownHook] DEBUG o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientInboundChannelExecutor'
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-16 23:27:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
