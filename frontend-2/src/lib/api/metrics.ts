import { api } from '../api'
import type { Metric<PERSON>uery, CreateMetricRequest, UpdateMetricRequest, MetricTestRequest, MetricTestResult, DisplayField } from '../api'

// Database types enum
export const DATABASE_TYPES = {
  MYSQL: 'MYSQL',
  POSTGRESQL: 'POSTGRESQL',
  ORACLE: 'ORACLE',
  MSSQL: 'MSSQL',
  SQLITE: 'SQLITE',
} as const

export type DatabaseType = keyof typeof DATABASE_TYPES

// Metric categories
export const METRIC_CATEGORIES = {
  CPU: 'CPU',
  MEMORY: 'MEMORY', 
  DISK: 'DISK',
  CONNECTION: 'CONNECTION',
  PERFORMANCE: 'PERFORMANCE',
  QUERY: 'QUERY',
  STORAGE: 'STORAGE',
  NETWORK: 'NETWORK',
} as const

export type MetricCategory = keyof typeof METRIC_CATEGORIES



// Metric API functions
export const metricsApi = {
  // Get all metrics
  getAll: async (): Promise<MetricQuery[]> => {
    return api.metrics.getAll()
  },

  // Get metric by ID
  getById: async (id: string): Promise<MetricQuery> => {
    return api.metrics.getById(id)
  },

  // Create new metric
  create: async (metricData: CreateMetricRequest): Promise<MetricQuery> => {
    return api.metrics.create(metricData)
  },

  // Update existing metric
  update: async (id: string, metricData: UpdateMetricRequest): Promise<MetricQuery> => {
    return api.metrics.update(id, metricData)
  },

  // Delete metric
  delete: async (id: string): Promise<void> => {
    return api.metrics.delete(id)
  },

  // Toggle metric status (active/inactive)
  toggleStatus: async (id: string): Promise<MetricQuery> => {
    return api.metrics.toggleStatus(id)
  },

  // Test metric query
  test: async (testData: MetricTestRequest): Promise<MetricTestResult> => {
    return api.metrics.test(testData)
  },

  // Get servers for testing (filtered by database type)
  getServersByType: async (databaseType?: string) => {
    const servers = await api.servers.getAll()
    if (databaseType) {
      return servers.filter((server: any) => server.databaseType === databaseType)
    }
    return servers
  },
}

// Helper functions
export const metricHelpers = {
  // Get display name for database type
  getDatabaseTypeDisplayName: (type: string): string => {
    const displayNames: Record<string, string> = {
      MYSQL: 'MySQL',
      POSTGRESQL: 'PostgreSQL',
      ORACLE: 'Oracle',
      MSSQL: 'SQL Server',
      SQLITE: 'SQLite',
    }
    return displayNames[type] || type
  },

  // Get display name for metric category
  getCategoryDisplayName: (category: string): string => {
    const displayNames: Record<string, string> = {
      CPU: 'İşlemci',
      MEMORY: 'Bellek',
      DISK: 'Disk',
      CONNECTION: 'Bağlantı',
      PERFORMANCE: 'Performans',
      QUERY: 'Sorgu',
      STORAGE: 'Depolama',
      NETWORK: 'Ağ',
    }
    return displayNames[category] || category
  },



  // Validate metric form data
  validateMetricData: (data: CreateMetricRequest): string[] => {
    const errors: string[] = []

    if (!data.queryName?.trim()) {
      errors.push('Metrik adı gereklidir')
    }

    if (!data.databaseType) {
      errors.push('Veritabanı tipi seçilmelidir')
    }

    if (!data.sqlQuery?.trim()) {
      errors.push('SQL sorgusu gereklidir')
    }

    if (data.displayFields && data.displayFields.length > 0) {
      data.displayFields.forEach((field, index) => {
        if (!field.columnName?.trim()) {
          errors.push(`${index + 1}. alanın kolon adı gereklidir`)
        }
        if (!field.displayName?.trim()) {
          errors.push(`${index + 1}. alanın görüntülenen adı gereklidir`)
        }
        if (!field.dataType) {
          errors.push(`${index + 1}. alanın veri tipi seçilmelidir`)
        }
      })
    }

    return errors
  },

  // Create display field from SQL column
  createDisplayFieldFromColumn: (columnName: string): DisplayField => {
    return {
      columnName,
      displayName: columnName.charAt(0).toUpperCase() + columnName.slice(1).replace(/_/g, ' '),
      dataType: 'string', // Default to string, user can change
    }
  },

  // Extract columns from SQL query (basic implementation)
  extractColumnsFromSQL: (sqlQuery: string): string[] => {
    try {
      // Very basic SQL parsing - just look for SELECT ... FROM pattern
      const selectMatch = sqlQuery.match(/SELECT\s+(.*?)\s+FROM/i)
      if (selectMatch) {
        const selectPart = selectMatch[1]
        if (selectPart.trim() === '*') {
          return [] // Can't determine columns from SELECT *
        }
        
        // Split by comma and clean up
        return selectPart
          .split(',')
          .map(col => col.trim().replace(/\s+AS\s+\w+/i, '').trim())
          .filter(col => col && col !== '*')
      }
      return []
    } catch (error) {
      console.error('Error parsing SQL:', error)
      return []
    }
  },

  // Format execution time
  formatExecutionTime: (timeMs: number): string => {
    if (timeMs < 1000) {
      return `${timeMs}ms`
    } else if (timeMs < 60000) {
      return `${(timeMs / 1000).toFixed(2)}s`
    } else {
      return `${(timeMs / 60000).toFixed(2)}m`
    }
  },

  // Format row count
  formatRowCount: (count: number): string => {
    if (count < 1000) {
      return count.toString()
    } else if (count < 1000000) {
      return `${(count / 1000).toFixed(1)}K`
    } else {
      return `${(count / 1000000).toFixed(1)}M`
    }
  },
}

export default metricsApi
