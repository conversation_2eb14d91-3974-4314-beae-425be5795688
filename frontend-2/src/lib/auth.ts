import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"

// Gateway API types
interface AuthRequest {
  email: string
  password: string
}

interface AuthResponse {
  token: string
  type: string
  email: string
  firstName?: string
  lastName?: string
  fullName?: string
  expiresAt: string
  issuedAt: string
}

interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp: string
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Gateway API çağrısı
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            } as AuthRequest),
          })

          if (!response.ok) {
            console.error("Login failed:", response.status, response.statusText)
            return null
          }

          const result: ApiResponse<AuthResponse> = await response.json()

          if (result.success && result.data) {
            const authData = result.data
            return {
              id: authData.email, // Email'i ID olarak kullan
              email: authData.email,
              name: authData.fullName || authData.firstName || authData.email,
              accessToken: authData.token,
              tokenType: authData.type,
              expiresAt: authData.expiresAt,
            }
          }

          console.error("Login failed:", result.error || result.message)
          return null
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }

        return null
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/login",
    signUp: "/auth/register",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.accessToken = (user as any).accessToken
        token.tokenType = (user as any).tokenType
        token.expiresAt = (user as any).expiresAt
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.email = token.email as string
        session.user.name = token.name as string
        ;(session as any).accessToken = token.accessToken
        ;(session as any).tokenType = token.tokenType
        ;(session as any).expiresAt = token.expiresAt
      }
      return session
    },
  },
}
