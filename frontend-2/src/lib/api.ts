const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

// Global token storage for client-side requests
let globalToken: string | null = null

export const setAuthToken = (token: string | null) => {
  globalToken = token
}

export const getAuthToken = () => globalToken

// ===== METRIC COMPONENT SYSTEM =====

// Component Types
export type ComponentType =
  | 'NUMBER_CARD'
  | 'STATUS_CARD'
  | 'PROGRESS_BAR'
  | 'BADGE'
  | 'ICON_NUMBER'
  | 'TREND_CARD'
  | 'LINE_CHART'
  | 'BAR_CHART'
  | 'AREA_CHART'
  | 'TABLE'
  | 'STATISTICS_CARD'
  | 'COMPARISON_CARD';

export interface MetricDisplayField {
  id: string;
  metricQueryId: string;
  columnName: string;
  displayName: string;
  dataType: string;
  metricType: string;
  componentType: ComponentType;
  componentConfig: Record<string, any>;
  description?: string;
  displayOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  componentCompatibleWithDataType: boolean;
  componentConfigAsJson: string;
}

export interface ComponentRecommendation {
  columnName: string;
  dataType: string;
  recommendedComponents: ComponentType[];
  defaultComponent: ComponentType;
  reason: string;
}

// Legacy DisplayField for backward compatibility
export interface DisplayField {
  columnName: string;
  displayName: string;
  dataType: 'int' | 'string';
}

export interface MetricQuery {
  id: string;
  queryName: string;
  queryDescription?: string;
  databaseType: string;
  sqlQuery: string;
  metricCategory?: string;
  displayFields?: MetricDisplayField[]; // Updated to use new type
  legacyDisplayFields?: DisplayField[]; // Keep legacy for compatibility
  validConfiguration?: boolean;
  isActive: boolean;
  executionOrder: number;
  createdAt: string;
  updatedAt?: string;
}

export interface CreateMetricRequest {
  queryName: string;
  queryDescription?: string;
  databaseType: string;
  sqlQuery: string;
  metricCategory?: string;
  displayFields?: DisplayField[]; // Keep legacy for creation
  isActive?: boolean;
  executionOrder?: number;
}

export interface UpdateMetricRequest extends Partial<CreateMetricRequest> {}

export interface MetricTestRequest {
  sqlQuery: string;
  databaseType: string;
  serverId?: string;
}

export interface MetricTestResult {
  success: boolean;
  valid: boolean;
  validationMessage?: string;
  executionTimeMs: number;
  rowCount: number;
  errorMessage?: string;
  errorCode?: string;
  resultData?: Record<string, any>;
  columns?: string[];
}

export class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message)
    this.name = 'ApiError'
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`

  // JWT token'ı global storage'dan al ve header'a ekle
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers as Record<string, string>,
  }

  if (globalToken) {
    headers.Authorization = `Bearer ${globalToken}`
  } else {
    // Token yoksa ve auth gerektiren endpoint ise hata fırlat
    if (!endpoint.includes('/auth/login') && !endpoint.includes('/auth/register')) {
      console.warn('API request made without token:', endpoint)
      throw new ApiError(401, 'Authentication token not available')
    }
  }

  const config: RequestInit = {
    headers,
    ...options,
  }

  try {
    const response = await fetch(url, config)

    if (!response.ok) {
      const errorText = await response.text()
      throw new ApiError(response.status, errorText || `HTTP error! status: ${response.status}`)
    }

    // DELETE işlemleri için 204 No Content kontrolü
    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return null as T
    }

    // Gateway direkt response döndürüyor, wrapper yok
    const result = await response.json()
    return result as T
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(500, 'Network error')
  }
}

// Wrapper response'lar için ayrı fonksiyon (permissions API'si için)
async function apiRequestWithWrapper<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`

  // JWT token'ı global storage'dan al ve header'a ekle
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers as Record<string, string>,
  }

  if (globalToken) {
    headers.Authorization = `Bearer ${globalToken}`
  }

  const config: RequestInit = {
    headers,
    ...options,
  }

  try {
    const response = await fetch(url, config)

    if (!response.ok) {
      const errorText = await response.text()
      throw new ApiError(response.status, errorText || `HTTP error! status: ${response.status}`)
    }

    // DELETE işlemleri için wrapper response kontrolü
    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return null as T
    }

    // Wrapper response parse et
    const result = await response.json()

    if (!result.success) {
      throw new ApiError(response.status, result.message || 'API request failed')
    }

    return result.data as T
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(500, 'Network error')
  }
}

export const api = {
  // Auth endpoints - Gateway API
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiRequest('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      }),
    register: (userData: { email: string; password: string; firstName?: string; lastName?: string }) =>
      apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      }),
    me: () =>
      apiRequest('/api/auth/me', {
        method: 'GET',
      }),
  },

  // User Management endpoints - Gateway proxy
  users: {
    getAll: () => apiRequest('/api/users'),
    getById: (id: string) => apiRequest(`/api/users/${id}`),
    create: (userData: any) => apiRequest('/api/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    }),
    update: (id: string, userData: any) => apiRequest(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    }),
    delete: (id: string) => apiRequest(`/api/users/${id}`, {
      method: 'DELETE',
    }),
  },

  // Permission Management endpoints - Gateway proxy (with wrapper response)
  permissions: {
    getAll: () => apiRequestWithWrapper('/api/permissions'),
    create: (permissionData: any) => apiRequestWithWrapper('/api/permissions', {
      method: 'POST',
      body: JSON.stringify(permissionData),
    }),
    delete: (id: string) => apiRequestWithWrapper(`/api/permissions/${id}`, {
      method: 'DELETE',
    }),
  },

  // Server Management endpoints - Gateway proxy
  servers: {
    getAll: () => apiRequest('/api/servers'),
    getById: (id: string) => apiRequest(`/api/servers/${id}`),
    create: (serverData: any) => apiRequest('/api/servers', {
      method: 'POST',
      body: JSON.stringify(serverData),
    }),
    update: (id: string, serverData: any) => apiRequest(`/api/servers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(serverData),
    }),
    delete: (id: string) => apiRequest(`/api/servers/${id}`, {
      method: 'DELETE',
    }),
    test: (id: string) => apiRequest(`/api/servers/${id}/test-connection`, {
      method: 'POST',
    }),
    testConnection: (serverData: any) => apiRequest('/api/servers/test-connection', {
      method: 'POST',
      body: JSON.stringify(serverData),
    }),
  },

  // Metrics endpoints - Gateway proxy
  metrics: {
    getAll: () => apiRequest<MetricQuery[]>('/api/metrics'),
    getById: (id: string) => apiRequest<MetricQuery>(`/api/metrics/${id}`),
    create: (metricData: CreateMetricRequest) => apiRequest<MetricQuery>('/api/metrics', {
      method: 'POST',
      body: JSON.stringify(metricData),
    }),
    update: (id: string, metricData: UpdateMetricRequest) => apiRequest<MetricQuery>(`/api/metrics/${id}`, {
      method: 'PUT',
      body: JSON.stringify(metricData),
    }),
    delete: (id: string) => apiRequest<void>(`/api/metrics/${id}`, {
      method: 'DELETE',
    }),
    toggleStatus: (id: string) => apiRequest<MetricQuery>(`/api/metrics/${id}/status`, {
      method: 'PATCH',
    }),
    test: (testData: MetricTestRequest) => apiRequest<MetricTestResult>('/api/metric-test/query', {
      method: 'POST',
      body: JSON.stringify(testData),
    }),

    // Component System APIs
    getAllComponentTypes: () => apiRequest<ComponentType[]>('/api/metric-queries/component-types'),
    getCompatibleComponentTypes: (dataType: string) =>
      apiRequest<ComponentType[]>(`/api/metric-queries/component-types/compatible?dataType=${dataType}`),
    getComponentRecommendations: (metricQueryId: string) =>
      apiRequest<ComponentRecommendation[]>(`/api/metric-queries/component-recommendations?metricQueryId=${metricQueryId}`),
    getMetricDisplayFields: (metricQueryId: string) =>
      apiRequest<MetricDisplayField[]>(`/api/metric-queries/${metricQueryId}/display-fields`),
    updateDisplayFieldComponent: (metricQueryId: string, columnName: string, componentData: {
      componentType: ComponentType;
      componentConfig?: Record<string, any>;
    }) => apiRequest<MetricDisplayField>(`/api/metric-queries/${metricQueryId}/display-fields/${columnName}/component`, {
      method: 'PUT',
      body: JSON.stringify(componentData),
    }),
  },



  // Metric Results API
  metricResults: {
    getAll: (params?: { serverId?: string; metricId?: string; startDate?: string; endDate?: string; page?: number; size?: number }) => {
      const searchParams = new URLSearchParams();
      if (params?.serverId) searchParams.append('serverId', params.serverId);
      if (params?.metricId) searchParams.append('metricId', params.metricId);
      if (params?.startDate) searchParams.append('startDate', params.startDate);
      if (params?.endDate) searchParams.append('endDate', params.endDate);
      if (params?.page !== undefined) searchParams.append('page', params.page.toString());
      if (params?.size !== undefined) searchParams.append('size', params.size.toString());

      const queryString = searchParams.toString();
      return apiRequestWithWrapper(`/api/metric-results${queryString ? `?${queryString}` : ''}`);
    },
    getById: (id: string) => apiRequestWithWrapper(`/api/metric-results/${id}`),
    getByServer: (serverId: string, params?: { startDate?: string; endDate?: string; page?: number; size?: number }) => {
      const searchParams = new URLSearchParams();
      if (params?.startDate) searchParams.append('startDate', params.startDate);
      if (params?.endDate) searchParams.append('endDate', params.endDate);
      if (params?.page !== undefined) searchParams.append('page', params.page.toString());
      if (params?.size !== undefined) searchParams.append('size', params.size.toString());

      const queryString = searchParams.toString();
      return apiRequestWithWrapper(`/api/metric-results/server/${serverId}${queryString ? `?${queryString}` : ''}`);
    },
    getLatestByServer: (serverId: string) => apiRequestWithWrapper(`/api/metric-results/server/${serverId}/latest`),
    getByMetric: (metricId: string, params?: { startDate?: string; endDate?: string; page?: number; size?: number }) => {
      const searchParams = new URLSearchParams();
      if (params?.startDate) searchParams.append('startDate', params.startDate);
      if (params?.endDate) searchParams.append('endDate', params.endDate);
      if (params?.page !== undefined) searchParams.append('page', params.page.toString());
      if (params?.size !== undefined) searchParams.append('size', params.size.toString());

      const queryString = searchParams.toString();
      return apiRequestWithWrapper(`/api/metric-results/metric/${metricId}${queryString ? `?${queryString}` : ''}`);
    },
    getStats: () => apiRequestWithWrapper('/api/metric-results/stats'),
    getStatsByServer: (serverId: string) => apiRequestWithWrapper(`/api/metric-results/server/${serverId}/stats`),
    getRecent: (params?: { sinceMs?: number; page?: number; size?: number }) => {
      const searchParams = new URLSearchParams();
      if (params?.sinceMs !== undefined) searchParams.append('sinceMs', params.sinceMs.toString());
      if (params?.page !== undefined) searchParams.append('page', params.page.toString());
      if (params?.size !== undefined) searchParams.append('size', params.size.toString());

      const queryString = searchParams.toString();
      return apiRequestWithWrapper(`/api/metric-results/recent${queryString ? `?${queryString}` : ''}`);
    },
    getByTimeRange: (serverId: string, startTime: number, endTime: number) => {
      const params = new URLSearchParams({ startTime: startTime.toString(), endTime: endTime.toString() });
      return apiRequestWithWrapper(`/api/metric-results/server/${serverId}/time-range?${params.toString()}`);
    },
    getByType: (serverId: string, metricType: string, params?: { startTime?: number; endTime?: number }) => {
      const searchParams = new URLSearchParams({ metricType });
      if (params?.startTime !== undefined) searchParams.append('startTime', params.startTime.toString());
      if (params?.endTime !== undefined) searchParams.append('endTime', params.endTime.toString());

      return apiRequestWithWrapper(`/api/metric-results/server/${serverId}/by-type?${searchParams.toString()}`);
    },
    delete: (id: string) => apiRequestWithWrapper(`/api/metric-results/${id}`, {
      method: 'DELETE',
    }),
  },

  // User Permissions endpoints - Gateway proxy
  userPermissions: {
    getAll: () => apiRequest('/api/user-permissions'),
    getGrouped: () => apiRequest('/api/user-permissions/grouped'),
    getById: (id: string) => apiRequest(`/api/user-permissions/${id}`),
    create: (permissionData: any) => apiRequest('/api/user-permissions', {
      method: 'POST',
      body: JSON.stringify(permissionData),
    }),
    delete: (id: string) => apiRequest(`/api/user-permissions/${id}`, {
      method: 'DELETE',
    }),
    getDatabaseRoles: (serverId: string) => apiRequest(`/api/user-permissions/database-roles/${serverId}`),
    getByUser: (userId: string) => apiRequest(`/api/user-permissions/user/${userId}`),
    getByServer: (serverId: string) => apiRequest(`/api/user-permissions/server/${serverId}`),
    // Yetki uygulama ve iptal etme
    apply: (permissionId: string) => apiRequest(`/api/user-permissions/${permissionId}/apply`, {
      method: 'POST',
    }),
    revoke: (permissionId: string) => apiRequest(`/api/user-permissions/${permissionId}/revoke`, {
      method: 'POST',
    }),
    // Kullanıcının bir sunucudaki tüm yetkilerini uygula/iptal et
    applyUserServerPermissions: (userId: string, serverId: string) => apiRequest(`/api/user-permissions/user/${userId}/server/${serverId}/apply`, {
      method: 'POST',
    }),
    revokeUserServerPermissions: (userId: string, serverId: string) => apiRequest(`/api/user-permissions/user/${userId}/server/${serverId}/revoke`, {
      method: 'POST',
    }),
    // Yetki geçmişi
    getHistory: () => apiRequest('/api/user-permissions/history'),
    getGroupedHistory: () => apiRequest('/api/user-permissions/history/grouped'),
    getHistoryByUserAndServer: (userId: string, serverId: string) =>
      apiRequest(`/api/user-permissions/history/user/${userId}/server/${serverId}`),
  },
}
