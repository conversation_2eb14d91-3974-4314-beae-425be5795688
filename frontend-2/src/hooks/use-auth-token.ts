"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import { setAuthToken } from "@/lib/api"

export function useAuthToken() {
  const { data: session, status } = useSession()
  const [isTokenReady, setIsTokenReady] = useState(false)

  useEffect(() => {
    if (status === "authenticated" && session) {
      const token = (session as any).accessToken
      if (token) {
        setAuthToken(token)
        setIsTokenReady(true)
      }
    } else if (status === "unauthenticated") {
      setAuthToken(null)
      setIsTokenReady(true) // Token yok ama ready
    } else if (status === "loading") {
      setIsTokenReady(false) // Henüz yükleniyor
    }
  }, [session, status])

  return {
    session,
    status,
    isTokenReady, // Token hazır mı?
    isAuthenticated: status === "authenticated" && !!session
  }
}
