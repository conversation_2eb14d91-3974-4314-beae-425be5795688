"use client"

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Users, Database, Activity, TrendingUp } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface StatsData {
  totalUsers: number
  totalDatabases: number
  activeConnections: number
  successRate: number
}

interface StatsCardsProps {
  data?: StatsData
  isLoading: boolean
}

export function StatsCards({ data, isLoading }: StatsCardsProps) {
  const stats = [
    {
      title: "Toplam Kullanıcı",
      value: data?.totalUsers || 0,
      icon: Users,
      description: "<PERSON>ıtlı kullanıcı sayısı",
      color: "text-blue-600",
    },
    {
      title: "Veritabanları",
      value: data?.totalDatabases || 0,
      icon: Database,
      description: "Bağlı veritabanı sayısı",
      color: "text-green-600",
    },
    {
      title: "Aktif Bağlantı",
      value: data?.activeConnections || 0,
      icon: Activity,
      description: "<PERSON>u anda aktif bağlantılar",
      color: "text-orange-600",
    },
    {
      title: "Başarı Oranı",
      value: `${data?.successRate || 0}%`,
      icon: TrendingUp,
      description: "Bağlantı başarı oranı",
      color: "text-purple-600",
    },
  ]

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
