"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON>ontainer } from "recharts"

interface ChartData {
  name: string
  connections: number
  success: number
}

interface ChartSectionProps {
  data?: ChartData[]
  isLoading: boolean
}

// Örnek veri
const mockData: ChartData[] = [
  { name: "Pzt", connections: 120, success: 115 },
  { name: "<PERSON>", connections: 150, success: 142 },
  { name: "<PERSON><PERSON>", connections: 180, success: 175 },
  { name: "Per", connections: 200, success: 190 },
  { name: "C<PERSON>", connections: 170, success: 165 },
  { name: "Cmt", connections: 90, success: 88 },
  { name: "<PERSON>", connections: 60, success: 58 },
]

export function ChartSection({ data, isLoading }: ChartSectionProps) {
  const chartData = data || mockData

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-80 w-full" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bağlantı İstatistikleri</CardTitle>
        <CardDescription>
          Son 7 günün bağlantı ve başarı oranları
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="connections" 
              stroke="#3b82f6" 
              strokeWidth={2}
              name="Toplam Bağlantı"
            />
            <Line 
              type="monotone" 
              dataKey="success" 
              stroke="#10b981" 
              strokeWidth={2}
              name="Başarılı Bağlantı"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
