"use client"

import { Badge } from "@/components/ui/badge"

interface Activity {
  id: string
  user: string
  action: string
  timestamp: string
  status: "success" | "error" | "warning"
}

// Örnek veri
const mockActivities: Activity[] = [
  {
    id: "1",
    user: "<EMAIL>",
    action: "Veritabanı bağlantısı",
    timestamp: "2 dakika önce",
    status: "success",
  },
  {
    id: "2",
    user: "<EMAIL>",
    action: "Giriş denemesi",
    timestamp: "5 dakika önce",
    status: "error",
  },
  {
    id: "3",
    user: "<EMAIL>",
    action: "<PERSON><PERSON><PERSON>",
    timestamp: "10 dakika önce",
    status: "success",
  },
  {
    id: "4",
    user: "<EMAIL>",
    action: "Yetki güncelleme",
    timestamp: "15 dakika önce",
    status: "warning",
  },
]

const statusColors = {
  success: "bg-green-100 text-green-800",
  error: "bg-red-100 text-red-800",
  warning: "bg-yellow-100 text-yellow-800",
}

const statusLabels = {
  success: "Başarılı",
  error: "Hata",
  warning: "Uyarı",
}

export function DataTable() {
  return (
    <div className="space-y-4">
      {mockActivities.map((activity) => (
        <div
          key={activity.id}
          className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium">{activity.action}</p>
              <Badge 
                variant="secondary" 
                className={statusColors[activity.status]}
              >
                {statusLabels[activity.status]}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {activity.user}
            </p>
          </div>
          <div className="text-sm text-muted-foreground">
            {activity.timestamp}
          </div>
        </div>
      ))}
    </div>
  )
}
