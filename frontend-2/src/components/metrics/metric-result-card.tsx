"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Hash, 
  CheckCircle, 
  Activity, 
  TrendingUp, 
  Line<PERSON>hart, 
  BarChart3, 
  <PERSON><PERSON>hart, 
  Table,
  AlertCircle,
  Clock,
  Database
} from "lucide-react"
import { MetricDisplayField, ComponentType } from "@/lib/api"

interface MetricResult {
  id: string;
  metricQueryId: string;
  serverId: string;
  recordedAt: number;
  executionTimeMs: number;
  success: boolean;
  errorMessage?: string;
  resultData: {
    rows: Record<string, any>[];
    rowCount: number;
    timestamp: number;
  };
}

interface MetricResultCardProps {
  displayField: MetricDisplayField;
  results: MetricResult[];
  isLoading?: boolean;
}

// Component type icons mapping
const componentTypeIcons: Record<ComponentType, React.ComponentType<any>> = {
  NUMBER_CARD: Hash,
  STATUS_CARD: CheckCircle,
  PROGRESS_BAR: Activity,
  BADGE: Hash,
  ICON_NUMBER: Hash,
  TREND_CARD: TrendingUp,
  LINE_CHART: LineChart,
  BAR_CHART: BarChart3,
  AREA_CHART: BarChart3,
  TABLE: Table,
  STATISTICS_CARD: BarChart3,
  COMPARISON_CARD: PieChart
}

// Demo component renderer
function renderDemoComponent(componentType: ComponentType, value: number, status: boolean, percentage: number) {
  switch (componentType) {
    case 'NUMBER_CARD':
      return (
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {value.toLocaleString()}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )

    case 'STATUS_CARD':
      return (
        <div className="space-y-2">
          <Badge variant={status ? "default" : "secondary"} className="gap-1">
            <CheckCircle className="h-3 w-3" />
            {status ? 'Aktif' : 'Pasif'}
          </Badge>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )

    case 'PROGRESS_BAR':
      return (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Kullanım</span>
            <span>{percentage}%</span>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${percentage}%` }}
            />
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )

    case 'BADGE':
      return (
        <div className="space-y-2">
          <Badge variant="outline">
            {value}
          </Badge>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )

    case 'TREND_CARD':
      const trend = Math.random() > 0.5 ? value * 0.1 : -value * 0.1
      return (
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {value.toLocaleString()}
          </div>
          <div className={`flex items-center gap-1 text-sm ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className={`h-3 w-3 ${trend < 0 ? 'rotate-180' : ''}`} />
            {Math.abs(trend).toFixed(1)}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )

    default:
      return (
        <div className="space-y-2">
          <div className="text-lg font-semibold">
            {value.toLocaleString()}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date().toLocaleTimeString('tr-TR')}
          </div>
        </div>
      )
  }
}

// Real data component renderer
function renderRealComponent(componentType: ComponentType, value: any, result: MetricResult) {
  const timestamp = new Date(result.recordedAt).toLocaleTimeString('tr-TR')

  switch (componentType) {
    case 'NUMBER_CARD':
      return (
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {typeof value === 'number' ? value.toLocaleString() : value || 'N/A'}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )

    case 'STATUS_CARD':
      const isActive = value === 'active' || value === true || value === 1 || value > 0
      return (
        <div className="space-y-2">
          <Badge variant={isActive ? "default" : "secondary"} className="gap-1">
            <CheckCircle className="h-3 w-3" />
            {isActive ? 'Aktif' : 'Pasif'}
          </Badge>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )

    case 'PROGRESS_BAR':
      const percentage = Math.min(100, Math.max(0, Number(value) || 0))
      return (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Değer</span>
            <span>{percentage}%</span>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${percentage}%` }}
            />
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )

    case 'BADGE':
      return (
        <div className="space-y-2">
          <Badge variant="outline">
            {value || 'N/A'}
          </Badge>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )

    case 'TREND_CARD':
      return (
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {typeof value === 'number' ? value.toLocaleString() : value || 'N/A'}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )

    default:
      return (
        <div className="space-y-2">
          <div className="text-lg font-semibold">
            {typeof value === 'number' ? value.toLocaleString() : value || 'N/A'}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {timestamp}
          </div>
        </div>
      )
  }
}

export function MetricResultCard({ displayField, results, isLoading }: MetricResultCardProps) {
  const IconComponent = componentTypeIcons[displayField.componentType] || Hash

  // Debug: Log the results to see what we're getting
  console.log('MetricResultCard - displayField:', displayField.displayName)
  console.log('MetricResultCard - results:', results)
  console.log('MetricResultCard - isLoading:', isLoading)

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <IconComponent className="h-4 w-4" />
            {displayField.displayName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-16" />
        </CardContent>
      </Card>
    )
  }

  // Generate demo data
  const demoValue = Math.floor(Math.random() * 1000) + 1
  const demoStatus = Math.random() > 0.5
  const demoPercentage = Math.floor(Math.random() * 100)

  if (!results || results.length === 0) {
    // Show demo data instead of "no data"
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <IconComponent className="h-4 w-4" />
            {displayField.displayName}
          </CardTitle>
          <CardDescription className="text-xs">
            Demo Veri
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderDemoComponent(displayField.componentType, demoValue, demoStatus, demoPercentage)}
          <div className="mt-3 pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              Demo
            </div>
            <Badge variant="outline" className="text-xs">
              {displayField.componentType.replace('_', ' ')}
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Check if we have real data
  const latestResult = results[0]

  if (latestResult && latestResult.success && latestResult.resultData?.rows?.length > 0) {
    const latestData = latestResult.resultData.rows[0]
    const value = latestData?.[displayField.columnName]

    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <IconComponent className="h-4 w-4" />
            {displayField.displayName}
          </CardTitle>
          <CardDescription className="text-xs">
            Canlı Veri
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderRealComponent(displayField.componentType, value, latestResult)}

          <div className="mt-3 pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              {latestResult.executionTimeMs}ms
            </div>
            <Badge variant="outline" className="text-xs">
              {displayField.componentType.replace('_', ' ')}
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Fallback to demo data
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <IconComponent className="h-4 w-4" />
          {displayField.displayName}
        </CardTitle>
        <CardDescription className="text-xs">
          Demo Veri (Fallback)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderDemoComponent(displayField.componentType, demoValue, demoStatus, demoPercentage)}
        <div className="mt-3 pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3" />
            Demo
          </div>
          <Badge variant="outline" className="text-xs">
            {displayField.componentType.replace('_', ' ')}
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}
