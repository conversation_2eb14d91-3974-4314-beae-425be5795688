"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  Bar<PERSON><PERSON>3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Hash,
  CheckCircle,
  Activity,
  Table,
  ChevronDown,
  Palette,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'
import { motion } from 'framer-motion'
import { toast } from 'sonner'
import {
  ComponentType,
  MetricDisplayField,
  MetricQuery,
  api
} from '@/lib/api'

// Component type icons and info
const componentTypeInfo: Record<ComponentType, {
  icon: React.ComponentType<any>;
  name: string;
  description: string;
  color: string;
  requiresTimeRange: boolean;
}> = {
  NUMBER_CARD: {
    icon: Hash,
    name: '<PERSON><PERSON>',
    description: '<PERSON>üyük sayı gösterimi',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    requiresTimeRange: false
  },
  STATUS_CARD: {
    icon: CheckCircle,
    name: 'Durum Kartı',
    description: 'Renkli durum gösterimi',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    requiresTimeRange: false
  },
  PROGRESS_BAR: {
    icon: Activity,
    name: 'İlerleme Çubuğu',
    description: 'Yüzde gösterimi',
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    requiresTimeRange: false
  },
  BADGE: {
    icon: Hash,
    name: 'Rozet',
    description: 'Küçük sayı rozeti',
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    requiresTimeRange: false
  },
  ICON_NUMBER: {
    icon: Hash,
    name: 'İkonlu Sayı',
    description: 'İkonlu gösterim',
    color: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
    requiresTimeRange: false
  },
  TREND_CARD: {
    icon: TrendingUp,
    name: 'Trend Kartı',
    description: 'Sayı + mini grafik',
    color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    requiresTimeRange: true
  },
  LINE_CHART: {
    icon: LineChart,
    name: 'Çizgi Grafik',
    description: 'Zaman serisi grafiği',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    requiresTimeRange: true
  },
  BAR_CHART: {
    icon: BarChart3,
    name: 'Sütun Grafik',
    description: 'Kategorik veri grafiği',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    requiresTimeRange: true
  },
  AREA_CHART: {
    icon: BarChart3,
    name: 'Alan Grafik',
    description: 'Dolu alan grafiği',
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    requiresTimeRange: true
  },
  TABLE: {
    icon: Table,
    name: 'Tablo',
    description: 'Detaylı tablo görünümü',
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    requiresTimeRange: false
  },
  STATISTICS_CARD: {
    icon: BarChart3,
    name: 'İstatistik Kartı',
    description: 'Özet istatistikler',
    color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    requiresTimeRange: true
  },
  COMPARISON_CARD: {
    icon: PieChart,
    name: 'Karşılaştırma Kartı',
    description: 'Değer karşılaştırması',
    color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    requiresTimeRange: true
  }
}

interface MetricComponentSelectorProps {
  metricQuery: MetricQuery;
  onUpdate?: () => void;
}

export default function MetricComponentSelector({ metricQuery, onUpdate }: MetricComponentSelectorProps) {
  const [displayFields, setDisplayFields] = useState<MetricDisplayField[]>([])
  const [allComponentTypes, setAllComponentTypes] = useState<ComponentType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadData()
  }, [metricQuery.id])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [componentTypes, fields] = await Promise.all([
        api.metrics.getAllComponentTypes(),
        // For now, use displayFields from metricQuery if available
        Promise.resolve(metricQuery.displayFields || [])
      ])
      
      setAllComponentTypes(componentTypes)
      setDisplayFields(fields)
    } catch (error) {
      console.error('Error loading component data:', error)
      toast.error('Bileşen verileri yüklenirken hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  const handleComponentChange = async (field: MetricDisplayField, newComponentType: ComponentType) => {
    try {
      const updatedField = await api.metrics.updateDisplayFieldComponent(
        metricQuery.id,
        field.columnName,
        {
          componentType: newComponentType,
          componentConfig: getDefaultConfig(newComponentType)
        }
      )

      // Update local state
      setDisplayFields(prev => 
        prev.map(f => f.id === field.id ? updatedField : f)
      )

      toast.success('Bileşen türü güncellendi')
      onUpdate?.()
    } catch (error) {
      console.error('Error updating component:', error)
      toast.error('Bileşen güncellenirken hata oluştu')
    }
  }

  const getDefaultConfig = (componentType: ComponentType): Record<string, any> => {
    const baseConfig = {
      color: 'blue',
      size: 'medium',
      showIcon: true
    }

    switch (componentType) {
      case 'PROGRESS_BAR':
        return { ...baseConfig, showPercentage: true, maxValue: 100 }
      case 'TREND_CARD':
        return { ...baseConfig, showTrend: true, trendPeriod: '24h' }
      case 'LINE_CHART':
      case 'BAR_CHART':
      case 'AREA_CHART':
        return { ...baseConfig, height: 300, showGrid: true, showLegend: true }
      case 'TABLE':
        return { ...baseConfig, pageSize: 10, sortable: true }
      default:
        return baseConfig
    }
  }

  const toggleFieldExpansion = (fieldId: string) => {
    setExpandedFields(prev => {
      const newSet = new Set(prev)
      if (newSet.has(fieldId)) {
        newSet.delete(fieldId)
      } else {
        newSet.add(fieldId)
      }
      return newSet
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  if (displayFields.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Bileşen Seçimi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Bu metrik için henüz görüntüleme alanı tanımlanmamış.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Metrik oluşturulduktan sonra otomatik olarak bileşenler atanacaktır.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Bileşen Seçimi
          <Badge variant="secondary">{displayFields.length} alan</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {displayFields.map((field, index) => {
          const isExpanded = expandedFields.has(field.id)
          const componentInfo = componentTypeInfo[field.componentType]
          const IconComponent = componentInfo?.icon || Hash

          return (
            <motion.div
              key={field.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Collapsible open={isExpanded} onOpenChange={() => toggleFieldExpansion(field.id)}>
                <div className="border rounded-lg">
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-4 hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <IconComponent className="w-5 h-5 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">{field.displayName}</h4>
                          <p className="text-sm text-muted-foreground">{field.columnName} ({field.dataType})</p>
                        </div>
                        <Badge className={componentInfo?.color || 'bg-gray-100 text-gray-800'}>
                          {componentInfo?.name || field.componentType}
                        </Badge>
                        {!field.componentCompatibleWithDataType && (
                          <Badge variant="destructive">Uyumsuz</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {field.isActive ? (
                          <Eye className="w-4 h-4 text-green-600" />
                        ) : (
                          <EyeOff className="w-4 h-4 text-muted-foreground" />
                        )}
                        <ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="p-4 pt-0 border-t bg-muted/20">
                    <div className="space-y-4">
                      {/* Component Type Selection */}
                      <div className="grid gap-2">
                        <Label>Bileşen Türü</Label>
                        <ComponentTypeSelector
                          currentType={field.componentType}
                          dataType={field.dataType}
                          onSelect={(newType) => handleComponentChange(field, newType)}
                          allTypes={allComponentTypes}
                        />
                      </div>

                      {/* Component Configuration */}
                      <div className="grid gap-2">
                        <Label>Bileşen Konfigürasyonu</Label>
                        <div className="bg-background rounded border p-3">
                          <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                            {field.componentConfigAsJson || JSON.stringify(field.componentConfig, null, 2)}
                          </pre>
                        </div>
                      </div>

                      {/* Field Info */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="text-xs text-muted-foreground">Metrik Tipi</Label>
                          <p>{field.metricType}</p>
                        </div>
                        <div>
                          <Label className="text-xs text-muted-foreground">Görüntüleme Sırası</Label>
                          <p>{field.displayOrder}</p>
                        </div>
                      </div>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            </motion.div>
          )
        })}
      </CardContent>
    </Card>
  )
}

// Component Type Selector Sub-component
interface ComponentTypeSelectorProps {
  currentType: ComponentType;
  dataType: string;
  onSelect: (type: ComponentType) => void;
  allTypes: ComponentType[];
}

function ComponentTypeSelector({ currentType, dataType, onSelect, allTypes }: ComponentTypeSelectorProps) {
  const [compatibleTypes, setCompatibleTypes] = useState<ComponentType[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    loadCompatibleTypes()
  }, [dataType])

  const loadCompatibleTypes = async () => {
    try {
      setIsLoading(true)
      const compatible = await api.metrics.getCompatibleComponentTypes(dataType)
      setCompatibleTypes(compatible)
    } catch (error) {
      console.error('Error loading compatible types:', error)
      setCompatibleTypes(allTypes)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Select value={currentType} onValueChange={onSelect}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {allTypes.map((type) => {
          const info = componentTypeInfo[type]
          const isCompatible = compatibleTypes.includes(type)
          const IconComponent = info?.icon || Hash

          return (
            <SelectItem 
              key={type} 
              value={type}
              disabled={!isCompatible}
            >
              <div className="flex items-center gap-2">
                <IconComponent className="w-4 h-4" />
                <span>{info?.name || type}</span>
                {!isCompatible && (
                  <Badge variant="secondary" className="text-xs">Uyumsuz</Badge>
                )}
              </div>
            </SelectItem>
          )
        })}
      </SelectContent>
    </Select>
  )
}
