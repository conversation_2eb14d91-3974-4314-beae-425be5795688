"use client"

import { SessionProvider } from "next-auth/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ThemeProvider } from "next-themes"
import { Toaster } from "@/components/ui/sonner"
import { useState } from "react"
import { useAuthToken } from "@/hooks/use-auth-token"

// Auth token manager component
function AuthTokenManager({ children }: { children: React.ReactNode }) {
  useAuthToken() // This will automatically set the token when session changes
  return <>{children}</>
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 dakika - daha uzun cache süresi
        cacheTime: 10 * 60 * 1000, // 10 dakika - cache'de daha uzun tutma
        retry: 1,
        refetchOnWindowFocus: false, // Pencere odaklandığında tekrar fetch etme
        refetchOnMount: false, // Mount'ta tekrar fetch etme (cache varsa)
        refetchOnReconnect: false, // Bağlantı yenilendiğinde tekrar fetch etme
      },
    },
  }))

  return (
    <SessionProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthTokenManager>
            {children}
          </AuthTokenManager>
          <Toaster />
        </ThemeProvider>
      </QueryClientProvider>
    </SessionProvider>
  )
}
