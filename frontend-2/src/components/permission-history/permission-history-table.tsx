"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Loader2, Clock, User, Database, Shield, CheckCircle, XCircle, AlertTriangle, Eye } from "lucide-react"
import { api } from "@/lib/api"
import { useRouter } from "next/navigation"

interface GroupedPermissionHistory {
  id: string
  userId: string
  firstName: string
  lastName: string
  email: string
  serverId: string
  serverName: string
  databaseType: string
  roleNames: string
  firstGrantedAt: string
  lastActionAt: string
  totalPermissions: number
  activePermissions: number
  revokedPermissions: number
  expiredPermissions: number
  lastAction: string
  createdAt: string
  updatedAt: string
}

interface PermissionHistoryTableProps {
  refreshTrigger: number
}

export function PermissionHistoryTable({ refreshTrigger }: PermissionHistoryTableProps) {
  const router = useRouter()

  const {
    data: permissionHistory = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["permission-history-grouped", refreshTrigger],
    queryFn: () => api.userPermissions.getGroupedHistory(),
    staleTime: 0, // Veriyi hemen stale yap
    gcTime: 0, // Cache'i hemen temizle
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusBadge = (history: GroupedPermissionHistory) => {
    if (history.activePermissions > 0) {
      return (
        <Badge variant="default" className="gap-1 bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="h-3 w-3" />
          {history.activePermissions} Aktif
        </Badge>
      )
    } else if (history.revokedPermissions > 0) {
      return (
        <Badge variant="destructive" className="gap-1">
          <XCircle className="h-3 w-3" />
          İptal Edildi
        </Badge>
      )
    } else if (history.expiredPermissions > 0) {
      return (
        <Badge variant="secondary" className="gap-1 bg-orange-100 text-orange-800 border-orange-200">
          <AlertTriangle className="h-3 w-3" />
          Süresi Doldu
        </Badge>
      )
    }
    return (
      <Badge variant="outline">
        Bilinmeyen
      </Badge>
    )
  }

  const handleViewDetails = (history: GroupedPermissionHistory) => {
    router.push(`/permission-history/${history.userId}/${history.serverId}`)
  }

  const getDatabaseTypeBadge = (databaseType: string) => {
    const colors = {
      'postgresql': 'bg-blue-100 text-blue-800 border-blue-200',
      'mysql': 'bg-orange-100 text-orange-800 border-orange-200',
      'oracle': 'bg-red-100 text-red-800 border-red-200',
      'sqlserver': 'bg-purple-100 text-purple-800 border-purple-200',
    }
    
    const colorClass = colors[databaseType.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
    
    return (
      <Badge variant="outline" className={`gap-1 ${colorClass}`}>
        <Database className="h-3 w-3" />
        {databaseType.toUpperCase()}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Yetki geçmişi yükleniyor...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Yetki geçmişi yüklenirken bir hata oluştu
      </div>
    )
  }

  if (permissionHistory.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Henüz yetki geçmişi bulunmuyor
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {permissionHistory.map((history: GroupedPermissionHistory) => (
        <Card key={`${history.userId}-${history.serverId}`} className="relative">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    {history.firstName} {history.lastName}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {history.email}
                  </CardDescription>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                {getStatusBadge(history)}
                {history.serverName && getDatabaseTypeBadge(history.databaseType)}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Sunucu Bilgileri */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Sunucu:</span>
                <span className="text-sm text-muted-foreground">
                  {history.serverName || 'Bilinmeyen Sunucu'}
                </span>
              </div>
            </div>

            {/* Yetki Özeti */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Yetkiler:</span>
                <span className="text-sm text-muted-foreground">
                  {history.totalPermissions} toplam
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {history.roleNames.split(', ').slice(0, 3).map((roleName, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {roleName}
                  </Badge>
                ))}
                {history.roleNames.split(', ').length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{history.roleNames.split(', ').length - 3} daha
                  </Badge>
                )}
              </div>
            </div>

            {/* Tarih Bilgileri */}
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">İlk Yetki:</span>
                <span className="text-muted-foreground">
                  {formatDate(history.firstGrantedAt)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Son İşlem:</span>
                <span className="text-muted-foreground">
                  {formatDate(history.lastActionAt)}
                </span>
              </div>
            </div>

            {/* Detay Butonu */}
            <div className="flex justify-end pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewDetails(history)}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                Detayları Gör
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
