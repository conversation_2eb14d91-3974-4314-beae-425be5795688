"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Edit, Trash2, Shield, User, Database } from "lucide-react"
import { api } from "@/lib/api"

interface GroupedUserPermission {
  id: string
  userId: string
  firstName: string
  lastName: string
  email: string
  serverId: string
  roleNames: string
  grantedAt: string
  expiresAt: string
  hasActivePermissions: boolean
}

interface UserPermission {
  id: string
  userId: string
  userName: string
  userEmail: string
  serverId: string
  serverName: string
  databaseType: string
  roleName: string
  roleDescription: string
  grantedAt: number
  expiresAt: number
  isActive: boolean
}

interface EditUserPermissionsDialogProps {
  groupedPermission: GroupedUserPermission | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onPermissionsUpdated: () => void
}

export function EditUserPermissionsDialog({
  groupedPermission,
  open,
  onOpenChange,
  onPermissionsUpdated,
}: EditUserPermissionsDialogProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [isDeleting, setIsDeleting] = useState(false)
  const queryClient = useQueryClient()

  // Kullanıcının bu sunucudaki tüm yetkilerini getir
  const { data: userPermissions = [], isLoading } = useQuery({
    queryKey: ["user-permissions-detail", groupedPermission?.userId, groupedPermission?.serverId],
    queryFn: () => {
      if (!groupedPermission) return []
      return api.userPermissions.getAll().then((permissions: UserPermission[]) =>
        permissions.filter(
          (p) => p.userId === groupedPermission.userId && p.serverId === groupedPermission.serverId
        )
      )
    },
    enabled: !!groupedPermission && open,
  })

  // Dialog açıldığında seçili yetkileri sıfırla
  useEffect(() => {
    if (open) {
      setSelectedPermissions([])
    }
  }, [open])

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    )
  }

  const handleDeleteSelected = async () => {
    if (selectedPermissions.length === 0) {
      toast.error("Silinecek yetki seçin")
      return
    }

    setIsDeleting(true)
    try {
      // Seçili yetkileri sil
      await Promise.all(
        selectedPermissions.map((permissionId) =>
          api.userPermissions.delete(permissionId)
        )
      )

      toast.success(`${selectedPermissions.length} yetki başarıyla silindi`)
      setSelectedPermissions([])
      onPermissionsUpdated()
      
      // Eğer tüm yetkiler silindiyse dialog'u kapat
      if (selectedPermissions.length === userPermissions.length) {
        onOpenChange(false)
      }
    } catch (error: any) {
      console.error("Yetki silme hatası:", error)
      toast.error(error.message || "Yetkiler silinirken bir hata oluştu")
    } finally {
      setIsDeleting(false)
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const isExpired = (expiresAt: number) => {
    return new Date(expiresAt) < new Date()
  }

  if (!groupedPermission) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Kullanıcı Yetkilerini Düzenle
          </DialogTitle>
          <DialogDescription>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>
                  {groupedPermission.firstName} {groupedPermission.lastName} ({groupedPermission.email})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                <span>Sunucu ID: {groupedPermission.serverId}</span>
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Yetkiler yükleniyor...</span>
            </div>
          ) : userPermissions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Bu kullanıcının bu sunucuda yetkisi bulunmuyor
            </div>
          ) : (
            <>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Yetkiler ({userPermissions.length})
                  </h4>
                  <div className="text-sm text-muted-foreground">
                    {selectedPermissions.length} seçili
                  </div>
                </div>

                {userPermissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center space-x-3 p-3 border rounded-lg"
                  >
                    <Checkbox
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={() => handlePermissionToggle(permission.id)}
                    />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{permission.roleName}</span>
                        <Badge
                          variant={permission.isActive ? "default" : "secondary"}
                        >
                          {permission.isActive ? "Aktif" : "Pasif"}
                        </Badge>
                        {isExpired(permission.expiresAt) && (
                          <Badge variant="destructive">Süresi Dolmuş</Badge>
                        )}
                      </div>
                      {permission.roleDescription && (
                        <div className="text-sm text-muted-foreground">
                          {permission.roleDescription}
                        </div>
                      )}
                      <div className="text-xs text-muted-foreground">
                        Verildi: {formatDate(permission.grantedAt)} • 
                        Bitiş: {formatDate(permission.expiresAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-between pt-4 border-t">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  İptal
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteSelected}
                  disabled={selectedPermissions.length === 0 || isDeleting}
                  className="gap-2"
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  Seçili Yetkileri Sil ({selectedPermissions.length})
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
