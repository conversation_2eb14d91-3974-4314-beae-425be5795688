"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Edit, Clock, User, Database, Shield, CheckCircle, XCircle, Play, Square } from "lucide-react"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { EditUserPermissionsDialog } from "./edit-user-permissions-dialog"

interface GroupedUserPermission {
  id: string
  userId: string
  firstName: string
  lastName: string
  email: string
  serverId: string
  roleNames: string
  grantedAt: string
  expiresAt: string
  hasActivePermissions: boolean
  sqlQuery?: string // SQL sorgusu varsa yetki uygulanmış demektir
  isApplied?: boolean // Yetkilerin uygulanıp uygulanmadığını gösterir
}

interface UserPermissionsTableProps {
  refreshTrigger: number
}

export function UserPermissionsTable({ refreshTrigger }: UserPermissionsTableProps) {
  const [selectedPermission, setSelectedPermission] = useState<GroupedUserPermission | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const queryClient = useQueryClient()

  const {
    data: permissions = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["user-permissions-grouped", refreshTrigger],
    queryFn: () => api.userPermissions.getGrouped(),
    staleTime: 0, // Veriyi hemen stale yap
    gcTime: 0, // Cache'i hemen temizle
  })

  // Yetki uygulama mutation (çoklu)
  const applyPermissionsMutation = useMutation({
    mutationFn: ({ userId, serverId }: { userId: string, serverId: string }) =>
      api.userPermissions.applyUserServerPermissions(userId, serverId),
    onSuccess: () => {
      toast.success("Yetkiler başarıyla uygulandı")
      refetch()
    },
    onError: (error: any) => {
      toast.error(error?.message || "Yetkiler uygulanırken bir hata oluştu")
    },
  })

  // Yetki iptal etme mutation (çoklu)
  const revokePermissionsMutation = useMutation({
    mutationFn: ({ userId, serverId }: { userId: string, serverId: string }) =>
      api.userPermissions.revokeUserServerPermissions(userId, serverId),
    onSuccess: () => {
      toast.success("Yetkiler başarıyla iptal edildi")
      refetch()
    },
    onError: (error: any) => {
      toast.error(error?.message || "Yetkiler iptal edilirken bir hata oluştu")
    },
  })

  const handleEditPermissions = (permission: GroupedUserPermission) => {
    setSelectedPermission(permission)
    setEditDialogOpen(true)
  }

  const handlePermissionsUpdated = () => {
    refetch()
  }

  const handleApplyPermissions = (permission: GroupedUserPermission) => {
    applyPermissionsMutation.mutate({
      userId: permission.userId,
      serverId: permission.serverId
    })
  }

  const handleRevokePermissions = (permission: GroupedUserPermission) => {
    revokePermissionsMutation.mutate({
      userId: permission.userId,
      serverId: permission.serverId
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  const getStatusBadge = (permission: GroupedUserPermission) => {
    if (!permission.hasActivePermissions) {
      return <Badge variant="secondary">Pasif</Badge>
    }
    if (isExpired(permission.expiresAt)) {
      return <Badge variant="destructive">Süresi Dolmuş</Badge>
    }
    return <Badge variant="default">Aktif</Badge>
  }

  const getApplicationStatusBadge = (permission: GroupedUserPermission) => {
    // SQL query varsa ve yetki aktifse uygulanmış sayılır
    const isApplied = permission.hasActivePermissions && !isExpired(permission.expiresAt) && permission.sqlQuery

    if (isApplied) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="h-3 w-3 mr-1" />
          Uygulandı
        </Badge>
      )
    } else {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
          <XCircle className="h-3 w-3 mr-1" />
          Uygulanmadı
        </Badge>
      )
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Yükleniyor...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Yetki listesi yüklenirken bir hata oluştu
      </div>
    )
  }

  if (permissions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Henüz kullanıcı yetkisi bulunmuyor
      </div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {permissions.map((permission) => {
          const isExpiredPermission = isExpired(permission.expiresAt)
          const isActive = permission.hasActivePermissions && !isExpiredPermission
          // Yetki uygulanmış sayılır: aktif, süresi dolmamış ve SQL query'si var
          const isApplied = permission.hasActivePermissions && !isExpiredPermission && permission.sqlQuery

          return (
            <Card key={permission.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <User className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">
                        {permission.firstName} {permission.lastName}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {permission.email}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    {getStatusBadge(permission)}
                    {getApplicationStatusBadge(permission)}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Sunucu Bilgisi */}
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Sunucu:</span>
                  <span className="text-sm font-mono bg-muted px-2 py-1 rounded">
                    {permission.serverId}
                  </span>
                </div>

                {/* Yetkiler */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Yetkiler:</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {permission.roleNames.split(', ').map((roleName, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {roleName}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Tarih Bilgileri */}
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Veriliş:</span>
                    <span className="text-muted-foreground">
                      {formatDate(permission.grantedAt)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Bitiş:</span>
                    <span className={`${isExpiredPermission ? 'text-red-600' : 'text-muted-foreground'}`}>
                      {formatDate(permission.expiresAt)}
                    </span>
                  </div>
                </div>

                {/* İşlem Butonları */}
                <div className="flex flex-wrap gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditPermissions(permission)}
                    className="gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    Düzenle
                  </Button>

                  {!isExpiredPermission && (
                    <>
                      {isApplied ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRevokePermissions(permission)}
                          disabled={revokePermissionsMutation.isPending}
                          className="gap-2 text-red-600 border-red-200 hover:bg-red-50"
                        >
                          {revokePermissionsMutation.isPending ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Square className="h-4 w-4" />
                          )}
                          Yetkileri İptal Et
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApplyPermissions(permission)}
                          disabled={applyPermissionsMutation.isPending}
                          className="gap-2 text-green-600 border-green-200 hover:bg-green-50"
                        >
                          {applyPermissionsMutation.isPending ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                          Yetkileri Uygula
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

    <EditUserPermissionsDialog
      groupedPermission={selectedPermission}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onPermissionsUpdated={handlePermissionsUpdated}
    />
  </>
  )
}
