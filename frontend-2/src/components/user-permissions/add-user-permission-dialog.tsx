"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useQuery } from "@tanstack/react-query"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, Shield, Plus, Calendar as CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { api } from "@/lib/api"

const createUserPermissionSchema = z.object({
  serverId: z.string().min(1, "Sunucu seçimi zorunludur"),
  userId: z.string().min(1, "Kullanıcı seçimi zorunludur"),
  roleNames: z.array(z.string()).min(1, "En az bir yetki seçimi zorunludur"),
  durationDays: z.number().min(1, "Süre seçimi zorunludur"),
  customDate: z.date().optional(),
})

type CreateUserPermissionRequest = z.infer<typeof createUserPermissionSchema>

interface AddUserPermissionDialogProps {
  onPermissionAdded: () => void
}

interface Server {
  id: string
  name: string
  databaseType: string
  isActive: boolean
}

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  active: boolean
}

interface DatabaseRole {
  roleName: string
  description: string
  isSuper: boolean
  canCreateRole: boolean
  canCreateDb: boolean
  canLogin: boolean
  isReplication: boolean
  bypassRls: boolean
}

const DURATION_OPTIONS = [
  { label: "1 Gün", value: 1 },
  { label: "1 Hafta", value: 7 },
  { label: "1 Ay", value: 30 },
  { label: "1 Yıl", value: 365 },
]

export function AddUserPermissionDialog({ onPermissionAdded }: AddUserPermissionDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedServerId, setSelectedServerId] = useState<string>("")
  const [useCustomDate, setUseCustomDate] = useState(false)

  const form = useForm<CreateUserPermissionRequest>({
    resolver: zodResolver(createUserPermissionSchema),
    defaultValues: {
      serverId: "",
      userId: "",
      roleNames: [],
      durationDays: 7, // Varsayılan 1 hafta
      customDate: undefined,
    },
  })

  // Sunucuları getir
  const { data: servers = [], isLoading: serversLoading } = useQuery({
    queryKey: ["servers"],
    queryFn: () => api.servers.getAll(),
  })

  // Kullanıcıları getir
  const { data: users = [], isLoading: usersLoading } = useQuery({
    queryKey: ["users"],
    queryFn: () => api.users.getAll(),
  })

  // Seçilen sunucunun veritabanı rollerini getir
  const { data: databaseRoles = [], isLoading: rolesLoading } = useQuery({
    queryKey: ["database-roles", selectedServerId],
    queryFn: () => api.userPermissions.getDatabaseRoles(selectedServerId),
    enabled: !!selectedServerId,
  })

  // Sunucu seçimi değiştiğinde rolleri temizle
  useEffect(() => {
    if (selectedServerId) {
      form.setValue("roleNames", [])
    }
  }, [selectedServerId, form])

  const onSubmit = async (data: CreateUserPermissionRequest) => {
    setIsLoading(true)
    try {
      // Eğer özel tarih seçildiyse, gün sayısını hesapla
      let finalData = { ...data }
      if (useCustomDate && data.customDate) {
        const today = new Date()
        const diffTime = data.customDate.getTime() - today.getTime()
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        finalData.durationDays = Math.max(1, diffDays) // En az 1 gün
      }

      await api.userPermissions.create(finalData)
      toast.success("Kullanıcı yetkisi başarıyla verildi")
      form.reset()
      setSelectedServerId("")
      setUseCustomDate(false)
      setOpen(false)
      onPermissionAdded()
    } catch (error: any) {
      console.error("Yetki verme hatası:", error)
      toast.error(error.message || "Yetki verilirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  const handleServerChange = (serverId: string) => {
    setSelectedServerId(serverId)
    form.setValue("serverId", serverId)
  }

  const handleRoleToggle = (roleName: string, checked: boolean) => {
    const currentRoles = form.getValues("roleNames")
    if (checked) {
      form.setValue("roleNames", [...currentRoles, roleName])
    } else {
      form.setValue("roleNames", currentRoles.filter(role => role !== roleName))
    }
  }

  // Aktif sunucuları filtrele
  const activeServers = servers.filter((server: Server) => server.isActive)
  
  // Aktif kullanıcıları filtrele
  const activeUsers = users.filter((user: User) => user.active)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Yetki Ver
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Kullanıcıya Yetki Ver
          </DialogTitle>
          <DialogDescription>
            Bir kullanıcıya veritabanı yetkisi verin. Önce sunucu, sonra kullanıcı seçin.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Sunucu Seçimi */}
            <FormField
              control={form.control}
              name="serverId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sunucu</FormLabel>
                  <Select
                    onValueChange={handleServerChange}
                    value={field.value}
                    disabled={serversLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sunucu seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {serversLoading ? (
                        <SelectItem value="loading" disabled>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Yükleniyor...
                        </SelectItem>
                      ) : (
                        activeServers.map((server: Server) => (
                          <SelectItem key={server.id} value={server.id}>
                            {server.name} ({server.databaseType})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Kullanıcı Seçimi */}
            <FormField
              control={form.control}
              name="userId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kullanıcı</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={usersLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Kullanıcı seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {usersLoading ? (
                        <SelectItem value="loading" disabled>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Yükleniyor...
                        </SelectItem>
                      ) : (
                        activeUsers.map((user: User) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.firstName} {user.lastName} ({user.email})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Yetki Türleri */}
            {selectedServerId && (
              <FormField
                control={form.control}
                name="roleNames"
                render={() => (
                  <FormItem>
                    <FormLabel>Yetki Türleri</FormLabel>
                    <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
                      {rolesLoading ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Yetki türleri yükleniyor...
                        </div>
                      ) : databaseRoles.length === 0 ? (
                        <div className="text-center py-4 text-muted-foreground">
                          Bu sunucu için yetki türü bulunamadı
                        </div>
                      ) : (
                        databaseRoles.map((role: DatabaseRole) => (
                          <div key={role.roleName} className="flex items-start space-x-2">
                            <Checkbox
                              id={role.roleName}
                              checked={form.getValues("roleNames").includes(role.roleName)}
                              onCheckedChange={(checked) => 
                                handleRoleToggle(role.roleName, checked as boolean)
                              }
                            />
                            <div className="grid gap-1.5 leading-none">
                              <label
                                htmlFor={role.roleName}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                              >
                                {role.roleName}
                                {role.isSuper && (
                                  <span className="ml-1 text-xs bg-red-100 text-red-800 px-1 rounded">
                                    SUPER
                                  </span>
                                )}
                              </label>
                              {role.description && (
                                <p className="text-xs text-muted-foreground">
                                  {role.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Süre Seçimi */}
            <div className="space-y-4">
              <Label>Yetki Süresi</Label>

              {/* Önceden tanımlı süreler */}
              {!useCustomDate && (
                <FormField
                  control={form.control}
                  name="durationDays"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Süre seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DURATION_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Özel tarih seçimi */}
              {useCustomDate && (
                <FormField
                  control={form.control}
                  name="customDate"
                  render={({ field }) => (
                    <FormItem>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: tr })
                              ) : (
                                <span>Bitiş tarihi seçin</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date()}
                            initialFocus
                            locale={tr}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Tarih seçimi toggle */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="custom-date"
                  checked={useCustomDate}
                  onCheckedChange={(checked) => {
                    setUseCustomDate(checked as boolean)
                    if (!checked) {
                      form.setValue("customDate", undefined)
                      form.setValue("durationDays", 7)
                    }
                  }}
                />
                <Label htmlFor="custom-date" className="text-sm">
                  Özel tarih seç
                </Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Veriliyor...
                  </>
                ) : (
                  "Yetki Ver"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
