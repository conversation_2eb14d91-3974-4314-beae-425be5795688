"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { Server, Database, Edit, Eye, EyeOff, TestTube } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { api } from "@/lib/api"
import { UpdateServerRequest, DATABASE_TYPES, REPLICA_TYPES } from "@/types/server"
import type { Server as ServerType } from "@/types/server"

const updateServerSchema = z.object({
  name: z.string().min(1, "Sunucu adı gereklidir").max(100, "Sunucu adı en fazla 100 karakter olabilir"),
  host: z.string().min(1, "Host adresi gereklidir").max(255, "Host adresi en fazla 255 karakter olabilir"),
  port: z.number().min(1, "Port numarası 1'den küçük olamaz").max(65535, "Port numarası 65535'den büyük olamaz"),
  databaseType: z.enum(['POSTGRESQL', 'MYSQL', 'MONGODB', 'REDIS', 'SQL_SERVER'], {
    required_error: "Veritabanı tipi seçiniz",
  }),
  replicaType: z.enum(['PRIMARY', 'SECONDARY', 'READ_ONLY']).optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
})

interface EditServerDialogProps {
  server: ServerType | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onServerUpdated: () => void
}

export function EditServerDialog({ server, open, onOpenChange, onServerUpdated }: EditServerDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [isTesting, setIsTesting] = useState(false)

  const form = useForm<UpdateServerRequest>({
    resolver: zodResolver(updateServerSchema),
    defaultValues: {
      name: "",
      host: "",
      port: 5432,
      databaseType: "POSTGRESQL",
      username: "",
      password: "",
      description: "",
      replicaType: undefined,
      isActive: true,
    },
  })

  // Server değiştiğinde formu güncelle
  useEffect(() => {
    if (server) {
      form.reset({
        name: server.name,
        host: server.host,
        port: server.port,
        databaseType: server.databaseType,
        username: server.username || "",
        password: "", // Güvenlik için şifreyi boş bırak
        description: server.description || "",
        replicaType: server.replicaType,
        isActive: server.isActive,
      })
    }
  }, [server, form])

  const selectedDatabaseType = form.watch("databaseType")

  // Veritabanı tipi değiştiğinde default port'u güncelle
  const handleDatabaseTypeChange = (value: string) => {
    const dbType = DATABASE_TYPES.find(db => db.value === value)
    if (dbType) {
      form.setValue("port", dbType.defaultPort)
    }
  }

  const handleTestConnection = async () => {
    if (!server) return

    // Form'dan güncel verileri al
    const formData = form.getValues()

    setIsTesting(true)
    try {
      // Test için sunucu verilerini hazırla
      const testData = {
        name: formData.name,
        host: formData.host,
        port: formData.port,
        databaseType: formData.databaseType,
        username: formData.username,
        password: formData.password || server.password // Eğer yeni şifre girilmemişse mevcut şifreyi kullan
      }

      const result = await api.servers.testConnection(testData)
      if (result.success) {
        toast.success(`Bağlantı başarılı! ${result.connectionTime || ''}`)
      } else {
        toast.error(`Bağlantı başarısız: ${result.message}`)
      }
    } catch (error: any) {
      console.error("Bağlantı testi hatası:", error)
      toast.error("Bağlantı testi sırasında bir hata oluştu")
    } finally {
      setIsTesting(false)
    }
  }

  const onSubmit = async (data: UpdateServerRequest) => {
    if (!server) return

    setIsLoading(true)
    try {
      // Eğer şifre boşsa, şifreyi güncelleme isteğinden çıkar
      const updateData = { ...data }
      if (!updateData.password || updateData.password.trim() === "") {
        delete updateData.password
      }

      await api.servers.update(server.id, updateData)
      toast.success("Sunucu başarıyla güncellendi")
      onOpenChange(false)
      onServerUpdated()
    } catch (error: any) {
      console.error("Sunucu güncelleme hatası:", error)
      toast.error(error.message || "Sunucu güncellenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  if (!server) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Sunucu Düzenle
          </DialogTitle>
          <DialogDescription>
            Sunucu bilgilerini güncelleyin.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sunucu Adı *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Database className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Sunucu adını giriniz"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="host"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Host Adresi *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="localhost"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="port"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Port *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="5432"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="databaseType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Veritabanı Tipi *</FormLabel>
                  <Select 
                    onValueChange={(value) => {
                      field.onChange(value)
                      handleDatabaseTypeChange(value)
                    }} 
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Veritabanı tipi seçiniz" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {DATABASE_TYPES.map((dbType) => (
                        <SelectItem key={dbType.value} value={dbType.value}>
                          {dbType.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Replica Type - sadece SQL Server için göster */}
            {selectedDatabaseType === 'SQL_SERVER' && (
              <FormField
                control={form.control}
                name="replicaType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Replica Tipi</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Replica tipi seçiniz (opsiyonel)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {REPLICA_TYPES.map((replicaType) => (
                          <SelectItem key={replicaType.value} value={replicaType.value}>
                            <div className="flex flex-col">
                              <span>{replicaType.label}</span>
                              <span className="text-xs text-muted-foreground">{replicaType.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kullanıcı Adı</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Kullanıcı adını giriniz"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Şifre</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Yeni şifre (boş bırakılırsa değişmez)"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Sunucu hakkında açıklama giriniz"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Sunucu Durumu
                    </FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Sunucunun aktif olup olmadığını belirler
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleTestConnection}
                disabled={isLoading || isTesting}
                className="gap-2"
              >
                <TestTube className="h-4 w-4" />
                {isTesting ? "Test Ediliyor..." : "Bağlantı Testi"}
              </Button>

              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  İptal
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Güncelleniyor..." : "Sunucu Güncelle"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
