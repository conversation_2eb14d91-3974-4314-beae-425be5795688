"use client"

import { useState, useEffect } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  Pie<PERSON><PERSON>, 
  TrendingUp, 
  Hash, 
  CheckCircle, 
  Activity, 
  Table, 
  RefreshCw,
  AlertCircle,
  Play,
  Pause,
  Settings
} from "lucide-react"
import { toast } from "sonner"
import { api } from "@/lib/api"
import { Server } from "@/types/server"
import { <PERSON>ric<PERSON>uery, MetricDisplayField, ComponentType } from "@/lib/api"
import MetricComponentSelector from "@/components/metric-component-selector"
import { MetricResultCard } from "@/components/metrics/metric-result-card"
import { useAuthToken } from "@/hooks/use-auth-token"

// Component type icons mapping
const componentTypeIcons: Record<ComponentType, React.ComponentType<any>> = {
  NUMBER_CARD: Hash,
  STATUS_CARD: CheckCircle,
  PROGRESS_BAR: Activity,
  BADGE: Hash,
  ICON_NUMBER: Hash,
  TREND_CARD: TrendingUp,
  LINE_CHART: LineChart,
  BAR_CHART: BarChart3,
  AREA_CHART: BarChart3,
  TABLE: Table,
  STATISTICS_CARD: BarChart3,
  COMPARISON_CARD: PieChart
}

// Component type colors
const componentTypeColors: Record<ComponentType, string> = {
  NUMBER_CARD: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  STATUS_CARD: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  PROGRESS_BAR: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  BADGE: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
  ICON_NUMBER: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
  TREND_CARD: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  LINE_CHART: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  BAR_CHART: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  AREA_CHART: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  TABLE: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
  STATISTICS_CARD: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  COMPARISON_CARD: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

interface ServerMetricsDisplayProps {
  server: Server;
  metrics: MetricQuery[];
  onRefresh: () => void;
}

interface MetricResult {
  id: string;
  metricQueryId: string;
  serverId: string;
  recordedAt: number;
  executionTimeMs: number;
  success: boolean;
  errorMessage?: string;
  resultData: {
    rows: Record<string, any>[];
    rowCount: number;
    timestamp: number;
  };
}

export function ServerMetricsDisplay({ server, metrics, onRefresh }: ServerMetricsDisplayProps) {
  // Token yönetimi
  const { session, status, isTokenReady, isAuthenticated } = useAuthToken()

  const [isRefreshing, setIsRefreshing] = useState(false)
  const [expandedMetrics, setExpandedMetrics] = useState<Set<string>>(new Set())
  const [metricResults, setMetricResults] = useState<Record<string, MetricResult[]>>({})
  const [loadingResults, setLoadingResults] = useState<Set<string>>(new Set())

  // Tüm metrik verilerini çek
  const loadAllMetricResults = async () => {
    if (loadingResults.has('all')) return

    setLoadingResults(prev => new Set(prev).add('all'))
    try {
      console.log('Loading all metric results for server:', server.id)

      // API'den sunucunun son metrik sonuçlarını çek
      const response = await api.metricResults.getLatestByServer(server.id)

      console.log('API Response:', response)

      if (response && Array.isArray(response)) {
        // Her metrik için sonuçları grupla
        const groupedResults: Record<string, MetricResult[]> = {}

        response.forEach((result: MetricResult) => {
          if (!groupedResults[result.metricQueryId]) {
            groupedResults[result.metricQueryId] = []
          }
          groupedResults[result.metricQueryId].push(result)
        })

        console.log('Grouped results:', groupedResults)
        setMetricResults(groupedResults)
      }
    } catch (error) {
      console.error('Error loading metric results:', error)
      toast.error('Metrik verileri yüklenirken hata oluştu')
    } finally {
      setLoadingResults(prev => {
        const newSet = new Set(prev)
        newSet.delete('all')
        return newSet
      })
    }
  }

  // Metrik genişletildiğinde verileri yükle
  useEffect(() => {
    if (isTokenReady && isAuthenticated) {
      expandedMetrics.forEach(metricId => {
        if (!metricResults[metricId] && !loadingResults.has(metricId)) {
          loadMetricResults(metricId)
        }
      })
    }
  }, [expandedMetrics, isTokenReady, isAuthenticated])

  // Sayfa yüklendiğinde tüm metriklerin verilerini yükle
  useEffect(() => {
    if (isTokenReady && isAuthenticated) {
      metrics.forEach(metric => {
        if (!metricResults[metric.id] && !loadingResults.has(metric.id)) {
          loadMetricResults(metric.id)
        }
      })
    }
  }, [metrics, isTokenReady, isAuthenticated])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await onRefresh()
      toast.success('Metrikler yenilendi')
    } catch (error) {
      toast.error('Metrikler yenilenirken hata oluştu')
    } finally {
      setIsRefreshing(false)
    }
  }

  const toggleMetricExpansion = (metricId: string) => {
    setExpandedMetrics(prev => {
      const newSet = new Set(prev)
      if (newSet.has(metricId)) {
        newSet.delete(metricId)
      } else {
        newSet.add(metricId)
      }
      return newSet
    })
  }

  const getMetricCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'CONNECTION': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'PERFORMANCE': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'STORAGE': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      'CPU': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'RAM': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
      'DISK': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
    return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
  }

  if (metrics.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Sunucu Metrikleri
          </CardTitle>
          <CardDescription>
            {server.name} sunucusu için metrik verileri
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Metrik Bulunamadı</h3>
            <p className="text-muted-foreground mb-4">
              Bu sunucu için {server.databaseType} veritabanı tipine uygun aktif metrik bulunmuyor.
            </p>
            <Button variant="outline" onClick={() => window.open('/metrics', '_blank')}>
              <Settings className="mr-2 h-4 w-4" />
              Metrik Yönetimi
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Sunucu Metrikleri
                <Badge variant="secondary">{metrics.length} metrik</Badge>
              </CardTitle>
              <CardDescription>
                {server.name} sunucusu için {server.databaseType} metrik verileri ve grafikleri
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Metrics Grid */}
      <div className="space-y-4">
        {metrics.map((metric, index) => {
          const isExpanded = expandedMetrics.has(metric.id)
          const hasDisplayFields = metric.displayFields && metric.displayFields.length > 0

          return (
            <Card key={metric.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-lg">{metric.queryName}</CardTitle>
                      <Badge className={getMetricCategoryColor(metric.metricCategory || 'PERFORMANCE')}>
                        {metric.metricCategory || 'PERFORMANCE'}
                      </Badge>
                      <Badge variant={metric.isActive ? "default" : "secondary"}>
                        {metric.isActive ? "Aktif" : "Pasif"}
                      </Badge>
                      {hasDisplayFields && (
                        <Badge variant="outline" className="text-xs">
                          {metric.displayFields!.length} bileşen
                        </Badge>
                      )}
                    </div>
                    {metric.queryDescription && (
                      <CardDescription>{metric.queryDescription}</CardDescription>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleMetricExpansion(metric.id)}
                    >
                      {isExpanded ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Metric Results - Show actual data */}
                {hasDisplayFields && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {metric.displayFields!.map((field) => (
                        <MetricResultCard
                          key={field.id}
                          displayField={field}
                          results={metricResults[metric.id] || []}
                          isLoading={loadingResults.has(metric.id)}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Component Selector - Expanded View */}
                {isExpanded && hasDisplayFields && (
                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium flex items-center gap-2 mb-3">
                      <Settings className="h-4 w-4" />
                      Bileşen Ayarları
                    </h4>
                    <MetricComponentSelector
                      metricQuery={metric}
                      onUpdate={onRefresh}
                    />
                  </div>
                )}

                {/* Metric Info */}
                <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                  <span>Sıra: {metric.executionOrder}</span>
                  <span>Oluşturulma: {new Date(metric.createdAt).toLocaleDateString('tr-TR')}</span>
                  {metric.validConfiguration !== undefined && (
                    <Badge variant={metric.validConfiguration ? "default" : "destructive"} className="text-xs">
                      {metric.validConfiguration ? "Geçerli" : "Hatalı"}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Metrik Özeti</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {metrics.filter(m => m.metricCategory === 'CONNECTION').length}
              </div>
              <div className="text-xs text-muted-foreground">Bağlantı</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {metrics.filter(m => m.metricCategory === 'PERFORMANCE').length}
              </div>
              <div className="text-xs text-muted-foreground">Performans</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {metrics.filter(m => m.metricCategory === 'STORAGE').length}
              </div>
              <div className="text-xs text-muted-foreground">Depolama</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {metrics.reduce((total, m) => total + (m.displayFields?.length || 0), 0)}
              </div>
              <div className="text-xs text-muted-foreground">Toplam Bileşen</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
