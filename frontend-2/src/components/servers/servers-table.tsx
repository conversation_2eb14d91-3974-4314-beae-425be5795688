"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Edit, Trash2, MoreHorizontal, CheckCircle, XCircle, Database, TestTube, Eye } from "lucide-react"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { EditServerDialog } from "@/components/servers/edit-server-dialog"
import { api } from "@/lib/api"
import { Server, DATABASE_TYPES } from "@/types/server"
import { useAuthToken } from "@/hooks/use-auth-token"

interface ServersTableProps {
  refreshTrigger: number
}

export function ServersTable({ refreshTrigger }: ServersTableProps) {
  const router = useRouter()
  const { isTokenReady, isAuthenticated } = useAuthToken()
  const [servers, setServers] = useState<Server[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [editingServer, setEditingServer] = useState<Server | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [testingServerId, setTestingServerId] = useState<string | null>(null)

  const fetchServers = async () => {
    try {
      setIsLoading(true)
      const response = await api.servers.getAll()
      setServers(response)
    } catch (error: any) {
      console.error("Sunucular yüklenirken hata:", error)
      toast.error("Sunucular yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // Token hazır ve authenticated olduğunda veri yükle
    if (isTokenReady && isAuthenticated) {
      fetchServers()
    }
  }, [refreshTrigger, isTokenReady, isAuthenticated])

  const handleEditServer = (server: Server) => {
    setEditingServer(server)
    setEditDialogOpen(true)
  }

  const handleDeleteServer = async (serverId: string) => {
    if (!confirm("Bu sunucuyu silmek istediğinizden emin misiniz?")) {
      return
    }

    try {
      await api.servers.delete(serverId)
      toast.success("Sunucu başarıyla silindi")
      fetchServers()
    } catch (error: any) {
      console.error("Sunucu silme hatası:", error)
      toast.error("Sunucu silinirken bir hata oluştu")
    }
  }

  const handleTestConnection = async (server: Server) => {
    setTestingServerId(server.id)
    try {
      // Sunucu verilerini test endpoint'ine gönder
      const testData = {
        name: server.name,
        host: server.host,
        port: server.port,
        databaseType: server.databaseType,
        databaseName: server.databaseName,
        username: server.username,
        password: server.password // Şifreli olarak gelecek ama backend'de decrypt edilecek
      }

      const result = await api.servers.testConnection(testData)
      if (result.success) {
        toast.success(`Bağlantı başarılı! ${result.connectionTime || ''}`)
      } else {
        toast.error(`Bağlantı başarısız: ${result.message}`)
      }
    } catch (error: any) {
      console.error("Bağlantı testi hatası:", error)
      toast.error("Bağlantı testi sırasında bir hata oluştu")
    } finally {
      setTestingServerId(null)
    }
  }

  const getDatabaseTypeLabel = (type: string) => {
    const dbType = DATABASE_TYPES.find(db => db.value === type)
    return dbType?.label || type
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (servers.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Henüz sunucu bulunmuyor.</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Sunucu Adı</TableHead>
            <TableHead>Host</TableHead>
            <TableHead>Port</TableHead>
            <TableHead>Veritabanı Tipi</TableHead>
            <TableHead>Veritabanı Adı</TableHead>
            <TableHead>Durum</TableHead>
            <TableHead>Oluşturulma Tarihi</TableHead>
            <TableHead className="text-right">İşlemler</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {servers.map((server) => (
            <TableRow key={server.id}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-muted-foreground" />
                  <button
                    onClick={() => router.push(`/servers/${server.id}`)}
                    className="text-primary hover:underline font-medium"
                  >
                    {server.name}
                  </button>
                </div>
              </TableCell>
              <TableCell>{server.host}</TableCell>
              <TableCell>{server.port}</TableCell>
              <TableCell>
                <Badge variant="outline">
                  {getDatabaseTypeLabel(server.databaseType)}
                </Badge>
              </TableCell>
              <TableCell>{server.databaseName || "-"}</TableCell>
              <TableCell>
                <Badge
                  variant={server.isActive ? "default" : "secondary"}
                  className="gap-1"
                >
                  {server.isActive ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <XCircle className="h-3 w-3" />
                  )}
                  {server.isActive ? "Aktif" : "Pasif"}
                </Badge>
              </TableCell>
              <TableCell>{formatDate(server.createdAt)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Menüyü aç</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => router.push(`/servers/${server.id}`)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Detayları Görüntüle
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleTestConnection(server)}
                      disabled={testingServerId === server.id}
                    >
                      <TestTube className="mr-2 h-4 w-4" />
                      {testingServerId === server.id ? "Test Ediliyor..." : "Bağlantı Testi"}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleEditServer(server)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Düzenle
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => handleDeleteServer(server.id)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Sil
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <EditServerDialog
        server={editingServer}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onServerUpdated={fetchServers}
      />
    </div>
  )
}
