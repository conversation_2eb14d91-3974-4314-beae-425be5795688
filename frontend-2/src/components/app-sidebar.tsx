"use client"

import Link from "next/link"
import {
  Users,
  Server,
  BarChart3,
  TestTube,
  ChevronRight,
  Shield,
  History,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

// Menü yapısı
const menuData = [
  {
    title: "Kullanıcı İşlemleri",
    icon: Users,
    items: [
      {
        title: "<PERSON>llan<PERSON>cı<PERSON>",
        url: "/users",
        icon: Users,
      },
      {
        title: "Kullanıcı Yetkileri",
        url: "/user-permissions",
        icon: Shield,
      },
      {
        title: "Yet<PERSON> Geçmişi",
        url: "/permission-history",
        icon: History,
      },
    ],
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    icon: Server,
    items: [
      {
        title: "Sunuc<PERSON>",
        url: "/servers",
        icon: Server,
      },
      {
        title: "Sunucu Metrikleri",
        url: "/metrics",
        icon: BarChart3,
      },
      {
        title: "Metrik Test",
        url: "/metric-test",
        icon: TestTube,
      },
    ],
  },
]

export function AppSidebar() {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-lg font-semibold px-4 py-4">
            OptiDB
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuData.map((group) => (
                <Collapsible key={group.title} defaultOpen className="group/collapsible">
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton className="w-full">
                        <group.icon className="h-5 w-5" />
                        <span className="font-medium">{group.title}</span>
                        <ChevronRight className="ml-auto h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-90" />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {group.items.map((item) => (
                          <SidebarMenuSubItem key={item.title}>
                            <SidebarMenuSubButton asChild>
                              <Link href={item.url} className="flex items-center gap-3">
                                <item.icon className="h-4 w-4" />
                                <span>{item.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
