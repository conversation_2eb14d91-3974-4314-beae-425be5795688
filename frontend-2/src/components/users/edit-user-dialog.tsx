"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { User, Mail, Edit, ToggleLeft, ToggleRight } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { api } from "@/lib/api"
import { User as UserType, UpdateUserRequest } from "@/types/user"

const updateUserSchema = z.object({
  firstName: z.string().min(1, "Ad gereklidir").max(100, "Ad en fazla 100 karakter olabilir"),
  lastName: z.string().min(1, "Soyad gereklidir").max(100, "Soyad en fazla 100 karakter olabilir"),
  username: z.string().min(3, "Kullanıcı adı en az 3 karakter olmalıdır").max(50, "Kullanıcı adı en fazla 50 karakter olabilir"),
  email: z.string().email("Geçerli bir email adresi giriniz"),
  active: z.boolean(),
})

interface EditUserDialogProps {
  user: UserType | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onUserUpdated: () => void
}

export function EditUserDialog({ user, open, onOpenChange, onUserUpdated }: EditUserDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<UpdateUserRequest & { active: boolean }>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      username: "",
      email: "",
      active: true,
    },
  })

  useEffect(() => {
    if (user) {
      form.reset({
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
        email: user.email,
        active: user.active,
      })
    }
  }, [user, form])

  const onSubmit = async (data: UpdateUserRequest & { active: boolean }) => {
    if (!user) return

    setIsLoading(true)
    try {
      await api.users.update(user.id, data)
      toast.success("Kullanıcı başarıyla güncellendi")
      onOpenChange(false)
      onUserUpdated()
    } catch (error: any) {
      console.error("Kullanıcı güncelleme hatası:", error)
      toast.error(error.message || "Kullanıcı güncellenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleUserStatus = () => {
    const newStatus = !form.getValues("active")
    form.setValue("active", newStatus)
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Kullanıcı Düzenle
          </DialogTitle>
          <DialogDescription>
            Kullanıcı bilgilerini güncelleyin. Tüm alanlar zorunludur.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Durum:</span>
                <Badge variant={form.watch("active") ? "default" : "secondary"}>
                  {form.watch("active") ? "Aktif" : "Pasif"}
                </Badge>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={toggleUserStatus}
                className="gap-2"
              >
                {form.watch("active") ? (
                  <ToggleRight className="h-4 w-4 text-green-600" />
                ) : (
                  <ToggleLeft className="h-4 w-4 text-gray-400" />
                )}
                {form.watch("active") ? "Pasif Yap" : "Aktif Yap"}
              </Button>
            </div>

            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ad</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Adınızı giriniz"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Soyad</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Soyadınızı giriniz"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kullanıcı Adı</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Kullanıcı adınızı giriniz"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Güncelleniyor..." : "Güncelle"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
