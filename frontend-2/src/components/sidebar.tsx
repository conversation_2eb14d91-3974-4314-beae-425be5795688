"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  Menu,
  Users,
  Shield,
  UserCheck,
  History,
  Server,
  BarChart3,
  TestTube,
  ChevronDown,
  ChevronRight,
} from "lucide-react"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

interface MenuItem {
  title: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  children?: MenuItem[]
}

const menuItems: MenuItem[] = [
  {
    title: "Kullanıcı İşlemleri",
    icon: Users,
    children: [
      {
        title: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        href: "/dashboard/users",
        icon: Users,
      },
      {
        title: "<PERSON><PERSON><PERSON>",
        href: "/dashboard/permissions",
        icon: Shield,
      },
      {
        title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Yetkileri",
        href: "/dashboard/user-permissions",
        icon: User<PERSON><PERSON><PERSON>,
      },
      {
        title: "<PERSON><PERSON>",
        href: "/dashboard/permission-history",
        icon: History,
      },
    ],
  },
  {
    title: "Sunucu Yönetimi",
    icon: Server,
    children: [
      {
        title: "Sunucu Listesi",
        href: "/dashboard/servers",
        icon: Server,
      },
      {
        title: "Sunucu Metrikleri",
        href: "/dashboard/metrics",
        icon: BarChart3,
      },
      {
        title: "Metrik Test",
        href: "/dashboard/metric-test",
        icon: TestTube,
      },
    ],
  },
]

interface SidebarContentProps {
  onItemClick?: () => void
}

function SidebarContent({ onItemClick }: SidebarContentProps) {
  const pathname = usePathname()
  const [openSections, setOpenSections] = useState<string[]>(["Kullanıcı İşlemleri", "Sunucu Yönetimi"])

  const toggleSection = (title: string) => {
    setOpenSections(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-6">
        <h2 className="text-lg font-semibold">OptiDB</h2>
      </div>
      
      <nav className="flex-1 px-4 pb-4 space-y-2">
        {menuItems.map((item) => (
          <Collapsible
            key={item.title}
            open={openSections.includes(item.title)}
            onOpenChange={() => toggleSection(item.title)}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-between h-12 px-4"
              >
                <div className="flex items-center space-x-3">
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.title}</span>
                </div>
                {openSections.includes(item.title) ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="space-y-1">
              {item.children?.map((child) => (
                <Link
                  key={child.title}
                  href={child.href || "#"}
                  onClick={onItemClick}
                  className={cn(
                    "flex items-center space-x-3 px-4 py-3 ml-4 rounded-md text-sm transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    pathname === child.href
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground"
                  )}
                >
                  <child.icon className="h-4 w-4" />
                  <span>{child.title}</span>
                </Link>
              ))}
            </CollapsibleContent>
          </Collapsible>
        ))}
      </nav>
    </div>
  )
}


