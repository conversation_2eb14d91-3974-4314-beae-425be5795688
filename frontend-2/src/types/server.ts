export interface Server {
  id: string
  name: string
  host: string
  port: number
  databaseType: 'POSTGRESQL' | 'MYSQL' | 'MONGODB' | 'REDIS' | 'SQL_SERVER'
  username?: string
  password?: string
  description?: string
  replicaType?: 'PRIMARY' | 'SECONDARY' | 'READ_ONLY'
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateServerRequest {
  name: string
  host: string
  port: number
  databaseType: 'POSTGRESQL' | 'MYSQL' | 'MONGODB' | 'REDIS' | 'SQL_SERVER'
  username?: string
  password?: string
  description?: string
  replicaType?: 'PRIMARY' | 'SECONDARY' | 'READ_ONLY'
}

export interface UpdateServerRequest {
  name: string
  host: string
  port: number
  databaseType: 'POSTGRESQL' | 'MYSQL' | 'MONGODB' | 'REDIS' | 'SQL_SERVER'
  username?: string
  password?: string
  description?: string
  replicaType?: 'PRIMARY' | 'SECONDARY' | 'READ_ONLY'
  isActive?: boolean
}

export const DATABASE_TYPES = [
  { value: 'POSTGRESQL', label: 'PostgreSQL', defaultPort: 5432 },
  { value: 'MYSQL', label: 'MySQL', defaultPort: 3306 },
  { value: 'MONGODB', label: 'MongoDB', defaultPort: 27017 },
  { value: 'REDIS', label: 'Redis', defaultPort: 6379 },
  { value: 'SQL_SERVER', label: 'SQL Server', defaultPort: 1433 },
] as const

export const REPLICA_TYPES = [
  { value: 'PRIMARY', label: 'Primary', description: 'Ana replica - okuma/yazma' },
  { value: 'SECONDARY', label: 'Secondary', description: 'İkincil replica - sadece okuma, failover hazır' },
  { value: 'READ_ONLY', label: 'Read Only', description: 'Sadece okuma amaçlı replica' },
] as const
