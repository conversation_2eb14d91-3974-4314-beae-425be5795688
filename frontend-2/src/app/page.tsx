"use client"

import { useSession } from "next-auth/react"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { StatsCards } from "@/components/dashboard/stats-cards"
import { ChartSection } from "@/components/dashboard/chart-section"
import { DataTable } from "@/components/dashboard/data-table"
import { api } from "@/lib/api"
import { Loader2 } from "lucide-react"

export default function HomePage() {
  const { data: session, status } = useSession()

  // Mock data kullanarak API çağrılarını simüle edelim
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      // Mock data
      await new Promise(resolve => setTimeout(resolve, 1000)) // Loading simülasyonu
      return {
        totalUsers: 150,
        totalDatabases: 8,
        activeConnections: 23,
        successRate: 98.5
      }
    },
    enabled: !!session,
  })

  const { data: chartData, isLoading: chartLoading } = useQuery({
    queryKey: ['dashboard-chart'],
    queryFn: async () => {
      // Mock data
      await new Promise(resolve => setTimeout(resolve, 800))
      return [
        { name: "Pzt", connections: 120, success: 115 },
        { name: "Sal", connections: 150, success: 142 },
        { name: "Çar", connections: 180, success: 175 },
        { name: "Per", connections: 200, success: 190 },
        { name: "Cum", connections: 170, success: 165 },
        { name: "Cmt", connections: 90, success: 88 },
        { name: "Paz", connections: 60, success: 58 },
      ]
    },
    enabled: !!session,
  })

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Oturum Gerekli</h1>
          <p className="text-gray-600 mb-4">Bu sayfayı görüntülemek için giriş yapmanız gerekiyor.</p>
          <a href="/auth/login" className="text-blue-600 hover:underline">
            Giriş Yap
          </a>
        </div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
              <p className="text-muted-foreground">
                Hoş geldiniz, {session.user.name}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <StatsCards data={stats} isLoading={statsLoading} />
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ChartSection data={chartData} isLoading={chartLoading} />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Son Aktiviteler</CardTitle>
                <CardDescription>
                  Sistemdeki son aktivitelerin listesi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DataTable />
              </CardContent>
            </Card>
          </motion.div>
        </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
