"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { 
  Plus, 
  ArrowLeft, 
  Save,
  TestTube,
  Settings,
  Trash2,
  Database,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react"
import { metricsApi, metricHel<PERSON>, DATABASE_TYPES, METRIC_CATEGORIES } from "@/lib/api/metrics"
import type { CreateMetricRequest, DisplayField, MetricTestResult } from "@/lib/api"
import { toast } from "sonner"

export default function AddMetricPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<MetricTestResult | null>(null)
  const [servers, setServers] = useState<any[]>([])
  const [selectedServerId, setSelectedServerId] = useState<string>("")

  // Form data
  const [formData, setFormData] = useState<CreateMetricRequest>({
    queryName: "",
    queryDescription: "",
    databaseType: "",
    sqlQuery: "",
    metricCategory: "",
    displayFields: [],
    isActive: true,
    executionOrder: 0,
  })

  // Display fields state
  const [displayFields, setDisplayFields] = useState<DisplayField[]>([])

  // Load servers when database type changes
  useEffect(() => {
    if (formData.databaseType) {
      loadServers(formData.databaseType)
    }
  }, [formData.databaseType])

  const loadServers = async (databaseType: string) => {
    try {
      const data = await metricsApi.getServersByType(databaseType)
      setServers(data)
      setSelectedServerId("")
    } catch (error) {
      console.error('Error loading servers:', error)
      toast.error('Sunucular yüklenirken hata oluştu')
    }
  }

  // Handle form field changes
  const handleInputChange = (field: keyof CreateMetricRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle display field changes
  const handleDisplayFieldChange = (index: number, field: keyof DisplayField, value: string) => {
    const updatedFields = [...displayFields]
    updatedFields[index] = {
      ...updatedFields[index],
      [field]: value
    }
    setDisplayFields(updatedFields)
  }

  // Add new display field
  const addDisplayField = () => {
    setDisplayFields(prev => [...prev, {
      columnName: "",
      displayName: "",
      dataType: "string"
    }])
  }

  // Remove display field
  const removeDisplayField = (index: number) => {
    setDisplayFields(prev => prev.filter((_, i) => i !== index))
  }

  // Test SQL query
  const testQuery = async () => {
    if (!formData.sqlQuery.trim()) {
      toast.error('SQL sorgusu gereklidir')
      return
    }

    if (!selectedServerId) {
      toast.error('Test için sunucu seçiniz')
      return
    }

    try {
      setTesting(true)
      const result = await metricsApi.test({
        sqlQuery: formData.sqlQuery,
        databaseType: formData.databaseType,
        serverId: selectedServerId
      })
      
      setTestResult(result)
      
      if (result.success) {
        toast.success('SQL sorgusu başarıyla test edildi')
        
        // Auto-populate display fields from columns if available
        if (result.columns && result.columns.length > 0) {
          const autoFields = result.columns.map(col => 
            metricHelpers.createDisplayFieldFromColumn(col)
          )
          setDisplayFields(autoFields)
        }
      } else {
        toast.error(`Test başarısız: ${result.errorMessage}`)
      }
    } catch (error) {
      console.error('Error testing query:', error)
      toast.error('SQL sorgusu test edilirken hata oluştu')
    } finally {
      setTesting(false)
    }
  }

  // Auto-populate display fields from SQL
  const autoPopulateFields = () => {
    const columns = metricHelpers.extractColumnsFromSQL(formData.sqlQuery)
    if (columns.length > 0) {
      const autoFields = columns.map(col => 
        metricHelpers.createDisplayFieldFromColumn(col)
      )
      setDisplayFields(autoFields)
      toast.success(`${columns.length} alan otomatik eklendi`)
    } else {
      toast.warning('SQL sorgusundan alan çıkarılamadı')
    }
  }

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const errors = metricHelpers.validateMetricData({
      ...formData,
      displayFields
    })
    
    if (errors.length > 0) {
      errors.forEach(error => toast.error(error))
      return
    }

    try {
      setLoading(true)
      
      const metricData: CreateMetricRequest = {
        ...formData,
        displayFields: displayFields.length > 0 ? displayFields : undefined
      }
      
      await metricsApi.create(metricData)
      toast.success('Metrik başarıyla oluşturuldu')
      router.push('/metrics')
    } catch (error) {
      console.error('Error creating metric:', error)
      toast.error('Metrik oluşturulurken hata oluştu')
    } finally {
      setLoading(false)
    }
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h1 className="text-3xl font-bold tracking-tight">Yeni Metrik Ekle</h1>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                onClick={testQuery}
                disabled={!formData.sqlQuery.trim() || !selectedServerId || testing}
              >
                {testing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                SQL Test Et
              </Button>
              <Button 
                type="submit" 
                form="metric-form"
                disabled={loading}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Kaydet
              </Button>
            </div>
          </div>

          <form id="metric-form" onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Temel Bilgiler</CardTitle>
                <CardDescription>
                  Metriğin temel özelliklerini belirleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="queryName" className="text-sm font-medium">Metrik Adı *</Label>
                    <Input
                      id="queryName"
                      value={formData.queryName}
                      onChange={(e) => handleInputChange('queryName', e.target.value)}
                      placeholder="Örn: CPU Kullanımı"
                      required
                      className="border-2 border-input focus:border-primary"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="databaseType">Veritabanı Tipi *</Label>
                    <Select 
                      value={formData.databaseType} 
                      onValueChange={(value) => handleInputChange('databaseType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Veritabanı tipi seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(DATABASE_TYPES).map(type => (
                          <SelectItem key={type} value={type}>
                            {metricHelpers.getDatabaseTypeDisplayName(type)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="queryDescription">Açıklama</Label>
                  <Textarea
                    id="queryDescription"
                    value={formData.queryDescription}
                    onChange={(e) => handleInputChange('queryDescription', e.target.value)}
                    placeholder="Metriğin ne yaptığını açıklayın..."
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="metricCategory">Kategori</Label>
                    <Select 
                      value={formData.metricCategory} 
                      onValueChange={(value) => handleInputChange('metricCategory', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Kategori seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(METRIC_CATEGORIES).map(category => (
                          <SelectItem key={category} value={category}>
                            {metricHelpers.getCategoryDisplayName(category)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="executionOrder">Çalışma Sırası</Label>
                    <Input
                      id="executionOrder"
                      type="number"
                      value={formData.executionOrder}
                      onChange={(e) => handleInputChange('executionOrder', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* SQL Query */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>SQL Sorgusu</span>
                </CardTitle>
                <CardDescription>
                  Metrik verilerini almak için kullanılacak SQL sorgusu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sqlQuery">SQL Sorgusu *</Label>
                  <Textarea
                    id="sqlQuery"
                    value={formData.sqlQuery}
                    onChange={(e) => handleInputChange('sqlQuery', e.target.value)}
                    placeholder="SELECT column1, column2 FROM table_name WHERE condition"
                    rows={8}
                    className="font-mono"
                    required
                  />
                </div>

                {formData.databaseType && (
                  <div className="space-y-2">
                    <Label htmlFor="testServer">Test Sunucusu</Label>
                    <Select value={selectedServerId} onValueChange={setSelectedServerId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Test için sunucu seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {servers.map(server => (
                          <SelectItem key={server.id} value={server.id}>
                            {server.name} ({server.host}:{server.port})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Test Result */}
                {testResult && (
                  <div className={`p-4 rounded-lg border ${
                    testResult.success 
                      ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                      : 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800'
                  }`}>
                    <div className="flex items-center space-x-2 mb-2">
                      {testResult.success ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className="font-medium">
                        {testResult.success ? 'Test Başarılı' : 'Test Başarısız'}
                      </span>
                    </div>
                    <div className="text-sm space-y-1">
                      <p>Çalışma Süresi: {metricHelpers.formatExecutionTime(testResult.executionTimeMs)}</p>
                      <p>Satır Sayısı: {metricHelpers.formatRowCount(testResult.rowCount)}</p>
                      {testResult.errorMessage && (
                        <p className="text-red-600 dark:text-red-400">Hata: {testResult.errorMessage}</p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Display Fields */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Görüntüleme Alanları</span>
                </CardTitle>
                <CardDescription>
                  SQL sorgusundaki kolonların nasıl görüntüleneceğini belirleyin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Collapsible className="w-full">
                  <CollapsibleTrigger className="flex items-center gap-2 w-full p-4 hover:bg-accent rounded-lg">
                    <Settings className="h-4 w-4" />
                    <span>Görüntüleme Alanları ({displayFields.length})</span>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="p-4">
                      <div className="space-y-4">
                        {displayFields.length === 0 ? (
                          <div className="text-center py-8 text-muted-foreground">
                            <p>Henüz görüntüleme alanı eklenmemiş</p>
                            <p className="text-sm">SQL sorgusunu test ederek otomatik alan ekleyebilir veya manuel olarak ekleyebilirsiniz</p>
                          </div>
                        ) : (
                          displayFields.map((field, index) => (
                            <Card key={index} className="p-4">
                              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                                <div className="space-y-2">
                                  <Label htmlFor={`columnName-${index}`}>Kolon Adı *</Label>
                                  <Input
                                    id={`columnName-${index}`}
                                    value={field.columnName}
                                    onChange={(e) => handleDisplayFieldChange(index, 'columnName', e.target.value)}
                                    placeholder="SQL'deki kolon adı"
                                    required
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label htmlFor={`displayName-${index}`}>Görüntülenen Ad *</Label>
                                  <Input
                                    id={`displayName-${index}`}
                                    value={field.displayName}
                                    onChange={(e) => handleDisplayFieldChange(index, 'displayName', e.target.value)}
                                    placeholder="UI'da gösterilecek ad"
                                    required
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label htmlFor={`dataType-${index}`}>Veri Tipi *</Label>
                                  <Select
                                    value={field.dataType}
                                    onValueChange={(value: 'int' | 'string') => handleDisplayFieldChange(index, 'dataType', value)}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="string">String</SelectItem>
                                      <SelectItem value="int">Integer</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>

                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => removeDisplayField(index)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </Card>
                          ))
                        )}

                        <div className="flex items-center space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={addDisplayField}
                            className="flex-1"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Alan Ekle
                          </Button>

                          {formData.sqlQuery.trim() && (
                            <Button
                              type="button"
                              variant="outline"
                              onClick={autoPopulateFields}
                            >
                              <Database className="h-4 w-4 mr-2" />
                              Otomatik Ekle
                            </Button>
                          )}
                        </div>
                      </div>
                  </CollapsibleContent>
                </Collapsible>
              </CardContent>
            </Card>


          </form>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
