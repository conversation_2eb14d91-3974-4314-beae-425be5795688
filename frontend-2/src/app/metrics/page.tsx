"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  BarChart3,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  TestTube,

  Database,
  Activity,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { metricsApi, metricHelpers, DATABASE_TYPES, METRIC_CATEGORIES } from "@/lib/api/metrics"
import type { MetricQuery } from "@/lib/api"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import MetricComponentSelector from "@/components/metric-component-selector"
import { useAuthToken } from "@/hooks/use-auth-token"

export default function MetricsPage() {
  const router = useRouter()
  const { isTokenReady, isAuthenticated } = useAuthToken()
  const [metrics, setMetrics] = useState<MetricQuery[]>([])
  const [filteredMetrics, setFilteredMetrics] = useState<MetricQuery[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDatabaseType, setSelectedDatabaseType] = useState<string>("all")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [expandedMetrics, setExpandedMetrics] = useState<Set<string>>(new Set())

  // Load metrics
  const loadMetrics = async () => {
    try {
      setLoading(true)
      const data = await metricsApi.getAll()
      setMetrics(data)
      setFilteredMetrics(data)
    } catch (error) {
      console.error('Error loading metrics:', error)
      toast.error('Metrikler yüklenirken hata oluştu')
    } finally {
      setLoading(false)
    }
  }

  // Filter metrics
  useEffect(() => {
    let filtered = metrics

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(metric =>
        metric.queryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        metric.queryDescription?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Database type filter
    if (selectedDatabaseType !== "all") {
      filtered = filtered.filter(metric => metric.databaseType === selectedDatabaseType)
    }

    // Category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(metric => metric.metricCategory === selectedCategory)
    }

    // Status filter
    if (selectedStatus !== "all") {
      const isActive = selectedStatus === "active"
      filtered = filtered.filter(metric => metric.isActive === isActive)
    }

    setFilteredMetrics(filtered)
  }, [metrics, searchTerm, selectedDatabaseType, selectedCategory, selectedStatus])

  // Delete metric
  const handleDelete = async (id: string, name: string) => {
    try {
      await metricsApi.delete(id)
      toast.success(`${name} metriği silindi`)
      loadMetrics()
    } catch (error) {
      console.error('Error deleting metric:', error)
      toast.error('Metrik silinirken hata oluştu')
    }
  }

  // Toggle metric status
  const handleToggleStatus = async (id: string, name: string) => {
    try {
      await metricsApi.toggleStatus(id)
      toast.success(`${name} metriğinin durumu değiştirildi`)
      loadMetrics()
    } catch (error) {
      console.error('Error toggling metric status:', error)
      toast.error('Metrik durumu değiştirilirken hata oluştu')
    }
  }

  // Toggle metric expansion
  const toggleMetricExpansion = (metricId: string) => {
    setExpandedMetrics(prev => {
      const newSet = new Set(prev)
      if (newSet.has(metricId)) {
        newSet.delete(metricId)
      } else {
        newSet.add(metricId)
      }
      return newSet
    })
  }

  useEffect(() => {
    // Token hazır ve authenticated olduğunda veri yükle
    if (isTokenReady && isAuthenticated) {
      loadMetrics()
    } else if (isTokenReady && !isAuthenticated) {
      // Token hazır ama authenticated değil - login'e yönlendir
      router.push('/auth/login')
    }
  }, [isTokenReady, isAuthenticated, router])

  const stats = {
    total: metrics.length,
    active: metrics.filter(m => m.isActive).length,
    inactive: metrics.filter(m => !m.isActive).length,
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold tracking-tight">Sunucu Metrikleri</h1>
            </div>
            <Button onClick={() => router.push('/metrics/add')}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Metrik Ekle
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Toplam Metrik</p>
                    <p className="text-2xl font-bold">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Aktif</p>
                    <p className="text-2xl font-bold">{stats.active}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Pasif</p>
                    <p className="text-2xl font-bold">{stats.inactive}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <span>Filtreler</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Metrik ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={selectedDatabaseType} onValueChange={setSelectedDatabaseType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Veritabanı Tipi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Tipler</SelectItem>
                    {Object.keys(DATABASE_TYPES).map(type => (
                      <SelectItem key={type} value={type}>
                        {metricHelpers.getDatabaseTypeDisplayName(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Kategoriler</SelectItem>
                    {Object.keys(METRIC_CATEGORIES).map(category => (
                      <SelectItem key={category} value={category}>
                        {metricHelpers.getCategoryDisplayName(category)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Durum" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Pasif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Metrics List */}
          <Card>
            <CardHeader>
              <CardTitle>Metrik Listesi</CardTitle>
              <CardDescription>
                {filteredMetrics.length} metrik gösteriliyor
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredMetrics.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {metrics.length === 0 ? 'Henüz metrik eklenmemiş' : 'Filtrelere uygun metrik bulunamadı'}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredMetrics.map((metric) => {
                    const isExpanded = expandedMetrics.has(metric.id)
                    const hasDisplayFields = metric.displayFields && metric.displayFields.length > 0

                    return (
                      <Card key={metric.id} className="hover:shadow-md transition-shadow">
                        <Collapsible open={isExpanded} onOpenChange={() => toggleMetricExpansion(metric.id)}>
                          <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <CardTitle className="text-lg">{metric.queryName}</CardTitle>
                                  {hasDisplayFields && (
                                    <Badge variant="outline" className="text-xs">
                                      {metric.displayFields!.length} bileşen
                                    </Badge>
                                  )}
                                </div>
                                {metric.queryDescription && (
                                  <CardDescription>{metric.queryDescription}</CardDescription>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={metric.isActive ? "default" : "secondary"}>
                                  {metric.isActive ? "Aktif" : "Pasif"}
                                </Badge>
                                {hasDisplayFields && (
                                  <CollapsibleTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <BarChart3 className="h-4 w-4" />
                                    </Button>
                                  </CollapsibleTrigger>
                                )}
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div className="flex flex-wrap gap-2">
                              <Badge variant="outline" className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{metricHelpers.getDatabaseTypeDisplayName(metric.databaseType)}</span>
                              </Badge>
                              {metric.metricCategory && (
                                <Badge variant="outline">
                                  {metricHelpers.getCategoryDisplayName(metric.metricCategory)}
                                </Badge>
                              )}
                            </div>

                            {/* SQL Query Preview */}
                            {metric.sqlQuery && (
                              <div className="bg-muted/50 rounded-md p-3">
                                <p className="text-xs text-muted-foreground mb-1">SQL Sorgusu:</p>
                                <code className="text-xs font-mono text-foreground line-clamp-2">
                                  {metric.sqlQuery.length > 100
                                    ? `${metric.sqlQuery.substring(0, 100)}...`
                                    : metric.sqlQuery
                                  }
                                </code>
                              </div>
                            )}

                            <div className="flex items-center justify-between pt-2">
                              <div className="text-sm text-muted-foreground">
                                Oluşturulma: {new Date(metric.createdAt).toLocaleDateString('tr-TR')}
                              </div>
                              <div className="flex items-center space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => router.push(`/metrics/${metric.id}/test`)}
                                >
                                  <TestTube className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => router.push(`/metrics/${metric.id}/edit`)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <Button size="sm" variant="outline">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent>
                                    <DialogHeader>
                                      <DialogTitle>Metriği Sil</DialogTitle>
                                      <DialogDescription>
                                        "{metric.queryName}" metriğini silmek istediğinizden emin misiniz?
                                        Bu işlem geri alınamaz.
                                      </DialogDescription>
                                    </DialogHeader>
                                    <DialogFooter>
                                      <Button variant="outline">İptal</Button>
                                      <Button
                                        onClick={() => handleDelete(metric.id, metric.queryName)}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        Sil
                                      </Button>
                                    </DialogFooter>
                                  </DialogContent>
                                </Dialog>
                              </div>
                            </div>

                            {/* Component Selector - Collapsible Content */}
                            <CollapsibleContent className="pt-4 border-t">
                              <MetricComponentSelector
                                metricQuery={metric}
                                onUpdate={loadMetrics}
                              />
                            </CollapsibleContent>
                          </CardContent>
                        </Collapsible>
                      </Card>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}