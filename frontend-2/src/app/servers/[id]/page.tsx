"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Server, 
  ArrowLeft, 
  Database, 
  Activity, 
  BarChart3, 
  CheckCircle, 
  XCircle,
  Clock,
  Zap,
  TrendingUp,
  AlertTriangle
} from "lucide-react"
import { toast } from "sonner"
import { api } from "@/lib/api"
import { Server as ServerType } from "@/types/server"
import { MetricQuery, MetricDisplayField } from "@/lib/api"
import { ServerMetricsDisplay } from "@/components/servers/server-metrics-display"
import { useAuthToken } from "@/hooks/use-auth-token"

export default function ServerDetailPage() {
  const params = useParams()
  const router = useRouter()
  const serverId = params.id as string

  // Token yönetimi
  const { session, status, isTokenReady, isAuthenticated } = useAuthToken()

  const [server, setServer] = useState<ServerType | null>(null)
  const [metrics, setMetrics] = useState<MetricQuery[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isTestingConnection, setIsTestingConnection] = useState(false)

  useEffect(() => {
    // Token hazır ve authenticated olduğunda veri yükle
    if (serverId && isTokenReady && isAuthenticated) {
      loadServerData()
    } else if (isTokenReady && !isAuthenticated) {
      // Token hazır ama authenticated değil - login'e yönlendir
      router.push('/auth/login')
    }
  }, [serverId, isTokenReady, isAuthenticated, router])

  const loadServerData = async () => {
    try {
      setIsLoading(true)
      const [serverData, allMetrics] = await Promise.all([
        api.servers.getById(serverId),
        api.metrics.getAll()
      ])
      
      setServer(serverData)
      
      // Bu sunucunun veritabanı tipine uygun metrikleri filtrele
      const compatibleMetrics = allMetrics.filter(
        metric => metric.databaseType === serverData.databaseType && metric.isActive
      )
      setMetrics(compatibleMetrics)
      
    } catch (error) {
      console.error('Error loading server data:', error)
      toast.error('Sunucu verileri yüklenirken hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async () => {
    if (!server) return
    
    setIsTestingConnection(true)
    try {
      const testData = {
        name: server.name,
        host: server.host,
        port: server.port,
        databaseType: server.databaseType,
        databaseName: server.databaseName,
        username: server.username,
        password: server.password
      }

      const result = await api.servers.testConnection(testData)
      if (result.success) {
        toast.success(`Bağlantı başarılı! ${result.connectionTime || ''}`)
      } else {
        toast.error(`Bağlantı başarısız: ${result.message}`)
      }
    } catch (error) {
      console.error('Connection test error:', error)
      toast.error('Bağlantı testi sırasında hata oluştu')
    } finally {
      setIsTestingConnection(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Token henüz hazır değilse veya veri yükleniyorsa loading göster
  if (!isTokenReady || isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <Navbar />
          <main className="container mx-auto p-6 space-y-6">
            <div className="space-y-4">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  if (!server) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <Navbar />
          <main className="container mx-auto p-6">
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Sunucu Bulunamadı</h2>
              <p className="text-muted-foreground mb-4">
                Aradığınız sunucu bulunamadı veya erişim izniniz yok.
              </p>
              <Button onClick={() => router.push('/servers')}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Sunucu Listesine Dön
              </Button>
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => router.push('/servers')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Geri
              </Button>
              <div className="flex items-center space-x-2">
                <Server className="h-6 w-6 text-primary" />
                <h1 className="text-3xl font-bold tracking-tight">{server.name}</h1>
                <Badge
                  variant={server.isActive ? "default" : "secondary"}
                  className="gap-1"
                >
                  {server.isActive ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <XCircle className="h-3 w-3" />
                  )}
                  {server.isActive ? "Aktif" : "Pasif"}
                </Badge>
              </div>
            </div>
            <Button 
              onClick={handleTestConnection}
              disabled={isTestingConnection}
              variant="outline"
            >
              <Zap className="mr-2 h-4 w-4" />
              {isTestingConnection ? "Test Ediliyor..." : "Bağlantı Testi"}
            </Button>
          </div>

          {/* Server Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Host</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{server.host}</div>
                <p className="text-xs text-muted-foreground">Port: {server.port}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Veritabanı</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{server.databaseType}</div>
                <p className="text-xs text-muted-foreground">{server.databaseName || "Varsayılan"}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Metrikler</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.length}</div>
                <p className="text-xs text-muted-foreground">Aktif metrik</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Oluşturulma</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold">{formatDate(server.createdAt)}</div>
                <p className="text-xs text-muted-foreground">
                  {server.updatedAt && server.updatedAt !== server.createdAt 
                    ? `Güncellendi: ${formatDate(server.updatedAt)}`
                    : "Güncellenmemiş"
                  }
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="metrics" className="space-y-4">
            <TabsList>
              <TabsTrigger value="metrics" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Metrikler ve Grafikler
              </TabsTrigger>
              <TabsTrigger value="details" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                Sunucu Detayları
              </TabsTrigger>
            </TabsList>

            <TabsContent value="metrics" className="space-y-4">
              <ServerMetricsDisplay 
                server={server}
                metrics={metrics}
                onRefresh={loadServerData}
              />
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Sunucu Bilgileri</CardTitle>
                  <CardDescription>
                    Sunucu konfigürasyon detayları
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Sunucu Adı</label>
                      <p className="text-sm">{server.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Host Adresi</label>
                      <p className="text-sm">{server.host}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Port</label>
                      <p className="text-sm">{server.port}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Veritabanı Tipi</label>
                      <p className="text-sm">{server.databaseType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Veritabanı Adı</label>
                      <p className="text-sm">{server.databaseName || "Belirtilmemiş"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Kullanıcı Adı</label>
                      <p className="text-sm">{server.username}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Durum</label>
                      <p className="text-sm">
                        <Badge variant={server.isActive ? "default" : "secondary"}>
                          {server.isActive ? "Aktif" : "Pasif"}
                        </Badge>
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Açıklama</label>
                      <p className="text-sm">{server.description || "Açıklama bulunmuyor"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
