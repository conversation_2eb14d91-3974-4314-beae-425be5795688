"use client"

import { useState } from "react"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Server } from "lucide-react"
import { AddServerDialog } from "@/components/servers/add-server-dialog"
import { ServersTable } from "@/components/servers/servers-table"

export default function ServersPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleServerAdded = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Server className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON>esi</h1>
            </div>
            <AddServerDialog onServerAdded={handleServerAdded} />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Sunucu Yönetimi</CardTitle>
              <CardDescription>
                Veritabanı sunucularını görüntüleyin ve yönetin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ServersTable refreshTrigger={refreshTrigger} />
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
