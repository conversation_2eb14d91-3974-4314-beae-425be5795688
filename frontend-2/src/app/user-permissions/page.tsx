"use client"

import { useState } from "react"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield } from "lucide-react"
import { AddUserPermissionDialog } from "@/components/user-permissions/add-user-permission-dialog"
import { UserPermissionsTable } from "@/components/user-permissions/user-permissions-table"

export default function UserPermissionsPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handlePermissionAdded = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold tracking-tight">Kullanıcı Yetkileri</h1>
            </div>
            <AddUserPermissionDialog onPermissionAdded={handlePermissionAdded} />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Kullanıcı Yetki Listesi</CardTitle>
              <CardDescription>
                Kullanıcılara verilen veritabanı yetkilerini görüntüleyin ve yönetin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserPermissionsTable refreshTrigger={refreshTrigger} />
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
