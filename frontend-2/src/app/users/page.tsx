"use client"

import { useState } from "react"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users } from "lucide-react"
import { AddUserDialog } from "@/components/users/add-user-dialog"
import { UsersTable } from "@/components/users/users-table"

export default function UsersPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleUserAdded = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON><PERSON><PERSON>lar</h1>
            </div>
            <AddUserDialog onUserAdded={handleUserAdded} />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Kullanıcı Listesi</CardTitle>
              <CardDescription>
                Sistemdeki tüm kullanıcıları görüntüleyin ve yönetin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UsersTable refreshTrigger={refreshTrigger} />
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
