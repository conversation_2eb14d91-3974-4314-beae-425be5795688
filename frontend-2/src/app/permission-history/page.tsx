"use client"

import { useState } from "react"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { History } from "lucide-react"
import { PermissionHistoryTable } from "@/components/permission-history/permission-history-table"

export default function PermissionHistoryPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <History className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON></h1>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Yetki İşlem Geçmişi</CardTitle>
              <CardDescription>
                Kullanıcılara verilen ve iptal edilen yetkilerin tarihçesini görüntüleyin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PermissionHistoryTable refreshTrigger={refreshTrigger} />
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
