"use client"

import { useQuery } from "@tanstack/react-query"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Navbar } from "@/components/navbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Loader2, Clock, User, Database, Shield, CheckCircle, XCircle, AlertTriangle, ArrowLeft, History } from "lucide-react"
import { api } from "@/lib/api"

interface PermissionHistory {
  id: string
  userId: string
  firstName: string
  lastName: string
  email: string
  serverId: string
  serverName: string
  databaseType: string
  roleName: string
  roleDescription: string
  sqlQuery?: string
  grantedAt: string
  expiresAt: string
  isActive: boolean
  action: string // "GRANTED", "REVOKED", "EXPIRED", "ACTIVE"
  actionDate: string
  createdAt: string
  updatedAt: string
}

export default function PermissionHistoryDetailPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params.userId as string
  const serverId = params.serverId as string

  const {
    data: permissionHistory = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["permission-history-detail", userId, serverId],
    queryFn: () => api.userPermissions.getHistoryByUserAndServer(userId, serverId),
    enabled: !!userId && !!serverId,
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'ACTIVE':
        return (
          <Badge variant="default" className="gap-1 bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3" />
            Aktif
          </Badge>
        )
      case 'GRANTED':
        return (
          <Badge variant="default" className="gap-1 bg-blue-100 text-blue-800 border-blue-200">
            <Shield className="h-3 w-3" />
            Verildi
          </Badge>
        )
      case 'REVOKED':
        return (
          <Badge variant="destructive" className="gap-1">
            <XCircle className="h-3 w-3" />
            İptal Edildi
          </Badge>
        )
      case 'EXPIRED':
        return (
          <Badge variant="secondary" className="gap-1 bg-orange-100 text-orange-800 border-orange-200">
            <AlertTriangle className="h-3 w-3" />
            Süresi Doldu
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {action}
          </Badge>
        )
    }
  }

  const getDatabaseTypeBadge = (databaseType: string) => {
    const colors = {
      'postgresql': 'bg-blue-100 text-blue-800 border-blue-200',
      'mysql': 'bg-orange-100 text-orange-800 border-orange-200',
      'oracle': 'bg-red-100 text-red-800 border-red-200',
      'sqlserver': 'bg-purple-100 text-purple-800 border-purple-200',
    }
    
    const colorClass = colors[databaseType?.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
    
    return (
      <Badge variant="outline" className={`gap-1 ${colorClass}`}>
        <Database className="h-3 w-3" />
        {databaseType?.toUpperCase() || 'UNKNOWN'}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <Navbar />
          <main className="container mx-auto p-6">
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Yetki detayları yükleniyor...</span>
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  if (error) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <Navbar />
          <main className="container mx-auto p-6">
            <div className="text-center py-8 text-red-500">
              Yetki detayları yüklenirken bir hata oluştu
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  const userInfo = permissionHistory[0]

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Navbar />
        <main className="container mx-auto p-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Geri
              </Button>
              <div className="flex items-center space-x-2">
                <History className="h-6 w-6 text-primary" />
                <h1 className="text-3xl font-bold tracking-tight">Yetki Detayları</h1>
              </div>
            </div>
          </div>

          {userInfo && (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <User className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">
                        {userInfo.firstName} {userInfo.lastName}
                      </CardTitle>
                      <CardDescription>
                        {userInfo.email} - {userInfo.serverName || 'Bilinmeyen Sunucu'}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    {getDatabaseTypeBadge(userInfo.databaseType)}
                  </div>
                </div>
              </CardHeader>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Yetki Geçmişi</CardTitle>
              <CardDescription>
                Bu kullanıcının seçili sunucudaki tüm yetki işlemleri
              </CardDescription>
            </CardHeader>
            <CardContent>
              {permissionHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Bu kullanıcı için yetki geçmişi bulunmuyor
                </div>
              ) : (
                <div className="space-y-4">
                  {permissionHistory.map((history: PermissionHistory) => (
                    <Card key={`${history.id}-${history.actionDate}`} className="relative">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-3 flex-1">
                            <div className="flex items-center space-x-2">
                              <Shield className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">Yetki:</span>
                              <Badge variant="outline" className="text-xs">
                                {history.roleName}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">Verildi:</span>
                                <span className="text-muted-foreground">
                                  {formatDate(history.grantedAt)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">Bitiş:</span>
                                <span className="text-muted-foreground">
                                  {formatDate(history.expiresAt)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">İşlem:</span>
                                <span className="text-muted-foreground">
                                  {formatDate(history.actionDate)}
                                </span>
                              </div>
                            </div>

                            {/* SQL Query */}
                            {history.sqlQuery && (
                              <div className="mt-4 p-3 bg-muted/50 rounded-md">
                                <p className="text-xs text-muted-foreground mb-2 font-medium">Uygulanan SQL Sorgusu:</p>
                                <code className="text-xs font-mono text-foreground block bg-background p-2 rounded border">
                                  {history.sqlQuery}
                                </code>
                              </div>
                            )}
                          </div>

                          <div className="ml-4">
                            {getActionBadge(history.action)}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
