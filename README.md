# DB Auth v2 - Database Authentication & User Management System

Modern, scalable database authentication and user management system built with Spring Boot microservices and Next.js frontend.

## 🏗️ Architecture

### Backend (Java 17 + Spring Boot 3.2)
- **Onion Architecture** with clean separation of concerns
- **Multi-module Maven** structure
- **PostgreSQL** database with Flyway migrations
- **Microservices** architecture

#### Modules:
- **Gateway** (Port 8080) - API Gateway and single entry point
- **User Management** (Port 8081) - User CRUD operations
- **Server Management** (Port 8082) - Database server monitoring
- **Shared Resources** - Common utilities and configurations

### Frontend (Next.js 14)
- **App Router** with TypeScript
- **ShadCN UI** components
- **TailwindCSS** for styling
- **Framer Motion** for animations
- **Recharts** for data visualization

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL (via Docker)

### 1. Configure Environment
```bash
# Backend configuration is in application.yml files
# Frontend configuration
cd frontend
cp .env.example .env.local
# Edit .env.local with your settings

# Docker configuration
cd "Docker Compose"
cp .env.example .env  # Optional: customize Docker settings
```

### 2. Start Database
```bash
cd "Docker Compose"
./start.bat  # Windows
# or
docker compose up -d
```

### 3. Start Backend Services
```bash
cd backend

# Start Gateway (includes User Management)
cd gateway
mvn spring-boot:run

# Start Server Management
cd ../server-management
mvn spring-boot:run
```

### 4. Start Frontend
```bash
cd frontend
npm install
npm run dev
```

### 5. Access Applications
- **Frontend Dashboard**: http://localhost:3000
- **Gateway API**: http://localhost:8080/api
- **Server Management API**: http://localhost:8082/api

## 📁 Project Structure

```
db-auth-v2/
├── backend/
│   ├── gateway/                 # API Gateway (8080)
│   ├── user-management/         # User & Permission Management (8081)
│   ├── server-management/       # Server Monitoring (8082)
│   ├── shared-resources/        # Common utilities
│   └── logs/                    # Centralized logging
├── frontend/                    # Next.js Dashboard
├── Docker Compose/              # Database setup
└── README.md
```

## ⚙️ Configuration

### Dynamic Configuration
All static values have been externalized to configuration files for better maintainability:

#### Backend Configuration
- **Metric Collection**: `application.yml` in server-management module
  - Collection intervals, timeouts, cleanup schedules
  - Database connection parameters
  - Query execution limits
- **Scheduled Tasks**: Configurable cron expressions
- **HTTP Timeouts**: RestTemplate timeout configurations

#### Frontend Configuration
- **Environment Variables**: `.env.local` file
  - API endpoints, polling intervals
  - Authentication tokens
  - Toast durations
- **Dynamic API URLs**: Support for different environments

#### Docker Configuration
- **Environment Variables**: `.env` file in Docker Compose directory
  - Database ports, health check intervals
  - Container names, network settings
  - Volume configurations

## 🔧 Features

### User Management
- ✅ User CRUD operations

### Server Monitoring
- ✅ Database server management
- ✅ Real-time metrics collection
- ✅ Performance monitoring
- ✅ Health checks

### Frontend Dashboard
- ✅ Modern responsive design
- ✅ Real-time data updates
- ✅ User-friendly interface
- ✅ Mobile responsive

## 🛠️ Development

### Code Quality Standards
- **Clean Code** principles
- **SOLID** design patterns
- **Constructor injection** for dependencies
- **Comprehensive logging** with SLF4J + Logback
- **Integration tests** with PostgreSQL TestContainers
- **Dynamic configuration** - No hardcoded values

### Database
- **PostgreSQL** as primary database
- **Flyway** for schema migrations
- **Connection pooling** with HikariCP
- **Transaction management**

## 📊 Monitoring & Logging

- **Centralized logging** in `backend/logs/`
- **Structured logging** with server identification
- **Health checks** via Spring Actuator
- **Metrics collection** for performance monitoring

## 🔒 Security

- **Input validation** with Bean Validation
- **SQL injection** prevention with JPA
- **CORS** configuration for frontend integration
- **Error handling** with proper HTTP status codes

## 📝 API Documentation

### User Management Endpoints
- `GET /api/users` - List all users
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Permission Management
- `GET /api/user-permissions` - List user permissions
- `POST /api/user-permissions` - Assign permission
- `DELETE /api/user-permissions/{id}` - Remove permission

### Server Management
- `GET /api/servers` - List servers
- `POST /api/servers` - Add server
- `GET /api/servers/{id}/metrics` - Get server metrics

## 🧪 Testing

```bash
# Backend tests
cd backend
mvn test

# Frontend tests
cd frontend
npm test
```

## 📦 Deployment

### Docker Deployment
```bash
# Build and deploy all services
docker compose -f docker-compose.prod.yml up -d
```

### Manual Deployment
1. Build backend JARs: `mvn clean package`
2. Build frontend: `npm run build`
3. Deploy to target environment

## 🤝 Contributing

1. Follow clean code principles
2. Write comprehensive tests
3. Update documentation
4. Use conventional commit messages

## 📄 License

This project is licensed under the MIT License.
